<?php
/**
 * api/admin/send_manual_notification.php
 * API для ручной отправки уведомлений
 */

// ИСПРАВЛЕНИЕ: Полностью отключаем вывод ошибок для чистого JSON
ini_set('display_errors', 0);
ini_set('log_errors', 0);
error_reporting(0);

// ИСПРАВЛЕНИЕ: Буферизация вывода для предотвращения нежелательного вывода
ob_start();

header('Content-Type: application/json');

try {
    require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    ob_clean(); // ИСПРАВЛЕНИЕ: Очищаем буфер перед JSON выводом
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit;
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions.php';
require_once __DIR__ . '/../db_mock.php'; // ИСПРАВЛЕНИЕ: Подключаем db_mock.php для функции loadUserData()

// ИСПРАВЛЕНИЕ: config.php уже подключает bot_config_loader.php и вызывает defineBotConstants()
// Дублирование убрано для предотвращения конфликтов

function sendNotification($chatId, $message, $botToken) {
    $url = "https://api.telegram.org/bot{$botToken}/sendMessage";
    $data = [
        'chat_id' => $chatId,
        'text' => $message,
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => true,
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data),
            'timeout' => 5
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    return json_decode($result, true);
}

$response = ['success' => false, 'error' => 'Invalid request'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $recipientType = $_POST['recipient_type'] ?? 'all';
    $message = $_POST['message'] ?? '';
    $userSearch = $_POST['user_search'] ?? '';

    if (empty($message)) {
        $response['error'] = 'Message is empty';
        ob_clean(); // ИСПРАВЛЕНИЕ: Очищаем буфер перед JSON выводом
        echo json_encode($response);
        exit;
    }

    $userData = loadUserData();
    if (!$userData) {
        $response['error'] = 'Could not load user data';
        ob_clean(); // ИСПРАВЛЕНИЕ: Очищаем буфер перед JSON выводом
        echo json_encode($response);
        exit;
    }

    $botToken = defined('TELEGRAM_BOT_TOKEN') ? TELEGRAM_BOT_TOKEN : '';
    if (empty($botToken)) {
        $response['error'] = 'Telegram bot token is not configured';
        ob_clean(); // ИСПРАВЛЕНИЕ: Очищаем буфер перед JSON выводом
        echo json_encode($response);
        exit;
    }

    $sentCount = 0;
    $failedCount = 0;

    if ($recipientType === 'all') {
        foreach ($userData as $userId => $user) {
            $telegramId = $user['telegram_data']['id'] ?? $user['id'] ?? null;
            if ($telegramId) {
                $personalizedMessage = str_replace(
                    ['{first_name}', '{last_name}', '{username}'],
                    [
                        $user['first_name'] ?? 'User',
                        $user['last_name'] ?? '',
                        isset($user['username']) ? '@' . $user['username'] : ''
                    ],
                    $message
                );
                $res = sendNotification($telegramId, $personalizedMessage, $botToken);
                if ($res && $res['ok']) {
                    $sentCount++;
                } else {
                    $failedCount++;
                }
                usleep(50000); // 50ms delay
            }
        }
        $response = ['success' => true, 'message' => "Sent to {$sentCount} users, {$failedCount} failed."];

    } elseif ($recipientType === 'single') {
        if (empty($userSearch)) {
            $response['error'] = 'User ID or Username is required';
            ob_clean(); // ИСПРАВЛЕНИЕ: Очищаем буфер перед JSON выводом
            echo json_encode($response);
            exit;
        }

        $foundUser = null;
        foreach ($userData as $userId => $user) {
            if ($userId == $userSearch || (isset($user['username']) && $user['username'] == $userSearch)) {
                $foundUser = $user;
                break;
            }
        }

        if ($foundUser) {
            $telegramId = $foundUser['telegram_data']['id'] ?? $foundUser['id'] ?? null;
            if ($telegramId) {
                $personalizedMessage = str_replace(
                    ['{first_name}', '{last_name}', '{username}'],
                    [
                        $foundUser['first_name'] ?? 'User',
                        $foundUser['last_name'] ?? '',
                        isset($foundUser['username']) ? '@' . $foundUser['username'] : ''
                    ],
                    $message
                );
                $res = sendNotification($telegramId, $personalizedMessage, $botToken);
                if ($res && $res['ok']) {
                    $response = ['success' => true, 'message' => 'Message sent successfully.'];
                } else {
                    $response['error'] = 'Failed to send message: ' . ($res['description'] ?? 'Unknown error');
                }
            } else {
                $response['error'] = 'User does not have a Telegram ID';
            }
        } else {
            $response['error'] = 'User not found';
        }
    }
}

// ИСПРАВЛЕНИЕ: Очищаем буфер перед финальным JSON выводом
ob_clean();
echo json_encode($response);

} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'System error']);
} catch (Error $e) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'System error']);
}
