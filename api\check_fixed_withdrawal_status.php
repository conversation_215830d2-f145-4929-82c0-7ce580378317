<?php
/**
 * Проверка статуса исправленной выплаты
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

$payoutId = '5003353973'; // ID исправленной выплаты
$userId = '7947418432';   // ID пользователя Qulisa

echo "🔍 ПРОВЕРКА СТАТУСА ИСПРАВЛЕННОЙ ВЫПЛАТЫ\n\n";
echo "Payout ID: {$payoutId}\n";
echo "User ID: {$userId}\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "📡 Запрашиваем статус из NOWPayments API...\n";

try {
    $status = $api->getPayoutStatus($payoutId);
    
    if ($status) {
        if (is_array($status)) {
            echo "✅ Статус получен: " . json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
            $statusString = $status['status'] ?? 'unknown';
        } else {
            echo "✅ Статус получен: {$status}\n\n";
            $statusString = $status;
        }

        // Проверяем, изменился ли статус
        if ($statusString !== 'waiting' && $statusString !== 'unknown') {
            echo "🎉 Статус изменился! Выплата обрабатывается.\n";
        } else {
            echo "⏳ Выплата еще в обработке.\n";
        }
        
    } else {
        echo "❌ Не удалось получить статус выплаты\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при получении статуса: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

// Также проверим данные в файле пользователя
echo "📁 ПРОВЕРКА ДАННЫХ В ФАЙЛЕ ПОЛЬЗОВАТЕЛЯ:\n\n";

require_once 'functions.php';
$userData = loadUserData();

if (isset($userData[$userId]['withdrawals'])) {
    foreach ($userData[$userId]['withdrawals'] as $withdrawal) {
        if ($withdrawal['payout_id'] === $payoutId) {
            echo "✅ Выплата найдена в файле:\n";
            echo "   ID: {$withdrawal['id']}\n";
            echo "   Payout ID: {$withdrawal['payout_id']}\n";
            echo "   Статус: {$withdrawal['status']}\n";
            echo "   Сумма: {$withdrawal['coins_amount']} монет\n";
            echo "   Валюта: {$withdrawal['currency']}\n";
            echo "   Создана: {$withdrawal['created_at']}\n";
            echo "   Обновлена: {$withdrawal['updated_at']}\n";
            
            if (isset($withdrawal['nowpayments_created'])) {
                echo "   ✅ Отмечена как созданная в NOWPayments\n";
            }
            
            if (isset($withdrawal['original_manual_id'])) {
                echo "   📝 Оригинальный ID: {$withdrawal['original_manual_id']}\n";
            }
            
            break;
        }
    }
} else {
    echo "❌ Выплаты пользователя не найдены\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Проверка завершена.\n";
?>
