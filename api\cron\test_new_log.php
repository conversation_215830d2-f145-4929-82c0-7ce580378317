<?php
// Создаем новый лог файл для тестирования
$logFile = __DIR__ . '/test_notifications.log';
$timestamp = date('Y-m-d H:i:s');

echo "Записываем в файл: $logFile\n";

$result = file_put_contents($logFile, "[$timestamp] НОВЫЙ ТЕСТ ЗАПИСИ В ЛОГ\n", FILE_APPEND | LOCK_EX);
echo "Результат записи: " . ($result !== false ? "Успешно ($result байт)" : "Ошибка") . "\n";

// Проверяем, что файл создался
if (file_exists($logFile)) {
    echo "Файл создан успешно\n";
    echo "Содержимое файла:\n";
    echo file_get_contents($logFile);
} else {
    echo "ОШИБКА: Файл не создан\n";
}

// Теперь попробуем подключить основной скрипт уведомлений
echo "\nПопытка подключения send_notifications.php...\n";
try {
    // Переопределяем функцию logMessage для записи в наш тестовый лог
    function logMessage($message) {
        global $logFile;
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
        echo "[$timestamp] $message\n";
    }
    
    // Подключаем config.php
    logMessage("Подключаем config.php");
    require_once __DIR__ . '/../config.php';
    logMessage("config.php подключен");
    
    // Подключаем functions.php
    logMessage("Подключаем functions.php");
    require_once __DIR__ . '/../functions.php';
    logMessage("functions.php подключен");
    
    logMessage("ВСЕ ПОДКЛЮЧЕНИЯ УСПЕШНЫ");
    
} catch (Exception $e) {
    $error = "ОШИБКА: " . $e->getMessage();
    file_put_contents($logFile, "[$timestamp] $error\n", FILE_APPEND | LOCK_EX);
    echo "$error\n";
}
?>
