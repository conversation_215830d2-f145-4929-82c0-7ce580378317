<?php
/**
 * api/admin/migrate_support_chats.php
 * Скрипт для миграции существующих чатов поддержки (добавление статуса)
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

session_start();

// Проверяем аутентификацию
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/support_data.php';

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json; charset=utf-8');

try {
    // Получаем все чаты
    $chats = getSupportChats();
    $migratedCount = 0;
    $totalCount = count($chats);
    
    // Проходим по всем чатам и добавляем статус, если его нет
    foreach ($chats as $chatId => &$chat) {
        if (!isset($chat['status'])) {
            $chat['status'] = 'open'; // По умолчанию все чаты открыты
            $migratedCount++;
        }
    }
    
    // Сохраняем обновленные данные
    if ($migratedCount > 0) {
        $result = saveSupportChats($chats);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Migration completed successfully',
                'total_chats' => $totalCount,
                'migrated_chats' => $migratedCount,
                'details' => "Обновлено {$migratedCount} из {$totalCount} чатов"
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Failed to save migrated data'
            ]);
        }
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'No migration needed',
            'total_chats' => $totalCount,
            'migrated_chats' => 0,
            'details' => "Все {$totalCount} чатов уже имеют статус"
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Migration error: ' . $e->getMessage()
    ]);
}
?>
