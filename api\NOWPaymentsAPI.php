<?php
/**
 * NOWPaymentsAPI.php
 * Класс для работы с NOWPayments API
 */

require_once __DIR__ . '/RateLimiter.php';

class NOWPaymentsAPI {
    private $apiKey;
    private $publicKey;
    private $ipnSecret;
    private $apiUrl;
    private $jwtToken;
    private $rateLimiter;

    public function __construct($apiKey, $publicKey, $ipnSecret, $apiUrl) {
        $this->apiKey = $apiKey;        // Приватный ключ для payout
        $this->publicKey = $publicKey;  // Публичный ключ для estimate
        $this->ipnSecret = $ipnSecret;
        $this->apiUrl = $apiUrl;
        $this->jwtToken = null;
        $this->rateLimiter = new RateLimiter();
    }

    /**
     * Получить JWT токен для авторизации через email и password
     */
    public function getJWTToken($email = null, $password = null) {
        if ($this->jwtToken) {
            return $this->jwtToken;
        }

        // Используем переданные данные или константы из конфига
        $authEmail = $email ?: (defined('NOWPAYMENTS_EMAIL') ? NOWPAYMENTS_EMAIL : null);
        $authPassword = $password ?: (defined('NOWPAYMENTS_PASSWORD') ? NOWPAYMENTS_PASSWORD : null);

        if (!$authEmail || !$authPassword) {
            error_log("NOWPaymentsAPI ERROR: Email и password не указаны для получения JWT токена");
            return false;
        }

        $url = $this->apiUrl . '/auth';
        $data = [
            'email' => $authEmail,
            'password' => $authPassword
        ];

        error_log("NOWPaymentsAPI INFO: Попытка получения JWT токена для email: " . $authEmail);

        // Для получения JWT токена не нужна авторизация
        $response = $this->makeRequestWithoutAuth('POST', $url, $data);

        if ($response && isset($response['token'])) {
            $this->jwtToken = $response['token'];
            error_log("NOWPaymentsAPI SUCCESS: JWT токен получен успешно");
            return $this->jwtToken;
        }

        error_log("NOWPaymentsAPI ERROR: Не удалось получить JWT токен");
        if ($response) {
            error_log("NOWPaymentsAPI ERROR: Ответ сервера: " . json_encode($response));
        }
        return false;
    }

    /**
     * Получить оценку суммы для конвертации
     */
    public function getEstimateAmount($amount, $fromCurrency, $toCurrency) {
        $url = $this->apiUrl . '/estimate';
        $params = [
            'amount' => $amount,
            'currency_from' => $fromCurrency,
            'currency_to' => $toCurrency
        ];

        // Для estimate пробуем оба ключа
        $response = $this->makeRequest('GET', $url . '?' . http_build_query($params), null, 'x-api-key', false);

        if ($response === false) {
            // Если приватный ключ не сработал, пробуем публичный
            $response = $this->makeRequest('GET', $url . '?' . http_build_query($params), null, 'x-api-key', true);
        }

        if ($response === false) {
            error_log("NOWPaymentsAPI ERROR: Не удалось получить оценку суммы");
            return false;
        }

        return $response;
    }

    /**
     * Создать массовую выплату с автоматическим переключением методов авторизации
     */
    public function createMassWithdrawal($withdrawals) {
        // Пробуем разные endpoints и методы авторизации
        $endpoints = [
            ['url' => '/mass-payouts', 'data_key' => 'payouts'],
            ['url' => '/payout', 'data_key' => 'withdrawals']
        ];

        $authMethods = ['both', 'bearer', 'x-api-key'];

        foreach ($endpoints as $endpoint) {
            $url = $this->apiUrl . $endpoint['url'];
            $data = [$endpoint['data_key'] => $withdrawals];

            error_log("NOWPaymentsAPI INFO: Пробуем endpoint: " . $url);
            error_log("NOWPaymentsAPI INFO: Данные запроса: " . json_encode($data));

            foreach ($authMethods as $authMethod) {
                error_log("NOWPaymentsAPI INFO: Пробуем метод авторизации: " . $authMethod);

                $response = $this->makeRequest('POST', $url, $data, $authMethod);

                if ($response !== false) {
                    error_log("NOWPaymentsAPI SUCCESS: Массовая выплата создана с endpoint: " . $url . " и методом авторизации: " . $authMethod);
                    error_log("NOWPaymentsAPI INFO: Ответ API: " . json_encode($response));
                    return $response;
                }

                error_log("NOWPaymentsAPI INFO: Комбинация " . $url . " + " . $authMethod . " не сработала");
            }
        }

        error_log("NOWPaymentsAPI ERROR: Все комбинации endpoints и методов авторизации не сработали");
        return false;
    }

    /**
     * Создать одиночную выплату с автоматическим переключением методов авторизации
     */
    public function createSinglePayout($address, $currency, $amount) {
        // Используем новую функцию с обработкой комиссий
        return $this->createPayoutWithFeeHandling($address, $currency, $amount);
    }

    /**
     * Выполнить HTTP запрос к API
     */
    private function makeRequest($method, $url, $data = null, $authType = 'bearer', $usePublicKey = false) {
        // ИСПРАВЛЕНИЕ: Проверяем лимиты запросов перед отправкой
        $cacheKey = md5($method . $url . serialize($data));

        // Пробуем получить из кэша для GET запросов
        if ($method === 'GET') {
            $cachedResult = $this->rateLimiter->getFromCache($cacheKey);
            if ($cachedResult !== null) {
                error_log("NOWPaymentsAPI CACHE: Используем кэшированный результат для {$method} {$url}");
                return $cachedResult;
            }
        }

        // Проверяем лимиты
        if (!$this->rateLimiter->canMakeRequest($url)) {
            $waitTime = $this->rateLimiter->getWaitTime();
            error_log("NOWPaymentsAPI RATE_LIMIT: Превышен лимит запросов. Ожидание: {$waitTime} сек");

            if ($waitTime > 0 && $waitTime <= 60) {
                sleep($waitTime);
            } else {
                return [
                    'error' => true,
                    'code' => 'RATE_LIMIT_EXCEEDED',
                    'message' => 'Превышен лимит запросов к API. Попробуйте позже.',
                    'wait_time' => $waitTime
                ];
            }
        }

        // Выбираем правильный ключ
        $keyToUse = $usePublicKey ? $this->publicKey : $this->apiKey;

        if ($authType === 'x-api-key') {
            $headers = [
                'x-api-key: ' . $keyToUse,
                'Content-Type: application/json'
            ];
        } elseif ($authType === 'both') {
            // Для payout API нужны оба заголовка с реальным JWT токеном
            $jwtToken = $this->getJWTToken();
            if ($jwtToken) {
                $headers = [
                    'x-api-key: ' . $keyToUse,
                    'Authorization: Bearer ' . $jwtToken,
                    'Content-Type: application/json'
                ];
            } else {
                // Если не удалось получить JWT, используем API ключ как токен
                $headers = [
                    'x-api-key: ' . $keyToUse,
                    'Authorization: Bearer ' . $keyToUse,
                    'Content-Type: application/json'
                ];
            }
        } else {
            // Для bearer пробуем сначала JWT токен, потом API ключ
            $jwtToken = $this->getJWTToken();
            $tokenToUse = $jwtToken ?: $keyToUse;
            $headers = [
                'Authorization: Bearer ' . $tokenToUse,
                'Content-Type: application/json'
            ];
        }

        error_log("NOWPaymentsAPI INFO: Отправка запроса {$method} {$url} с заголовками: " . json_encode($headers));

        // Регистрируем запрос в rate limiter
        $this->rateLimiter->registerRequest($url);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Отключаем проверку SSL для разработки
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("NOWPaymentsAPI CURL ERROR: " . $error);
            return false;
        }

        if ($httpCode !== 200) {
            error_log("NOWPaymentsAPI HTTP INFO: Code {$httpCode}, Response: {$response}");

            // Парсим ошибку для детального сообщения
            $errorData = json_decode($response, true);
            if ($errorData) {
                return [
                    'error' => true,
                    'http_code' => $httpCode,
                    'message' => $errorData['message'] ?? 'Unknown error',
                    'code' => $errorData['code'] ?? 'UNKNOWN',
                    'details' => $errorData
                ];
            }

            return false;
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("NOWPaymentsAPI JSON ERROR: " . json_last_error_msg());
            return false;
        }

        // Кэшируем успешные GET запросы
        if ($method === 'GET' && $decodedResponse && !isset($decodedResponse['error'])) {
            $this->rateLimiter->saveToCache($cacheKey, $decodedResponse);
        }

        return $decodedResponse;
    }

    /**
     * Проверить статус выплаты с автоматическим переключением методов авторизации
     */
    public function getPayoutStatus($payoutId) {
        error_log("NOWPaymentsAPI INFO: Проверка статуса выплаты {$payoutId}");

        $url = $this->apiUrl . '/payout/' . $payoutId;
        $authMethods = ['x-api-key', 'bearer', 'both'];

        foreach ($authMethods as $authMethod) {
            error_log("NOWPaymentsAPI DEBUG: Пробуем метод авторизации: {$authMethod}");

            $response = $this->makeRequest('GET', $url, null, $authMethod);

            if ($response !== false) {
                error_log("NOWPaymentsAPI DEBUG: Получен ответ: " . json_encode($response));

                // NOWPayments возвращает данные в формате:
                // {"id": "...", "withdrawals": [{"status": "FINISHED", ...}]}
                if (isset($response['withdrawals']) && is_array($response['withdrawals']) && count($response['withdrawals']) > 0) {
                    $withdrawal = $response['withdrawals'][0];
                    if (isset($withdrawal['status'])) {
                        $status = strtolower($withdrawal['status']);
                        error_log("NOWPaymentsAPI INFO: Статус выплаты {$payoutId}: {$status}");

                        // Возвращаем нормализованный ответ
                        return [
                            'status' => $status,
                            'amount' => $withdrawal['amount'] ?? null,
                            'fee' => $withdrawal['fee'] ?? null,
                            'hash' => $withdrawal['hash'] ?? null,
                            'currency' => $withdrawal['currency'] ?? null,
                            'address' => $withdrawal['address'] ?? null,
                            'updated_at' => $withdrawal['updated_at'] ?? null,
                            'error' => $withdrawal['error'] ?? null,
                            'raw_response' => $response
                        ];
                    }
                }

                // Проверяем старые форматы для совместимости
                if (isset($response['status'])) {
                    error_log("NOWPaymentsAPI INFO: Статус выплаты {$payoutId} (прямой): {$response['status']}");
                    return $response;
                } elseif (isset($response['data']) && isset($response['data']['status'])) {
                    error_log("NOWPaymentsAPI INFO: Статус выплаты {$payoutId} (в data): {$response['data']['status']}");
                    return $response['data'];
                } elseif (!isset($response['error'])) {
                    error_log("NOWPaymentsAPI WARNING: Неожиданная структура ответа. Доступные поля: " . implode(', ', array_keys($response)));
                    return $response;
                } else {
                    error_log("NOWPaymentsAPI WARNING: Ошибка в ответе: " . ($response['error'] ?? 'неизвестная ошибка'));
                }
            } else {
                error_log("NOWPaymentsAPI WARNING: Пустой ответ для метода {$authMethod}");
            }
        }

        error_log("NOWPaymentsAPI ERROR: Не удалось получить статус выплаты {$payoutId} ни одним методом");
        return false;
    }

    /**
     * Альтернативный метод проверки статуса (для совместимости)
     */
    public function checkPayoutStatus($payoutId) {
        return $this->getPayoutStatus($payoutId);
    }

    /**
     * Получить список доступных валют
     */
    public function getAvailableCurrencies() {
        $url = $this->apiUrl . '/currencies';
        return $this->makeRequest('GET', $url, null, 'x-api-key');
    }

    /**
     * Получить калькулятор цены (estimate)
     * Документация: https://documenter.getpostman.com/view/7907941/2s93JusNJt#4461aa48-10ca-4032-803c-ed460b19fec0
     */
    public function getPriceCalculator($amount, $fromCurrency, $toCurrency) {
        return $this->getEstimateAmount($amount, $fromCurrency, $toCurrency);
    }

    /**
     * Проверить баланс аккаунта с автоматическим переключением методов авторизации
     */
    public function getAccountBalance() {
        $url = $this->apiUrl . '/balance';

        $authMethods = ['x-api-key', 'both', 'bearer'];

        foreach ($authMethods as $authMethod) {
            $response = $this->makeRequest('GET', $url, null, $authMethod);
            if ($response !== false) {
                return $response;
            }
        }

        return false;
    }

    /**
     * Найти валюту с достаточным балансом для тестовой выплаты
     */
    public function findCurrencyWithBalance($minAmount = 0.00001) {
        $balance = $this->getAccountBalance();

        if (!$balance) {
            return false;
        }

        error_log("NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами");
        error_log("NOWPaymentsAPI INFO: Минимальная сумма: " . $minAmount);

        foreach ($balance as $currency => $data) {
            if (isset($data['amount']) && $data['amount'] > $minAmount) {
                error_log("NOWPaymentsAPI INFO: Найдена валюта {$currency} с балансом {$data['amount']}");
                return [
                    'currency' => $currency,
                    'balance' => $data['amount'],
                    'available' => $data['amount'] - ($data['pendingAmount'] ?? 0)
                ];
            }
        }

        error_log("NOWPaymentsAPI WARNING: Не найдено валют с достаточным балансом");
        return false;
    }

    /**
     * Создать выплату с полной автоконвертацией из доступного баланса
     */
    public function createPayoutWithInternalConversion($targetAddress, $targetCurrency, $targetAmount) {
        error_log("NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией");
        error_log("NOWPaymentsAPI INFO: Цель - {$targetAmount} {$targetCurrency} на адрес {$targetAddress}");

        // БЕЗОПАСНОСТЬ: Проверяем, что адрес не является тестовым
        if ($this->isTestAddress($targetAddress)) {
            error_log("NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: {$targetAddress}");
            return [
                'error' => true,
                'code' => 'TEST_ADDRESS_BLOCKED',
                'message' => "Выплаты на тестовые адреса запрещены",
                'details' => [
                    'address' => $targetAddress,
                    'reason' => 'Адрес является тестовым или служебным',
                    'suggestion' => 'Используйте реальный пользовательский адрес'
                ]
            ];
        }

        // Сначала проверяем совместимость адреса с запрашиваемой валютой
        if (!$this->isAddressCompatible($targetAddress, $targetCurrency)) {
            error_log("NOWPaymentsAPI ERROR: Адрес {$targetAddress} несовместим с валютой {$targetCurrency}");
            return [
                'error' => true,
                'code' => 'INCOMPATIBLE_ADDRESS',
                'message' => "Ваш адрес {$targetAddress} не подходит для валюты {$targetCurrency}.",
                'details' => [
                    'user_address' => $targetAddress,
                    'currency' => $targetCurrency,
                    'suggestion' => "Укажите адрес, совместимый с {$targetCurrency}"
                ]
            ];
        }

        // Определяем валюты, совместимые с адресом пользователя
        $compatibleCurrencies = $this->getCompatibleCurrencies($targetAddress);
        error_log("NOWPaymentsAPI INFO: Совместимые валюты для адреса: " . implode(', ', $compatibleCurrencies));

        if (empty($compatibleCurrencies)) {
            return [
                'error' => true,
                'code' => 'NO_COMPATIBLE_CURRENCIES',
                'message' => "Не удалось определить совместимые валюты для вашего адреса.",
                'details' => [
                    'user_address' => $targetAddress,
                    'suggestion' => "Проверьте правильность адреса"
                ]
            ];
        }

        // Получаем баланс для автоконвертации
        $balance = $this->getAccountBalance();
        if (!$balance) {
            return [
                'error' => true,
                'code' => 'BALANCE_ERROR',
                'message' => "Не удалось получить баланс аккаунта"
            ];
        }

        // Ищем лучший вариант автоконвертации
        $bestConversion = $this->findBestAutoConversion($balance, $compatibleCurrencies, $targetAmount, $targetCurrency);

        if (!$bestConversion) {
            // Если автоконвертация невозможна, показываем понятное сообщение
            $availableCurrencies = array_keys(array_filter($balance, function($data) {
                return ($data['amount'] ?? 0) > 0;
            }));

            return [
                'error' => true,
                'code' => 'NO_CONVERSION_POSSIBLE',
                'message' => "Невозможно конвертировать доступный баланс в валюту для вашего адреса. Пополните баланс " . implode(' или ', $compatibleCurrencies) . ".",
                'details' => [
                    'user_address' => $targetAddress,
                    'compatible_currencies' => $compatibleCurrencies,
                    'available_currencies' => $availableCurrencies,
                    'suggestion' => "Пополните баланс " . $compatibleCurrencies[0] . " для выплат на ваш адрес"
                ]
            ];
        }

        // Создаем выплату с автоконвертацией и обработкой комиссий
        error_log("NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: " . json_encode($bestConversion));

        $conversionResult = $this->createPayoutWithFeeHandling(
            $targetAddress,
            $bestConversion['target_currency'],
            $bestConversion['target_amount']
        );

        if ($conversionResult && !isset($conversionResult['error'])) {
            // Добавляем информацию о полной автоконвертации
            $conversionResult['full_auto_conversion'] = [
                'enabled' => true,
                'original_request' => [
                    'currency' => $targetCurrency,
                    'amount' => $targetAmount
                ],
                'conversion_path' => $bestConversion,
                'note' => "Автоматически конвертировано из доступного баланса"
            ];

            return $conversionResult;
        }

        error_log("NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию");

        // Если внутренняя конвертация не удалась, используем нашу автоконвертацию
        return $this->createPayoutWithAutoConversion($targetAddress, $targetCurrency, $targetAmount);
    }

    /**
     * Автоматическая конвертация из доступной валюты в запрашиваемую
     */
    public function createPayoutWithAutoConversion($targetAddress, $targetCurrency, $targetAmount) {
        error_log("NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией");
        error_log("NOWPaymentsAPI INFO: Цель - {$targetAmount} {$targetCurrency} на адрес {$targetAddress}");

        // БЕЗОПАСНОСТЬ: Проверяем, что адрес не является тестовым
        if ($this->isTestAddress($targetAddress)) {
            error_log("NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: {$targetAddress}");
            return [
                'error' => true,
                'code' => 'TEST_ADDRESS_BLOCKED',
                'message' => "Выплаты на тестовые адреса запрещены",
                'details' => [
                    'address' => $targetAddress,
                    'reason' => 'Адрес является тестовым или служебным',
                    'suggestion' => 'Используйте реальный пользовательский адрес'
                ]
            ];
        }

        // Сначала пробуем прямую выплату (самый простой способ)
        error_log("NOWPaymentsAPI INFO: Пробуем прямую выплату {$targetAmount} {$targetCurrency}");

        // Проверяем совместимость адреса с запрашиваемой валютой
        if ($this->isAddressCompatible($targetAddress, $targetCurrency)) {
            error_log("NOWPaymentsAPI INFO: Адрес совместим с {$targetCurrency}, пробуем прямую выплату");

            // Пробуем создать выплату напрямую
            $directResult = $this->createSinglePayout($targetAddress, $targetCurrency, $targetAmount);

            // Если выплата создана успешно, возвращаем результат
            if ($directResult && !isset($directResult['error'])) {
                error_log("NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно");
                return $directResult;
            }

            // Если ошибка не связана с недостаточным балансом, возвращаем ошибку
            if (!isset($directResult['error']) || $directResult['code'] !== 'BAD_CREATE_WITHDRAWAL_REQUEST') {
                error_log("NOWPaymentsAPI ERROR: Ошибка не связана с балансом, автоконвертация невозможна");
                return $directResult;
            }
        } else {
            error_log("NOWPaymentsAPI INFO: Адрес {$targetAddress} несовместим с {$targetCurrency}, переходим к автоконвертации");
        }

        error_log("NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию");

        // Получаем баланс всех валют
        $balance = $this->getAccountBalance();
        if (!$balance) {
            error_log("NOWPaymentsAPI ERROR: Не удалось получить баланс для автоконвертации");
            return ['error' => true, 'message' => 'Не удалось получить баланс аккаунта'];
        }

        // Определяем валюты, совместимые с адресом пользователя
        $compatibleCurrencies = $this->getCompatibleCurrencies($targetAddress);
        error_log("NOWPaymentsAPI INFO: Совместимые валюты для адреса {$targetAddress}: " . implode(', ', $compatibleCurrencies));

        // Ищем варианты конвертации в совместимые валюты
        $conversionOptions = [];

        foreach ($balance as $sourceCurrency => $data) {
            $availableAmount = $data['amount'] - ($data['pendingAmount'] ?? 0);

            if ($availableAmount <= 0) {
                continue; // Пропускаем валюты без баланса
            }

            // Пробуем конвертировать в каждую совместимую валюту
            foreach ($compatibleCurrencies as $compatibleCurrency) {
                try {
                    // Получаем курс конвертации из исходной валюты в совместимую
                    $estimate = $this->getEstimateAmount($availableAmount, $sourceCurrency, $compatibleCurrency);

                    if ($estimate && isset($estimate['estimated_amount'])) {
                        $convertedAmount = $estimate['estimated_amount'];

                        // Конвертируем запрашиваемую сумму в эквивалент совместимой валюты
                        $targetInCompatible = $this->getEstimateAmount($targetAmount, $targetCurrency, $compatibleCurrency);
                        $targetAmountInCompatible = $targetInCompatible['estimated_amount'] ?? $targetAmount;

                        if ($convertedAmount >= $targetAmountInCompatible) {
                            // Рассчитываем, сколько исходной валюты нужно
                            $neededSourceAmount = ($targetAmountInCompatible / $convertedAmount) * $availableAmount;

                            $conversionOptions[] = [
                                'source_currency' => $sourceCurrency,
                                'source_amount' => $neededSourceAmount,
                                'available_amount' => $availableAmount,
                                'target_currency' => $compatibleCurrency,
                                'target_amount' => $targetAmountInCompatible,
                                'original_target_currency' => $targetCurrency,
                                'original_target_amount' => $targetAmount,
                                'conversion_rate' => $convertedAmount / $availableAmount,
                                'priority' => $this->getCurrencyPriority($compatibleCurrency)
                            ];

                            error_log("NOWPaymentsAPI INFO: Найдена опция конвертации: {$neededSourceAmount} {$sourceCurrency} -> {$targetAmountInCompatible} {$compatibleCurrency} (эквивалент {$targetAmount} {$targetCurrency})");
                        }
                    }
                } catch (Exception $e) {
                    error_log("NOWPaymentsAPI WARNING: Ошибка при расчете конвертации {$sourceCurrency} -> {$compatibleCurrency}: " . $e->getMessage());
                    continue;
                }
            }
        }

        if (empty($conversionOptions)) {
            error_log("NOWPaymentsAPI ERROR: Не найдено валют для автоконвертации");

            $availableCurrencies = array_keys(array_filter($balance, function($data) {
                return ($data['amount'] ?? 0) > 0;
            }));

            // Специальное сообщение для TON адресов - но сначала пробуем все возможности
            if (in_array('ton', $compatibleCurrencies)) {
                // Проверяем, есть ли хоть какой-то баланс для попытки конвертации
                $hasAnyBalance = false;
                foreach ($balance as $currency => $data) {
                    if (($data['amount'] ?? 0) > 0.00001) { // Минимальный баланс для попытки
                        $hasAnyBalance = true;
                        break;
                    }
                }

                if ($hasAnyBalance) {
                    // Если есть баланс, пробуем создать выплату напрямую в TON
                    error_log("NOWPaymentsAPI INFO: Пробуем выплату в TON для TON адреса");

                    // Конвертируем запрашиваемую сумму в TON
                    try {
                        $tonEstimate = $this->getEstimateAmount($targetAmount, $targetCurrency, 'ton');
                        if ($tonEstimate && isset($tonEstimate['estimated_amount'])) {
                            $tonAmount = $tonEstimate['estimated_amount'];
                            error_log("NOWPaymentsAPI INFO: Конвертация {$targetAmount} {$targetCurrency} = {$tonAmount} TON");

                            // Пробуем создать выплату в TON
                            $tonResult = $this->createSinglePayout($targetAddress, 'ton', $tonAmount);
                            if ($tonResult && !isset($tonResult['error'])) {
                                error_log("NOWPaymentsAPI SUCCESS: Выплата в TON создана успешно");
                                $tonResult['auto_conversion'] = [
                                    'original_request' => ['currency' => $targetCurrency, 'amount' => $targetAmount],
                                    'actual_payout' => ['currency' => 'ton', 'amount' => $tonAmount],
                                    'note' => "Автоматически конвертировано в TON для TON адреса"
                                ];
                                return $tonResult;
                            }
                        }
                    } catch (Exception $e) {
                        error_log("NOWPaymentsAPI WARNING: Не удалось конвертировать в TON: " . $e->getMessage());
                    }
                }

                return [
                    'error' => true,
                    'message' => "Для выплаты на TON кошелек требуется баланс TON. 💡 Решения: 1. Пополните баланс TON в панели NOWPayments 2. Используйте BTC адрес для автоконвертации 3. Обратитесь к администратору для пополнения баланса",
                    'code' => 'NEED_TON_BALANCE',
                    'details' => [
                        'user_address' => $targetAddress,
                        'compatible_currencies' => $compatibleCurrencies,
                        'available_currencies' => $availableCurrencies,
                        'suggestion' => "Пополните баланс TON в панели NOWPayments для выплат на TON адреса"
                    ]
                ];
            }

            return [
                'error' => true,
                'message' => "Невозможно конвертировать в валюту, совместимую с вашим адресом. Доступные валюты: " . implode(', ', $availableCurrencies),
                'code' => 'NO_COMPATIBLE_CONVERSION',
                'details' => [
                    'user_address' => $targetAddress,
                    'compatible_currencies' => $compatibleCurrencies,
                    'available_currencies' => $availableCurrencies,
                    'suggestion' => "Укажите адрес, совместимый с одной из доступных валют"
                ]
            ];
        }

        // Выбираем лучшую опцию (по приоритету валюты, затем по наименьшему расходу)
        usort($conversionOptions, function($a, $b) {
            // Сначала сортируем по приоритету валюты (меньше = лучше)
            $priorityCompare = $a['priority'] <=> $b['priority'];
            if ($priorityCompare !== 0) {
                return $priorityCompare;
            }
            // Затем по наименьшему расходу исходной валюты
            return $a['source_amount'] <=> $b['source_amount'];
        });

        $bestOption = $conversionOptions[0];
        $sourceCurrency = $bestOption['source_currency'];
        $sourceAmount = $bestOption['source_amount'];
        $finalTargetCurrency = $bestOption['target_currency'];
        $finalTargetAmount = $bestOption['target_amount'];

        error_log("NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: {$sourceAmount} {$sourceCurrency} -> {$finalTargetAmount} {$finalTargetCurrency}");

        // Создаем выплату в совместимой валюте на адрес пользователя
        $conversionResult = $this->createSinglePayout($targetAddress, $finalTargetCurrency, $finalTargetAmount);

        if ($conversionResult && !isset($conversionResult['error'])) {
            error_log("NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно");

            // Добавляем информацию о конвертации в результат
            $conversionResult['auto_conversion'] = [
                'original_request' => [
                    'currency' => $targetCurrency,
                    'amount' => $targetAmount,
                    'address' => $targetAddress
                ],
                'actual_payout' => [
                    'currency' => $finalTargetCurrency,
                    'amount' => $finalTargetAmount,
                    'address' => $targetAddress // Используем адрес пользователя!
                ],
                'conversion_info' => "Автоматически конвертировано из {$sourceCurrency} в {$finalTargetCurrency} (эквивалент {$targetCurrency})",
                'address_note' => "Выплата отправлена на ваш адрес в совместимой валюте {$finalTargetCurrency}",
                'source_conversion' => [
                    'from_currency' => $sourceCurrency,
                    'from_amount' => $sourceAmount,
                    'to_currency' => $finalTargetCurrency,
                    'to_amount' => $finalTargetAmount
                ]
            ];

            return $conversionResult;
        }

        error_log("NOWPaymentsAPI ERROR: Автоконвертация также не удалась");
        return $conversionResult ?: ['error' => true, 'message' => 'Автоконвертация не удалась'];
    }

    /**
     * Найти лучший вариант автоконвертации из доступного баланса
     */
    public function findBestAutoConversion($balance, $compatibleCurrencies, $targetAmount, $targetCurrency) {
        error_log("NOWPaymentsAPI INFO: Поиск лучшей автоконвертации");

        $conversionOptions = [];

        // Проходим по всем доступным валютам в балансе
        foreach ($balance as $sourceCurrency => $data) {
            $availableAmount = $data['amount'] - ($data['pendingAmount'] ?? 0);

            if ($availableAmount <= 0) {
                continue; // Пропускаем валюты без баланса
            }

            error_log("NOWPaymentsAPI INFO: Проверяем {$sourceCurrency}: {$availableAmount}");

            // Проверяем каждую совместимую валюту
            foreach ($compatibleCurrencies as $compatibleCurrency) {
                try {
                    // Конвертируем запрашиваемую сумму в эквивалент совместимой валюты
                    $targetInCompatible = $this->convertAmount($targetAmount, $targetCurrency, $compatibleCurrency);

                    if (!$targetInCompatible) {
                        continue;
                    }

                    // Проверяем, можем ли мы конвертировать из доступной валюты в совместимую
                    $convertedAmount = $this->convertAmount($availableAmount, $sourceCurrency, $compatibleCurrency);

                    if ($convertedAmount && $convertedAmount >= $targetInCompatible) {
                        // Рассчитываем, сколько исходной валюты нужно
                        $neededSourceAmount = ($targetInCompatible / $convertedAmount) * $availableAmount;

                        $conversionOptions[] = [
                            'source_currency' => $sourceCurrency,
                            'source_amount' => $neededSourceAmount,
                            'target_currency' => $compatibleCurrency,
                            'target_amount' => $targetInCompatible,
                            'original_target_currency' => $targetCurrency,
                            'original_target_amount' => $targetAmount,
                            'priority' => $this->getCurrencyPriority($compatibleCurrency),
                            'efficiency' => $convertedAmount / $availableAmount // Эффективность конвертации
                        ];

                        error_log("NOWPaymentsAPI INFO: Найдена опция: {$neededSourceAmount} {$sourceCurrency} -> {$targetInCompatible} {$compatibleCurrency}");
                    }
                } catch (Exception $e) {
                    error_log("NOWPaymentsAPI WARNING: Ошибка при расчете конвертации {$sourceCurrency} -> {$compatibleCurrency}: " . $e->getMessage());
                    continue;
                }
            }
        }

        if (empty($conversionOptions)) {
            error_log("NOWPaymentsAPI ERROR: Не найдено опций автоконвертации");
            return null;
        }

        // Сортируем по приоритету валюты, затем по эффективности
        usort($conversionOptions, function($a, $b) {
            $priorityCompare = $a['priority'] <=> $b['priority'];
            if ($priorityCompare !== 0) {
                return $priorityCompare;
            }
            return $b['efficiency'] <=> $a['efficiency']; // Лучшая эффективность первой
        });

        $bestOption = $conversionOptions[0];
        error_log("NOWPaymentsAPI INFO: Выбрана лучшая опция: " . json_encode($bestOption));

        return $bestOption;
    }

    /**
     * Конвертировать сумму из одной валюты в другую
     */
    public function convertAmount($amount, $fromCurrency, $toCurrency) {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        try {
            $estimate = $this->getEstimateAmount($amount, $fromCurrency, $toCurrency);
            return $estimate['estimated_amount'] ?? null;
        } catch (Exception $e) {
            error_log("NOWPaymentsAPI WARNING: Ошибка конвертации {$fromCurrency} -> {$toCurrency}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получить минимальную сумму для выплаты
     */
    public function getMinWithdrawalAmount($currency) {
        try {
            $url = $this->apiUrl . '/payout-withdrawal/min-amount/' . $currency;

            // Пробуем разные методы авторизации
            $authMethods = ['both', 'bearer', 'x-api-key'];

            foreach ($authMethods as $authMethod) {
                $response = $this->makeRequest('GET', $url, null, $authMethod);
                if ($response && isset($response['min_amount'])) {
                    return $response['min_amount'];
                }
            }

            // Если не удалось получить через API, используем актуальные минимумы
            // Минимумы рассчитаны для курса 1 монета = $0.001
            $knownMinimums = [
                'ton' => 0.5,             // ~$0.5 (500 монет) - TON минимум
                'usdttrc20' => 8.58,      // $8.58 (высокий минимум NOWPayments)
                'usdt' => 8.58,           // $8.58 (общий USDT)
                'btc' => 0.000005,        // ~$0.005 (5 монет)
                'eth' => 0.0001,          // ~$0.25 (250 монет) - минимально возможный
                'ltc' => 0.001,           // ~$1.0 (1000 монет)
                'bch' => 0.001,           // ~$1.0 (1000 монет)
                'xrp' => 1.0,             // ~$1.0 (1000 монет)
                'ada' => 1.0,             // ~$1.0 (1000 монет)
                'dot' => 0.1              // ~$0.1 (100 монет)
            ];

            $currencyLower = strtolower($currency);
            if (isset($knownMinimums[$currencyLower])) {
                error_log("NOWPaymentsAPI INFO: Используем известный минимум для {$currency}: {$knownMinimums[$currencyLower]}");
                return $knownMinimums[$currencyLower];
            }

            return null;
        } catch (Exception $e) {
            error_log("NOWPaymentsAPI ERROR: Ошибка получения минимальной суммы для {$currency}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получить оценку комиссии для выплаты
     */
    public function getWithdrawalFeeEstimate($currency, $amount) {
        try {
            // Правильный endpoint согласно документации
            $url = $this->apiUrl . '/payout/fee';
            $params = [
                'currency' => strtoupper($currency),
                'amount' => (float)$amount
            ];

            // Пробуем разные методы авторизации
            $authMethods = ['both', 'bearer', 'x-api-key'];

            foreach ($authMethods as $authMethod) {
                $response = $this->makeRequest('GET', $url . '?' . http_build_query($params), null, $authMethod);
                if ($response && (isset($response['fee']) || isset($response['estimated_fee']))) {
                    error_log("NOWPaymentsAPI INFO: Получена комиссия для {$currency}: " . json_encode($response));
                    return $response;
                }
            }

            // Если основной метод не работает, пробуем альтернативный endpoint
            $altUrl = $this->apiUrl . '/payout-withdrawal/fee-estimate';
            $data = [
                'currency' => strtoupper($currency),
                'amount' => (float)$amount
            ];

            foreach ($authMethods as $authMethod) {
                $response = $this->makeRequest('POST', $altUrl, $data, $authMethod);
                if ($response && isset($response['fee'])) {
                    error_log("NOWPaymentsAPI INFO: Получена комиссия (альт. метод) для {$currency}: " . json_encode($response));
                    return $response;
                }
            }

            error_log("NOWPaymentsAPI WARNING: Не удалось получить комиссию для {$currency} через API, используем fallback");

            // Fallback: возвращаем примерную комиссию на основе статических данных
            return $this->getFallbackFeeEstimate($currency, $amount);
        } catch (Exception $e) {
            error_log("NOWPaymentsAPI ERROR: Ошибка получения комиссии для {$currency}: " . $e->getMessage());
            return $this->getFallbackFeeEstimate($currency, $amount);
        }
    }

    /**
     * Fallback метод для получения примерной комиссии
     */
    private function getFallbackFeeEstimate($currency, $amount) {
        // Примерные комиссии на основе типичных значений NOWPayments
        $fallbackFees = [
            'usdttrc20' => ['fixed' => 1.0, 'percent' => 0.5], // 1 USDT + 0.5%
            'btc' => ['fixed' => 0.0001, 'percent' => 0.5], // 0.0001 BTC + 0.5%
            'eth' => ['fixed' => 0.002, 'percent' => 0.5], // 0.002 ETH + 0.5%
            'ton' => ['fixed' => 0.1, 'percent' => 0.5], // 0.1 TON + 0.5%
            'ton' => ['fixed' => 0.1, 'percent' => 0.5], // 0.1 TON + 0.5% (заменил TRX)
        ];

        $currency = strtolower($currency);
        if (!isset($fallbackFees[$currency])) {
            return [
                'fee' => $amount * 0.01, // 1% по умолчанию
                'currency' => strtoupper($currency),
                'amount' => $amount,
                'fallback' => true,
                'note' => 'Примерная комиссия (API недоступен)'
            ];
        }

        $feeData = $fallbackFees[$currency];
        $calculatedFee = $feeData['fixed'] + ($amount * $feeData['percent'] / 100);

        return [
            'fee' => $calculatedFee,
            'currency' => strtoupper($currency),
            'amount' => $amount,
            'fixed_fee' => $feeData['fixed'],
            'percent_fee' => $feeData['percent'],
            'fallback' => true,
            'note' => 'Примерная комиссия на основе типичных значений NOWPayments'
        ];
    }

    /**
     * Создать выплату с использованием внутренней автоконвертации NOWPayments
     */
    public function createPayoutWithNOWPaymentsAutoConversion($targetAddress, $targetCurrency, $targetAmount) {
        error_log("NOWPaymentsAPI INFO: Попытка создания выплаты с внутренней автоконвертацией NOWPayments");

        // Создаем выплату с флагом автоконвертации
        $url = $this->apiUrl . '/payout';
        $data = [
            'withdrawals' => [
                [
                    'address' => $targetAddress,
                    'currency' => $targetCurrency,
                    'amount' => (float)$targetAmount,
                    'auto_conversion' => true // Включаем автоконвертацию NOWPayments
                ]
            ]
        ];

        error_log("NOWPaymentsAPI INFO: Создание выплаты с автоконвертацией: " . json_encode($data));

        $response = $this->makeRequest('POST', $url, $data, 'both');

        if ($response && !isset($response['error'])) {
            error_log("NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией NOWPayments создана успешно");
            $response['nowpayments_auto_conversion'] = true;
        }

        return $response;
    }

    /**
     * Создать выплату с учетом минимальных сумм и комиссий
     */
    public function createPayoutWithFeeHandling($targetAddress, $targetCurrency, $targetAmount) {
        error_log("NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий");

        // БЕЗОПАСНОСТЬ: Проверяем, что адрес не является тестовым
        if ($this->isTestAddress($targetAddress)) {
            error_log("NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: {$targetAddress}");
            return [
                'error' => true,
                'code' => 'TEST_ADDRESS_BLOCKED',
                'message' => "Выплаты на тестовые адреса запрещены",
                'details' => [
                    'address' => $targetAddress,
                    'reason' => 'Адрес является тестовым или служебным',
                    'suggestion' => 'Используйте реальный пользовательский адрес'
                ]
            ];
        }

        // Получаем минимальную сумму
        $minAmount = $this->getMinWithdrawalAmount($targetCurrency);
        if ($minAmount && $targetAmount < $minAmount) {
            return [
                'error' => true,
                'code' => 'AMOUNT_TOO_LOW',
                'message' => "Минимальная сумма для выплаты {$targetCurrency}: {$minAmount}",
                'details' => [
                    'requested_amount' => $targetAmount,
                    'minimum_amount' => $minAmount,
                    'currency' => $targetCurrency,
                    'suggestion' => "Увеличьте сумму до {$minAmount} или больше"
                ]
            ];
        }

        // Получаем оценку комиссии
        $feeEstimate = $this->getWithdrawalFeeEstimate($targetCurrency, $targetAmount);
        if ($feeEstimate) {
            error_log("NOWPaymentsAPI INFO: Комиссия для {$targetAmount} {$targetCurrency}: " . json_encode($feeEstimate));
        }

        // Создаем выплату с минимальным набором полей
        // Убираем проблемные поля и используем только обязательные
        $url = $this->apiUrl . '/payout';
        $data = [
            'withdrawals' => [
                [
                    'address' => $targetAddress,
                    'currency' => $targetCurrency,
                    'amount' => (float)$targetAmount
                ]
            ]
        ];

        error_log("NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: " . json_encode($data));

        $response = $this->makeRequest('POST', $url, $data, 'both');

        if ($response && !isset($response['error'])) {
            // Добавляем информацию о комиссиях
            $response['fee_handling'] = [
                'fee_paid_by_user' => true,
                'min_amount' => $minAmount,
                'fee_estimate' => $feeEstimate,
                'note' => 'Комиссия списывается с суммы получателя'
            ];
        }

        return $response;
    }

    /**
     * Проверить, является ли адрес тестовым или служебным
     * ИСПРАВЛЕНО: Убрана слишком строгая проверка, которая блокировала реальные адреса
     */
    private function isTestAddress($address) {
        // Список известных тестовых и служебных адресов (только точные совпадения)
        $testAddresses = [
            // Bitcoin тестовые адреса
            '**********************************', // Genesis block
            '**********************************', // Hal Finney
            '**********************************', // Известный тестовый

            // Ethereum тестовые адреса
            '******************************************', // Null address
            '******************************************', // Burn address
            '******************************************', // Vitalik

            // TON тестовые адреса (только явно тестовые)
            'UQTestAddress123456789012345678901234567890123456', // Тестовый TON
            'EQExampleAddress123456789012345678901234567890123', // Тестовый TON
            'UQSampleAddress123456789012345678901234567890123', // Тестовый TON
            'EQDemoAddress123456789012345678901234567890123456', // Тестовый TON

            // Другие известные тестовые адреса
            '******************************************', // Bitcoin bech32 тестовый
            'LTC1QW508D6QEJXTDG4Y5R3ZARVARY0C5XW7KV8F3T4', // Litecoin тестовый
        ];

        // Проверяем только точное совпадение с известными тестовыми адресами
        if (in_array($address, $testAddresses)) {
            return true;
        }

        // ИСПРАВЛЕНО: Убираем паттерны, которые могли блокировать реальные адреса
        // Оставляем только самые очевидные случаи
        $testPatterns = [
            '/^0x0+$/',          // Только нули в Ethereum (0x000...000)
            '/^1+$/',            // Только единицы (111...111)
            '/^UQ0{40,}$/',      // TON адрес из одних нулей после UQ
            '/^EQ0{40,}$/',      // TON адрес из одних нулей после EQ
        ];

        foreach ($testPatterns as $pattern) {
            if (preg_match($pattern, $address)) {
                return true;
            }
        }

        // ИСПРАВЛЕНО: Больше не блокируем адреса по содержанию слов "test", "example" и т.д.
        // Это могло блокировать реальные адреса пользователей

        return false;
    }

    /**
     * Получить список валют, совместимых с адресом
     * Включает поддержку автоконвертации через NOWPayments
     */
    public function getCompatibleCurrencies($address) {
        $compatibleCurrencies = [];

        // Определяем тип адреса и добавляем совместимые валюты
        if (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||
            preg_match('/^bc1[a-z0-9]{39,59}$/', $address)) {
            // Bitcoin адрес - поддерживает автоконвертацию из любой валюты
            $compatibleCurrencies[] = 'btc';
            // Добавляем все основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['eth', 'ton', 'usdttrc20', 'usdterc20']);
        }

        if (preg_match('/^0x[a-fA-F0-9]{40}$/', $address)) {
            // Ethereum адрес - поддерживает автоконвертацию из любой валюты
            $compatibleCurrencies[] = 'eth';
            $compatibleCurrencies[] = 'usdterc20';
            // Добавляем все основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'ton', 'usdttrc20']);
        }

        if (preg_match('/^[UE][Qf][0-9A-Za-z_-]{46}$/', $address)) {
            // TON адрес - поддерживает автоконвертацию из любой валюты
            $compatibleCurrencies[] = 'ton';
            // Добавляем все основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'eth', 'usdterc20', 'usdttrc20']);
        }

        if (preg_match('/^[LM][a-km-zA-HJ-NP-Z1-9]{26,33}$/', $address) ||
            preg_match('/^ltc1[a-z0-9]{39,59}$/', $address)) {
            // Litecoin адрес
            $compatibleCurrencies[] = 'ltc';
            // Добавляем основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'eth', 'ton']);
        }

        if (preg_match('/^D[5-9A-HJ-NP-U][1-9A-HJ-NP-Za-km-z]{32}$/', $address)) {
            // Dogecoin адрес
            $compatibleCurrencies[] = 'doge';
            // Добавляем основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'eth', 'ton']);
        }

        if (preg_match('/^bnb1[a-z0-9]{38}$/', $address)) {
            // Binance Chain адрес
            $compatibleCurrencies[] = 'bnb';
            // Добавляем основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'eth', 'ton']);
        }

        if (preg_match('/^[0-9A-Za-z_-]{48}$/', $address) ||
            preg_match('/^[UE][Qf][0-9A-Za-z_-]{46}$/', $address)) {
            // TON адрес (raw или user-friendly format)
            $compatibleCurrencies[] = 'ton';
            // Добавляем основные валюты для автоконвертации
            $compatibleCurrencies = array_merge($compatibleCurrencies, ['btc', 'eth', 'usdttrc20']);
        }

        // Удаляем дубликаты и возвращаем уникальные валюты
        return array_unique($compatibleCurrencies);
    }

    /**
     * Получить приоритет валюты (меньше = лучше)
     */
    public function getCurrencyPriority($currency) {
        $priorities = [
            'usdttrc20' => 1,  // USDT TRC20 - высший приоритет
            'ton' => 2,        // Toncoin - второй приоритет
            'btc' => 3,        // Bitcoin - третий приоритет
            'eth' => 4,        // Ethereum - четвертый приоритет
            // 'trx' удален - заменен на TON выше
            'usdterc20' => 6,  // USDT ERC20
            'ltc' => 7,        // Litecoin
            'doge' => 8,       // Dogecoin
            'bnb' => 9         // Binance Coin
        ];

        return $priorities[strtolower($currency)] ?? 99;
    }

    /**
     * Проверить совместимость адреса с валютой
     */
    public function isAddressCompatible($address, $currency) {
        switch (strtolower($currency)) {
            case 'btc':
                // Bitcoin адреса: Legacy (1...), SegWit (3...), Bech32 (bc1...)
                return (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||
                        preg_match('/^bc1[a-z0-9]{39,59}$/', $address));

            case 'eth':
            case 'usdterc20':
                // Ethereum адреса: 0x + 40 hex символов
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);

            case 'usdttrc20':
                // USDT TRC20 адреса: T + 33 символа base58 (оставляем для USDT TRC20)
                return (strlen($address) === 34 && substr($address, 0, 1) === 'T');

            case 'ltc':
                // Litecoin адреса: L, M, ltc1
                return (preg_match('/^[LM][a-km-zA-HJ-NP-Z1-9]{26,33}$/', $address) ||
                        preg_match('/^ltc1[a-z0-9]{39,59}$/', $address));

            case 'doge':
                // Dogecoin адреса: D + base58
                return preg_match('/^D[5-9A-HJ-NP-U][1-9A-HJ-NP-Za-km-z]{32}$/', $address);

            case 'bnb':
                // Binance Chain адреса: bnb + bech32
                return preg_match('/^bnb1[a-z0-9]{38}$/', $address);

            case 'ton':
                // TON адреса: raw format (48 символов) или user-friendly (UQ/EQ/kQ + 42-46 символов)
                return (preg_match('/^[0-9A-Za-z_-]{48}$/', $address) ||
                        preg_match('/^[UEkuek]Q[0-9A-Za-z_-]{42,46}$/', $address));

            default:
                // Для неизвестных валют считаем совместимыми
                return true;
        }
    }

    /**
     * Проверить подпись IPN уведомления
     */
    public function verifyIPNSignature($data, $signature) {
        $expectedSignature = hash_hmac('sha512', $data, $this->ipnSecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Выполнить HTTP запрос без авторизации (для получения JWT токена)
     */
    private function makeRequestWithoutAuth($method, $url, $data = null) {
        $headers = [
            'Content-Type: application/json'
        ];

        error_log("NOWPaymentsAPI INFO: Отправка запроса {$method} {$url} без авторизации");
        if ($data) {
            error_log("NOWPaymentsAPI INFO: Данные запроса: " . json_encode($data));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            error_log("NOWPaymentsAPI CURL ERROR: {$error}");
            return false;
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            error_log("NOWPaymentsAPI HTTP INFO: Code {$httpCode}, Response: {$response}");
            return false;
        }

        error_log("NOWPaymentsAPI SUCCESS: Ответ получен: " . $response);
        return json_decode($response, true);
    }
}
?>
