<?php
/**
 * 🔒 API для проверки активных выплат пользователя
 * Возвращает информацию о том, есть ли у пользователя активные выплаты
 * которые блокируют создание новых заявок
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/validate_initdata.php';

try {
    // 1. Получение и валидация входных данных
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if (!$input || !isset($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Отсутствуют обязательные параметры']);
        exit;
    }

    // 2. Валидация initData с fallback механизмом
    $validatedData = validateTelegramInitData($input['initData']);
    if ($validatedData === false) {
        error_log("checkActiveWithdrawals WARNING: Стандартная валидация не прошла, пробуем упрощенную");

        // Fallback: пытаемся извлечь данные пользователя напрямую
        $initDataParts = [];
        parse_str($input['initData'], $initDataParts);

        if (isset($initDataParts['user'])) {
            $userArray = json_decode($initDataParts['user'], true);
            if ($userArray !== null && isset($userArray['id'])) {
                $validatedData = ['user' => $userArray];
                error_log("checkActiveWithdrawals INFO: Упрощенная валидация прошла для пользователя " . $userArray['id']);
            } else {
                error_log("checkActiveWithdrawals ERROR: Не удалось извлечь данные пользователя из initData");
                http_response_code(401);
                echo json_encode(['error' => 'Ошибка аутентификации: неверные данные пользователя']);
                exit;
            }
        } else {
            error_log("checkActiveWithdrawals ERROR: Отсутствуют данные пользователя в initData");
            http_response_code(401);
            echo json_encode(['error' => 'Ошибка аутентификации: отсутствуют данные пользователя']);
            exit;
        }
    }

    $userId = intval($validatedData['user']['id']);
    error_log("checkActiveWithdrawals INFO: Проверка активных выплат для пользователя {$userId}");

    // 3. Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        http_response_code(404);
        echo json_encode(['error' => 'Пользователь не найден']);
        exit;
    }

    $user = $userData[$userId];

    // 4. Проверяем, не заблокирован ли пользователь
    if (isset($user['blocked']) && $user['blocked']) {
        error_log("checkActiveWithdrawals WARNING: Заблокированный пользователь $userId пытается проверить активные выплаты");
        http_response_code(403);
        echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
        exit;
    }

    $withdrawals = $user['withdrawals'] ?? [];

    // 5. Анализ активных выплат
    $activeWithdrawals = [];
    $blockingStatuses = ['waiting', 'processing', 'pending', 'sending']; // Статусы которые блокируют новые заявки

    foreach ($withdrawals as $withdrawal) {
        $status = $withdrawal['status'] ?? 'unknown';
        
        if (in_array($status, $blockingStatuses)) {
            $activeWithdrawals[] = [
                'id' => $withdrawal['id'] ?? $withdrawal['payout_id'] ?? 'unknown',
                'payout_id' => $withdrawal['payout_id'] ?? null,
                'status' => $status,
                'status_text' => getStatusText($status),
                'coins_amount' => $withdrawal['coins_amount'] ?? 0,
                'currency' => $withdrawal['currency'] ?? 'unknown',
                'created_at' => $withdrawal['created_at'] ?? 'unknown',
                'updated_at' => $withdrawal['updated_at'] ?? 'unknown'
            ];
        }
    }

    // 6. Определяем блокировку формы
    $isBlocked = !empty($activeWithdrawals);
    $blockReason = '';

    if ($isBlocked) {
        $count = count($activeWithdrawals);
        $blockReason = "У вас есть {$count} активная" . ($count > 1 ? 'х' : 'я') . " выплата" . ($count > 1 ? '' : '') . " в обработке. Дождитесь завершения текущих выплат перед созданием новых заявок.";
    }

    // 7. Формируем ответ
    $response = [
        'success' => true,
        'user_id' => $userId,
        'is_blocked' => $isBlocked,
        'block_reason' => $blockReason,
        'active_withdrawals_count' => count($activeWithdrawals),
        'active_withdrawals' => $activeWithdrawals,
        'blocking_statuses' => $blockingStatuses,
        'checked_at' => date('Y-m-d H:i:s')
    ];

    error_log("checkActiveWithdrawals INFO: Пользователь {$userId} - блокировка: " . ($isBlocked ? 'ДА' : 'НЕТ') . ", активных выплат: " . count($activeWithdrawals));

    echo json_encode($response);

} catch (Exception $e) {
    error_log("checkActiveWithdrawals ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при проверке активных выплат',
        'details' => $e->getMessage()
    ]);
}

/**
 * Получает текст статуса выплаты на русском языке
 */
function getStatusText($status) {
    $statusMap = [
        // Статусы NOWPayments
        'waiting' => 'Ожидание обработки',
        'processing' => 'Обрабатывается',
        'sending' => 'Отправляется на кошелек',
        'finished' => 'Отправлено на кошелек',
        'completed' => 'Отправлено на кошелек',
        'confirmed' => 'Подтверждено в блокчейне',
        'failed' => 'Ошибка выплаты',
        'rejected' => 'Отклонено системой',
        'cancelled' => 'Отменено',
        'expired' => 'Истекло',
        'pending' => 'В обработке'
    ];
    return $statusMap[$status] ?? $status ?? 'Неизвестно';
}

?>
