<?php
/**
 * api/admin/test_support_enhancements.php
 * Тестирование новых функций системы поддержки
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/support_data.php';

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-gear me-2"></i>
                    Тестирование улучшений поддержки
                </h1>
            </div>

            <!-- Результаты тестов -->
            <div id="test-results"></div>

            <!-- Тесты функций -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Тесты функций</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Основные функции</h6>
                            <button class="btn btn-primary btn-sm mb-2" onclick="testChatStatusStats()">
                                📊 Тест статистики чатов
                            </button><br>
                            <button class="btn btn-info btn-sm mb-2" onclick="testMigration()">
                                🔄 Тест миграции чатов
                            </button><br>
                            <button class="btn btn-success btn-sm mb-2" onclick="testStatusUpdate()">
                                🔒 Тест обновления статуса
                            </button><br>
                        </div>
                        <div class="col-md-6">
                            <h6>API функции</h6>
                            <button class="btn btn-warning btn-sm mb-2" onclick="testAPI('update_status')">
                                🔧 Тест API статуса
                            </button><br>
                            <button class="btn btn-secondary btn-sm mb-2" onclick="testFiltering()">
                                🔍 Тест фильтрации
                            </button><br>
                            <button class="btn btn-dark btn-sm mb-2" onclick="clearTests()">
                                🧹 Очистить результаты
                            </button><br>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Информация о системе -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Информация о системе</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Файлы системы</h6>
                            <ul class="list-unstyled">
                                <?php
                                $files = [
                                    'support_data.php' => 'Функции работы с данными',
                                    'manage_support_chat.php' => 'API управления чатами',
                                    'auto_migrate_support.php' => 'Автомиграция',
                                    'support.php' => 'Главная страница поддержки',
                                    'support_chat.php' => 'Страница отдельного чата'
                                ];
                                
                                foreach ($files as $file => $description) {
                                    $exists = file_exists(__DIR__ . '/' . $file);
                                    $icon = $exists ? '✅' : '❌';
                                    echo "<li>{$icon} <strong>{$file}</strong> - {$description}</li>";
                                }
                                ?>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Статистика</h6>
                            <?php
                            $stats = getChatStatusStats();
                            ?>
                            <ul class="list-unstyled">
                                <li><strong>Всего чатов:</strong> <?php echo $stats['total']; ?></li>
                                <li><strong>Открытых:</strong> <?php echo $stats['open']; ?></li>
                                <li><strong>Закрытых:</strong> <?php echo $stats['closed']; ?></li>
                                <li><strong>С непрочитанными:</strong> <?php echo $stats['unread']; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function addTestResult(title, message, type) {
    const resultsDiv = document.getElementById('test-results');
    const alertClass = type === 'success' ? 'alert-success' : (type === 'error' ? 'alert-danger' : 'alert-info');
    const icon = type === 'success' ? '✅' : (type === 'error' ? '❌' : 'ℹ️');
    
    const resultHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <strong>${icon} ${title}</strong><br>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    resultsDiv.insertAdjacentHTML('afterbegin', resultHtml);
}

function testChatStatusStats() {
    fetch('migrate_support_chats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addTestResult(
                    'Тест статистики чатов',
                    `Статистика получена успешно. ${data.details || 'Данные обработаны корректно.'}`,
                    'success'
                );
            } else {
                addTestResult('Тест статистики чатов', 'Ошибка: ' + data.error, 'error');
            }
        })
        .catch(error => {
            addTestResult('Тест статистики чатов', 'Ошибка сети: ' + error.message, 'error');
        });
}

function testMigration() {
    fetch('migrate_support_chats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addTestResult(
                    'Тест миграции чатов',
                    `Миграция выполнена. ${data.details}`,
                    'success'
                );
            } else {
                addTestResult('Тест миграции чатов', 'Ошибка: ' + data.error, 'error');
            }
        })
        .catch(error => {
            addTestResult('Тест миграции чатов', 'Ошибка сети: ' + error.message, 'error');
        });
}

function testStatusUpdate() {
    addTestResult(
        'Тест обновления статуса',
        'Функция updateChatStatus() доступна. Для полного тестирования используйте интерфейс управления чатами.',
        'info'
    );
}

function testAPI(action) {
    addTestResult(
        'Тест API',
        `API endpoint manage_support_chat.php доступен для действия: ${action}. Для полного тестирования используйте кнопки управления в интерфейсе.`,
        'info'
    );
}

function testFiltering() {
    const currentUrl = window.location.href;
    const testUrl = 'support.php?status=open&date=today&page=1&per_page=10';
    
    addTestResult(
        'Тест фильтрации',
        `Система фильтрации готова. Тестовый URL: <a href="${testUrl}" target="_blank">${testUrl}</a>`,
        'info'
    );
}

function clearTests() {
    document.getElementById('test-results').innerHTML = '';
}

// Автоматический тест при загрузке
window.addEventListener('load', function() {
    addTestResult(
        'Система загружена',
        'Все компоненты системы поддержки загружены и готовы к работе.',
        'success'
    );
});
</script>

<?php include 'templates/footer.php'; ?>
