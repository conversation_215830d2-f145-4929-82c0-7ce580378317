
/* Increase font size for specific elements in the calculator */

.calculator-subtitle,
.balance-label,
.amount-input-section label,
#dollar-equivalent,
.balance-status,
.requirement-label,
.requirement-value,
.status-text {
    font-size: calc(1em + 2px);
}

/* Adjusting the balance amount separately as it has a different base size */
.balance-display .balance-amount {
    font-size: calc(16px + 2px);
}
