# 🤖 Документация по системе настроек бота

## 📋 Обзор

Система настроек бота была полностью переработана для обеспечения централизованного управления всеми параметрами Telegram ботов через админку.

### ❌ Что было раньше (проблемы):
- Настройки были захардкожены в нескольких файлах
- `api/config.php` и `bot/config.php` содержали дублирующиеся константы
- Изменения в админке не применялись к боту
- Webhook URL нужно было обновлять вручную
- Нет единого источника истины для настроек

### ✅ Что стало (решение):
- Все настройки хранятся в одном JSON файле
- Админка может изменять любые настройки бота
- Автоматическое обновление webhook при изменении URL
- Единый загрузчик настроек для всех компонентов
- Логирование изменений и кэширование

## 📁 Структура файлов

### Новые файлы:
```
database/bot_settings.json          # Основной файл с настройками
includes/bot_config_loader.php      # Загрузчик настроек
```

### Обновленные файлы:
```
api/config.php                      # Теперь загружает из JSON
bot/config.php                      # Теперь загружает из JSON
api/admin/bot_settings.php          # Обновленная админка
api/admin/support_config.php        # Обновленная конфигурация поддержки
bot/support_bot_config.php          # Обновленная конфигурация поддержки
```

## 🔧 Файл настроек (database/bot_settings.json)

```json
{
    "TELEGRAM_BOT_TOKEN": "**********************************************",
    "BOT_USERNAME": "uniqpaid_paid_bot",
    "WEBAPP_URL": "https://app.uniqpaid.com/test3",
    "WEBHOOK_URL": "https://app.uniqpaid.com/test3/bot/webhook.php",
    "SUPPORT_BOT_TOKEN": "7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M",
    "SUPPORT_BOT_USERNAME": "uniqpaid_support_bot",
    "SUPPORT_WEBHOOK_URL": "https://app.uniqpaid.com/test3/api/admin/support_webhook.php",
    "last_updated": "2025-07-08 18:00:18",
    "updated_by": "admin"
}
```

## 🔌 API загрузчика настроек

### Основные функции:

#### `loadBotSettings()`
Загружает все настройки из JSON файла с кэшированием.

```php
$settings = loadBotSettings();
echo $settings['BOT_USERNAME']; // uniqpaid_paid_bot
```

#### `getBotSetting($key, $default = null)`
Получает конкретную настройку с возможностью указать значение по умолчанию.

```php
$botUsername = getBotSetting('BOT_USERNAME', 'default_bot');
$nonExistent = getBotSetting('SOME_KEY', 'default_value');
```

#### `saveBotSettings($newSettings, $updatedBy = null)`
Сохраняет новые настройки в JSON файл.

```php
$newSettings = [
    'BOT_USERNAME' => 'new_bot_name',
    'WEBAPP_URL' => 'https://new-domain.com'
];
saveBotSettings($newSettings, 'admin_user');
```

#### `defineBotConstants()`
Определяет PHP константы на основе загруженных настроек.

```php
defineBotConstants();
echo BOT_USERNAME; // uniqpaid_paid_bot
echo WEBHOOK_URL;  // https://app.uniqpaid.com/test3/bot/webhook.php
```

## 🎛️ Использование в админке

### Обновленная форма настроек:

Админка теперь содержит поля для всех настроек:

1. **Основные параметры бота:**
   - BOT_TOKEN
   - BOT_USERNAME  
   - WEBAPP_URL
   - WEBHOOK_URL

2. **Настройки бота поддержки:**
   - SUPPORT_BOT_TOKEN
   - SUPPORT_BOT_USERNAME
   - SUPPORT_WEBHOOK_URL

### Автоматическое обновление webhook:

При изменении WEBHOOK_URL в админке автоматически отправляется запрос к Telegram API для обновления webhook.

## 🔄 Процесс работы

1. **Загрузка настроек:**
   ```php
   require_once 'includes/bot_config_loader.php';
   defineBotConstants();
   ```

2. **Использование в коде:**
   ```php
   // Константы доступны глобально
   echo BOT_USERNAME;
   echo WEBHOOK_URL;
   
   // Или через функции
   $settings = loadBotSettings();
   echo $settings['BOT_USERNAME'];
   ```

3. **Изменение через админку:**
   - Заходим в админку → Настройки Telegram Бота
   - Изменяем нужные параметры
   - Нажимаем "Сохранить все изменения"
   - Настройки автоматически применяются ко всем компонентам

## 🛡️ Безопасность и надежность

### Fallback механизм:
Если JSON файл недоступен, используются настройки по умолчанию.

### Кэширование:
Настройки кэшируются в памяти для повышения производительности.

### Логирование:
Все изменения настроек логируются с указанием времени и пользователя.

### Валидация:
Проверка корректности JSON и наличия обязательных полей.

## 🧪 Тестирование

### Скрипты для тестирования:

1. **test_bot_settings.php** - полное тестирование системы
2. **migrate_bot_settings.php** - миграция и обновление webhook
3. **demo_bot_settings.php** - демонстрационная страница

### Запуск тестов:
```bash
php test_bot_settings.php
php migrate_bot_settings.php
```

## 🎯 Преимущества новой системы

1. **Централизованное управление** - все настройки в одном месте
2. **Динамическое обновление** - изменения применяются без перезапуска
3. **Автоматизация** - webhook обновляется автоматически
4. **Логирование** - отслеживание всех изменений
5. **Надежность** - fallback механизмы и валидация
6. **Производительность** - кэширование настроек
7. **Удобство** - простой интерфейс в админке

## 🔧 Устранение неполадок

### Проблема: Настройки не применяются
**Решение:** Проверьте права доступа к файлу `database/bot_settings.json`

### Проблема: Webhook не обновляется
**Решение:** Проверьте токен бота и доступность URL

### Проблема: Ошибки в логах
**Решение:** Проверьте синтаксис JSON файла

### Проверка состояния:
```bash
# Проверка настроек
php -r "require_once 'includes/bot_config_loader.php'; var_dump(loadBotSettings());"

# Проверка констант
php -r "require_once 'api/config.php'; echo BOT_USERNAME;"
```

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в `database/error.log`
2. Запустите тестовые скрипты
3. Проверьте права доступа к файлам
4. Убедитесь в корректности JSON синтаксиса
