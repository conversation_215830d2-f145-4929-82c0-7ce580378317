// === main.js ===
// Файл: js/main.js
// Описание: Главный класс приложения с полной интеграцией из оригинала

class UniQPaidApp {
  constructor() {
    this.isInitialized = false;
    this.tg = window.Telegram?.WebApp;

    // Элементы интерфейса (правильные ID из HTML)
    this.elements = {
      mainContentEl: document.getElementById('main-content'),
      earnSectionEl: document.getElementById('earn-section'),
      friendsSectionEl: document.getElementById('friends-section'),
      userNameEl: document.getElementById('user-name'),
      balanceAmountEl: document.getElementById('balance-amount'),
      headerBalanceInfoEl: document.getElementById('header-balance-info'),
      watchAdButton: document.getElementById('openLinkButton'), // Кнопка "Открыть ссылку"
      watchVideoButton: document.getElementById('watchVideoButton'), // Кнопка "Смотреть видео"
      openLinkButton: document.getElementById('openAdButton'), // Кнопка "Открыть рекламу"
      statusMessageEl: document.getElementById('status-message')
    };
  }

  async init() {
    if (this.isInitialized) {
      console.warn('[UniQPaidApp] Приложение уже инициализировано.');
      return;
    }

    console.log(`🚀 Инициализация ${window.AppConfig?.BOT_USERNAME || 'UniQPaid'} App (v с анимацией v6)...`);

    // Проверяем доступность Telegram WebApp (из оригинала)
    if (!this.tg) {
      console.error('🛑 Критическая ошибка: Telegram WebApp не найден.');
      alert('Не удалось запустить приложение. Убедитесь, что вы открываете его через Telegram.');
      return;
    }

    this.tg.ready();
    this.tg.expand();

    // Проверяем необходимые элементы интерфейса (из оригинала)
    const missingElements = this.checkRequiredElements();
    if (missingElements.length > 0) {
      console.error('🛑 Отсутствуют необходимые элементы:', missingElements);
      return;
    }

    try {
      // Этап 0: Инициализация локализации (определение языка по IP)
      console.log('[UniQPaidApp] 🌐 Инициализация системы локализации...');
      await this.initializeLocalization();

      // Этап 1: Инициализация базовых модулей (из оригинала)
      console.log('[UniQPaidApp] Инициализация базовых модулей...');
      this.initializeBaseModules();

      // Этап 2: Загрузка данных пользователя и настроек (из оригинала)
      console.log('[UniQPaidApp] Загрузка данных пользователя...');
      await this.fetchUserDataAndSettings();

      // Этап 3: Инициализация модулей, зависящих от данных пользователя (из оригинала)
      console.log('[UniQPaidApp] Инициализация дополнительных модулей...');
      this.initializeAdvancedModules();

      // Этап 4: Финальная настройка интерфейса (из оригинала)
      console.log('[UniQPaidApp] Финальная настройка интерфейса...');
      this.finalizeInterface();

      this.isInitialized = true;
      console.log('✅ Приложение успешно инициализировано.');

      if (window.appUtils) {
        window.appUtils.showStatus('');
      }

    } catch (error) {
      console.error('❌ Критическая ошибка при инициализации приложения:', error);

      // Не показываем alert для ошибок аутентификации в тестовом режиме
      if (error.message.includes('аутентификации')) {
        console.warn('[UniQPaidApp] Работаем в тестовом режиме без аутентификации');

        // Продолжаем инициализацию без данных пользователя
        this.initializeAdvancedModules();
        this.finalizeInterface();
        this.isInitialized = true;

        if (window.appUtils) {
          window.appUtils.showStatus('Тестовый режим', 'warning');
        }
      } else {
        if (window.appUtils) {
          window.appUtils.showAlert(`Ошибка загрузки: ${error.message}`);
        } else {
          console.error('Ошибка:', error.message);
        }
      }
    }
  }

  /**
   * Инициализирует систему локализации с определением языка по IP
   */
  async initializeLocalization() {
    if (window.appLocalization) {
      console.log('[UniQPaidApp] 🌐 Запуск системы локализации...');

      // Показываем статус загрузки
      if (window.appUtils) {
        window.appUtils.showStatus('Определение языка...', 'info');
      }

      try {
        // Инициализируем локализацию (определение языка по IP + загрузка переводов)
        await window.appLocalization.init();

        // Обновляем язык HTML документа
        const detectedLanguage = window.appLocalization.currentLanguage;
        document.documentElement.lang = detectedLanguage;

        console.log(`[UniQPaidApp] ✅ Локализация инициализирована: ${detectedLanguage}`);

        // Показываем статус готовности
        if (window.appUtils) {
          const statusMessage = window.appLocalization ?
            window.appLocalization.get('status.initializing_app') :
            (detectedLanguage === 'ru' ? 'Инициализация приложения...' : 'Initializing application...');
          window.appUtils.showStatus(statusMessage, 'info');
        }

      } catch (error) {
        console.warn('[UniQPaidApp] ⚠️ Ошибка инициализации локализации:', error);
        // Продолжаем работу с русским языком по умолчанию
        document.documentElement.lang = 'ru';
      }
    } else {
      console.warn('[UniQPaidApp] ⚠️ Система локализации не найдена, используем русский по умолчанию');
    }
  }

  /**
   * Проверяет наличие необходимых элементов интерфейса (из оригинала)
   */
  checkRequiredElements() {
    const requiredElements = [
      'mainContentEl',
      'earnSectionEl',
      'friendsSectionEl',
      'userNameEl',
      'balanceAmountEl',
      'headerBalanceInfoEl',
      'statusMessageEl'
    ];

    // Проверяем основные элементы
    const missingElements = requiredElements.filter(elementName => !this.elements[elementName]);

    // Кнопки рекламы проверяем отдельно (они могут отсутствовать)
    const adButtons = ['watchAdButton', 'watchVideoButton', 'openLinkButton'];
    const missingAdButtons = adButtons.filter(buttonName => !this.elements[buttonName]);

    if (missingAdButtons.length > 0) {
      console.warn('[UniQPaidApp] ⚠️ Отсутствуют кнопки рекламы:', missingAdButtons);
    }

    return missingElements;
  }

  /**
   * Инициализирует базовые модули (из оригинала)
   */
  initializeBaseModules() {
    // Проверяем доступность необходимых модулей
    const requiredModules = ['balanceManager', 'appUtils', 'pageManager', 'audioManager'];
    const missingModules = requiredModules.filter(module => !window[module]);

    if (missingModules.length > 0) {
      console.error('🛑 Отсутствуют необходимые модули:', missingModules);
      console.log('🔍 Доступные модули в window:', Object.keys(window).filter(key => key.includes('Manager') || key.includes('Utils')));
      throw new Error(`Отсутствуют модули: ${missingModules.join(', ')}`);
    }

    // Инициализируем базовые модули
    if (window.balanceManager) window.balanceManager.init();
    if (window.pageManager) window.pageManager.init();
    if (window.audioManager) window.audioManager.init();

    // Показываем локализованное сообщение о загрузке
    if (window.appUtils) {
      const loadingMessage = window.appLocalization ?
        window.appLocalization.get('status.loading_modules') :
        'Загрузка данных...';
      window.appUtils.showStatus(loadingMessage, 'info');
    }
  }

  /**
   * Инициализирует продвинутые модули (из оригинала)
   */
  initializeAdvancedModules() {
    // Инициализируем модули в правильном порядке
    if (window.adsManagerFull) {
      console.log('[UniQPaidApp] 🎬 Инициализируем AdsManagerFull...');
      window.adsManagerFull.init();
    } else {
      console.warn('[UniQPaidApp] ⚠️ AdsManagerFull не найден');
    }

    if (window.referralManager) window.referralManager.init();
    if (window.calculatorManager) window.calculatorManager.init();
    if (window.withdrawalTabsManager) window.withdrawalTabsManager.init();
    if (window.withdrawalFormManager) window.withdrawalFormManager.init();
    if (window.withdrawalManager) window.withdrawalManager.init();

    // Инициализируем награды из админки
    if (window.rewardBadgesManager) {
      console.log('[UniQPaidApp] 🏆 Инициализируем награды из админки...');
      window.rewardBadgesManager.init();
    }

    // Инициализируем счетчики рекламы
    if (window.serverAdCountersManager) {
      console.log('[UniQPaidApp] 📊 Инициализируем серверные счетчики рекламы...');
      window.serverAdCountersManager.init();
    } else if (window.adCountersManager) {
      console.log('[UniQPaidApp] 📊 Инициализируем локальные счетчики рекламы (fallback)...');
      window.adCountersManager.init();
    }
  }

  /**
   * Финальная настройка интерфейса (из оригинала)
   */
  finalizeInterface() {
    // Инициализируем иконку и информацию о валюте по умолчанию (TON)
    if (window.updateCurrencyCardIcon) {
      window.updateCurrencyCardIcon('ton');
    }
    if (window.updateCurrencyInfo) {
      window.updateCurrencyInfo('ton');
    }

    // ИСПРАВЛЕНИЕ: Добавляем обработчик клика по зелёной кнопке
    this.setupActionButtonHandler();

    // Принудительное исправление лейблов карточки валют (из оригинала)
    setTimeout(() => {
      this.fixCurrencyCardLabels();
    }, 100);
  }

  /**
   * Настраивает обработчик клика по кнопке "Доступно для вывода"
   */
  setupActionButtonHandler() {
    const actionButton = document.getElementById('action-status');
    if (actionButton) {
      actionButton.addEventListener('click', () => {
        // Проверяем что кнопка кликабельна
        const availableText = window.appLocalization ?
          window.appLocalization.get('earnings.available_for_withdrawal_status') :
          '✅ Доступно для вывода';
        if (actionButton.textContent.includes(availableText) || actionButton.textContent.includes('✅')) {
          console.log('[UniQPaidApp] Клик по зелёной кнопке - переход к выплатам');

          // Получаем сумму из калькулятора
          const calcAmountInput = document.getElementById('calc-amount');
          const amount = calcAmountInput ? parseInt(calcAmountInput.value) || 0 : 0;

          // Переключаемся на вкладку выплат
          if (window.withdrawalTabsManager) {
            window.withdrawalTabsManager.switchToTab('withdrawal');
          }

          // Заполняем форму
          setTimeout(() => {
            const withdrawalAmountInput = document.getElementById('withdrawal-amount');
            const cryptoCurrencySelect = document.getElementById('crypto-currency');

            if (withdrawalAmountInput && amount > 0) {
              withdrawalAmountInput.value = amount;
              withdrawalAmountInput.dispatchEvent(new Event('input', { bubbles: true }));
            }

            // Устанавливаем валюту (TON по умолчанию)
            if (cryptoCurrencySelect) {
              const activeTab = document.querySelector('.currency-tab.active');
              const currency = activeTab ? activeTab.dataset.currency : 'ton';
              cryptoCurrencySelect.value = currency;
              cryptoCurrencySelect.dispatchEvent(new Event('change', { bubbles: true }));
            }

            console.log(`[UniQPaidApp] Форма заполнена: ${amount} монет`);
          }, 100);
        }
      });

      console.log('[UniQPaidApp] ✅ Обработчик зелёной кнопки установлен');
    } else {
      console.warn('[UniQPaidApp] ❌ Кнопка action-status не найдена');
    }
  }

  /**
   * Исправляет лейблы в карточке валют (из оригинала) - ПЕРЕВОДИМЫЕ ВЕРСИИ
   */
  fixCurrencyCardLabels() {
    console.log('🔧 Принудительное исправление лейблов карточки валют...');

    // Исправляем лейбл "Сумма к выводу" (индекс 1) - ПЕРЕВОДИМАЯ ВЕРСИЯ
    const withdrawalLabel = document.getElementById('withdrawal-amount-label');
    if (withdrawalLabel) {
      const translatedText = window.appLocalization ?
        window.appLocalization.get('earnings.withdrawal_amount') + ':' :
        'Сумма к выводу:';
      if (withdrawalLabel.textContent !== translatedText) {
        withdrawalLabel.textContent = translatedText;
        console.log('✅ Исправлен лейбл "Сумма к выводу"');
      }
    }

    // Исправляем лейбл "Сетевая комиссия" (индекс 2) - ПЕРЕВОДИМАЯ ВЕРСИЯ
    const networkFeeLabel = document.getElementById('network-fee-label');
    if (networkFeeLabel) {
      const translatedText = window.appLocalization ?
        window.appLocalization.get('earnings.network_fee') + ':' :
        'Сетевая комиссия:';
      if (networkFeeLabel.textContent !== translatedText) {
        networkFeeLabel.textContent = translatedText;
        console.log('✅ Исправлен лейбл "Сетевая комиссия"');
      }
    }

    // Исправляем лейбл "Минимальная сумма" (индекс 3) - ПЕРЕВОДИМАЯ ВЕРСИЯ
    const minimumLabel = document.getElementById('minimum-amount-label');
    if (minimumLabel) {
      const translatedText = window.appLocalization ?
        window.appLocalization.get('earnings.minimum_withdrawal') + ':' :
        'Минимум к выводу:';
      if (minimumLabel.textContent !== translatedText) {
        minimumLabel.textContent = translatedText;
        console.log('✅ Исправлен лейбл "Минимум к выводу"');
      }
    }
  }

  /**
   * Загружает данные пользователя и настройки (из оригинала)
   */
  async fetchUserDataAndSettings() {
    let initData = window.appUtils ? window.appUtils.getInitData() : null;

    // Если нет данных аутентификации, показываем ошибку
    if (!initData) {
      console.error('[UniQPaidApp] Нет данных аутентификации');
      throw new Error('Ошибка аутентификации: отсутствуют данные Telegram');
    }

    try {
      const response = await fetch(`${window.API_BASE_URL}/getUserData.php`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ initData: initData }),
      });

      if (!response.ok) {
        console.warn(`[UniQPaidApp] API недоступен (${response.status}), работаем в offline режиме`);
        this.setupOfflineMode();
        return;
      }

      const data = await response.json();
      if (data.error) {
        console.warn(`[UniQPaidApp] Ошибка API: ${data.error}, работаем в offline режиме`);
        this.setupOfflineMode();
        return;
      }

      // Успешная загрузка данных
      const user = window.Telegram.WebApp.initDataUnsafe?.user;
      const userNameEl = document.getElementById('user-name');
      if (userNameEl && user) {
          userNameEl.textContent = user.first_name || user.username || `User ID: ${user.id}`;
      }

      if (window.balanceManager) {
        window.balanceManager.updateBalance(data.balance || 0, 'server_init');
      }
      if (window.appSettings) {
        window.appSettings.update(data.settings || {});
      }

    } catch (error) {
      console.warn(`[UniQPaidApp] Ошибка подключения к API: ${error.message}, работаем в offline режиме`);
      this.setupOfflineMode();
    }
  }

  /**
   * Настраивает offline режим работы
   */
  setupOfflineMode() {
    console.log('[UniQPaidApp] Настройка offline режима');

    // Устанавливаем тестовые данные
    const userNameEl = document.getElementById('user-name');
    if (userNameEl) {
      const testUserName = window.appLocalization ?
        window.appLocalization.get('common.test_user') || 'Test User' :
        'Тестовый пользователь';
      userNameEl.textContent = testUserName;
    }

    if (window.balanceManager) {
      window.balanceManager.updateBalance(0, 'offline_mode');
    }

    if (window.appUtils) {
      const offlineMessage = window.appLocalization ?
        window.appLocalization.get('common.offline_mode') || 'Offline mode' :
        'Offline режим';
      window.appUtils.showStatus(offlineMessage, 'warning');
    }
  }
}

// ВАЖНО: ЭКСПОРТ КЛАССА В ГЛОБАЛЬНУЮ ОБЛАСТЬ WINDOW
window.UniQPaidApp = UniQPaidApp;

// === ТОЧКА ВХОДА ПРИЛОЖЕНИЯ (из оригинала) ===

/**
 * Инициализирует приложение после загрузки DOM
 */
async function initializeApp() {
  console.log('[Main] 🚀 Инициализация приложения...');

  // Загружаем актуальный курс конвертации из админки
  if (window.appSettings && typeof window.appSettings.loadConversionRate === 'function') {
    try {
      await window.appSettings.loadConversionRate();
      console.log('[Main] ✅ Курс конвертации загружен из админки');
    } catch (error) {
      console.warn('[Main] ⚠️ Ошибка загрузки курса конвертации, используем fallback:', error);
    }
  } else {
    console.warn('[Main] ⚠️ AppSettings не найден, используем fallback курс конвертации');
  }

  // Инициализируем основное приложение
  const app = new UniQPaidApp();
  app.init();

  console.log('[Main] ✅ Приложение инициализировано');
}

// Точка входа - запускаем приложение после загрузки DOM (из оригинала)
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
  });
} else {
  // DOM уже загружен
  initializeApp();
}

console.log('📦 [UniQPaidApp] Главный класс приложения определен с полной интеграцией.');