<?php
/**
 * api/get_conversion_rate.php
 * API для получения актуального курса конвертации монет
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

/**
 * Функция для получения актуальных значений из config.php
 */
if (!function_exists('getActualConfigValue')) {
    function getActualConfigValue($constantName, $defaultValue = null) {
        static $configContent = null;
        
        // Читаем файл только один раз за запрос
        if ($configContent === null) {
            $configFile = __DIR__ . '/config.php';
            $configContent = file_get_contents($configFile);
        }
        
        // Ищем определение константы в файле
        $patterns = [
            "/define\('$constantName',\s*([^)]+)\);/", // Обычное определение
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $configContent, $matches)) {
                $value = trim($matches[1], " '\"");
                
                // Обрабатываем разные типы значений
                if ($value === 'true') return true;
                if ($value === 'false') return false;
                if (is_numeric($value)) return floatval($value);
                return $value;
            }
        }
        
        return $defaultValue;
    }
}

try {
    // Получаем актуальный курс конвертации из config.php
    $conversionRate = getActualConfigValue('CONVERSION_RATE', 0.001);
    
    // Получаем дополнительные настройки
    $showFeesToUser = getActualConfigValue('SHOW_FEES_TO_USER', true);
    $minWithdrawalAmount = getActualConfigValue('MIN_WITHDRAWAL_AMOUNT', 100);
    $minBalanceForWithdrawal = getActualConfigValue('MIN_BALANCE_FOR_WITHDRAWAL', 1);
    
    // Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'conversion_rate' => $conversionRate,
        'coin_value' => $conversionRate, // Для совместимости
        'settings' => [
            'show_fees_to_user' => $showFeesToUser,
            'min_withdrawal_amount' => $minWithdrawalAmount,
            'min_balance_for_withdrawal' => $minBalanceForWithdrawal
        ],
        'examples' => [
            '1_coin_usd' => $conversionRate,
            '100_coins_usd' => $conversionRate * 100,
            '1000_coins_usd' => $conversionRate * 1000
        ],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log('Ошибка в get_conversion_rate.php: ' . $e->getMessage());
    
    // Возвращаем ошибку с fallback значениями
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения курса конвертации',
        'conversion_rate' => 0.001, // Fallback значение
        'coin_value' => 0.001
    ]);
}
?>
