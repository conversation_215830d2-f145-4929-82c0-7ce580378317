/**
 * === navigation-simple.js ===
 * Простая рабочая навигация как в оригинале
 */

/**
 * Простой класс навигации
 */
class SimpleNavigationManager {
  constructor() {
    this.isInitialized = false;
    this.isTransitioning = false;
    this.currentPageElement = null;
    this.currentPageId = null;
    
    // Элементы DOM
    this.pages = new Map();
    this.navButtons = new Map();
  }

  /**
   * Инициализация навигации
   */
  init() {
    if (this.isInitialized) {
      console.log('⚠️ SimpleNavigationManager уже инициализирован');
      return;
    }

    console.log('🚀 Инициализация простой навигации...');
    
    // Кешируем элементы
    this.cacheElements();
    
    // Настраиваем обработчики
    this.setupEventListeners();
    
    // Устанавливаем начальное состояние
    this.setInitialState();
    
    this.isInitialized = true;
    console.log('✅ Простая навигация инициализирована');
  }

  /**
   * Кеширование элементов DOM
   */
  cacheElements() {
    // Страницы
    const pageIds = ['main-content', 'earn-section', 'friends-section'];
    pageIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        this.pages.set(id, element);
        console.log(`✅ Страница "${id}" найдена`);
      } else {
        console.log(`❌ Страница "${id}" не найдена`);
      }
    });

    // Кнопки навигации
    const buttonIds = ['nav-home', 'nav-earn', 'nav-friends'];
    buttonIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        this.navButtons.set(id, element);
        console.log(`✅ Кнопка "${id}" найдена`);
      } else {
        console.log(`❌ Кнопка "${id}" не найдена`);
      }
    });
  }

  /**
   * Настройка обработчиков событий
   */
  setupEventListeners() {
    // Обработчики для кнопок навигации
    const navHome = this.navButtons.get('nav-home');
    const navEarn = this.navButtons.get('nav-earn');
    const navFriends = this.navButtons.get('nav-friends');

    if (navHome) {
      navHome.addEventListener('click', () => this.showMainContent());
    }
    
    if (navEarn) {
      navEarn.addEventListener('click', () => this.showEarnSection());
    }
    
    if (navFriends) {
      navFriends.addEventListener('click', () => this.showFriendsSection());
    }
  }

  /**
   * Установка начального состояния
   */
  setInitialState() {
    // Показываем главную страницу по умолчанию
    const mainPage = this.pages.get('main-content');
    const homeButton = this.navButtons.get('nav-home');

    if (mainPage && homeButton) {
      this.currentPageElement = mainPage;
      this.currentPageId = 'main-content';

      // Скрываем все страницы
      this.pages.forEach(page => {
        page.style.display = 'none';
        page.classList.remove('active-section');
        page.classList.add('page-hidden');
      });

      // Показываем главную
      mainPage.style.display = 'block';
      mainPage.classList.add('active-section');
      mainPage.classList.remove('page-hidden');

      // Активируем кнопку
      this.navButtons.forEach(btn => btn.classList.remove('active'));
      homeButton.classList.add('active');

      console.log('✅ Установлено начальное состояние: main-content');
    }
  }

  /**
   * Простое переключение страниц
   * @param {HTMLElement} targetPage - Целевая страница
   * @param {HTMLElement} targetButton - Целевая кнопка
   * @param {string} pageId - ID страницы
   */
  switchPageSimple(targetPage, targetButton, pageId) {
    if (!targetPage || !targetButton) {
      console.log('❌ Не найдены элементы для переключения');
      return;
    }

    if (this.currentPageElement === targetPage || this.isTransitioning) {
      console.log(`⚠️ Переключение отменено: уже на странице ${pageId} или идет переход`);
      return;
    }

    console.log(`🔄 Переключение: ${this.currentPageId} → ${pageId}`);
    this.isTransitioning = true;

    // Скрываем все страницы
    this.pages.forEach(page => {
      page.style.display = 'none';
      page.classList.remove('active-section');
      page.classList.add('page-hidden');
    });

    // Показываем целевую страницу
    targetPage.style.display = 'block';
    targetPage.classList.add('active-section');
    targetPage.classList.remove('page-hidden');

    // Обновляем кнопки
    this.navButtons.forEach(btn => btn.classList.remove('active'));
    targetButton.classList.add('active');

    // Обновляем состояние
    this.currentPageElement = targetPage;
    this.currentPageId = pageId;

    // Завершаем переход
    setTimeout(() => {
      this.isTransitioning = false;
      console.log(`✅ Переключение завершено: ${pageId}`);
      
      // Специальная логика для страницы заработка
      if (pageId === 'earn-section') {
        this.handleEarnSectionActivated();
      }
    }, 100);
  }

  /**
   * Обработка активации страницы заработка
   */
  handleEarnSectionActivated() {
    console.log('🎯 Страница заработка активирована');

    // Инициализируем табы если они есть, но НЕ активируем автоматически
    if (window.withdrawalTabsManager && window.withdrawalTabsManager.isInitialized) { // withdrawalTabsManager is a global instance
      setTimeout(() => {
        console.log('🔧 Убеждаемся что все разделы скрыты по умолчанию');
        window.withdrawalTabsManager.hideAllSections();
      }, 200);
    }

    // Инициализируем оригинальный калькулятор если он есть
    if (window.calculatorManager && window.calculatorManager.isInitialized) { // originalCalculatorManager was changed to calculatorManager
      setTimeout(() => {
        console.log('🔧 Обновляем оригинальный калькулятор');
        window.calculatorManager.updateBalance();
        window.calculatorManager.updateCalculatorDisplay(0);
      }, 300);
    }
  }

  /**
   * Показать главную страницу
   */
  showMainContent() {
    const page = this.pages.get('main-content');
    const button = this.navButtons.get('nav-home');
    this.switchPageSimple(page, button, 'main-content');
  }

  /**
   * Показать страницу заработка
   */
  showEarnSection() {
    const page = this.pages.get('earn-section');
    const button = this.navButtons.get('nav-earn');
    this.switchPageSimple(page, button, 'earn-section');
  }

  /**
   * Показать страницу друзей
   */
  showFriendsSection() {
    const page = this.pages.get('friends-section');
    const button = this.navButtons.get('nav-friends');
    this.switchPageSimple(page, button, 'friends-section');
  }

  /**
   * Получить статистику
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      currentPageId: this.currentPageId,
      pagesFound: this.pages.size,
      buttonsFound: this.navButtons.size,
      isTransitioning: this.isTransitioning
    };
  }
}

// Создаем глобальный экземпляр
const simpleNavigationManager = new SimpleNavigationManager();

// Экспорт в глобальную область видимости
window.SimpleNavigationManager = SimpleNavigationManager;
window.simpleNavigationManager = simpleNavigationManager;

// Функции для обратной совместимости
window.showMainContent = () => simpleNavigationManager.showMainContent();
window.showEarnSection = () => simpleNavigationManager.showEarnSection();
window.showFriendsSection = () => simpleNavigationManager.showFriendsSection();
window.initSimpleNavigation = () => simpleNavigationManager.init();

console.log('📦 [SimpleNavigationManager] Простая навигация загружена');
