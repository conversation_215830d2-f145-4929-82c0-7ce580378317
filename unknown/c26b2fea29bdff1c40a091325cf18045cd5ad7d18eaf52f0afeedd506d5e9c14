/**
 * 🔄 ЕДИНЫЙ МЕНЕДЖЕР ДАННЫХ ВАЛЮТ
 * Синхронизирует данные между калькулятором и формой выплат
 */

class CurrencyDataManager {
  constructor() {
    // ЕДИНЫЙ ИСТОЧНИК ДАННЫХ ВАЛЮТ (ОБНОВЛЕНО ПО РЕАЛЬНЫМ ДАННЫМ ИЗ КЭША)
    this.currencyData = {
      ton: {
        name: 'TON (Telegram)',
        minCoins: 1546,  // РЕАЛЬНЫЕ ДАННЫЕ ИЗ КЭША
        networkFee: 0.15,
        status: 'best',
        shortName: 'TON'
      },
      eth: {
        name: 'Ethereum (ETH)',
        minCoins: 257,   // РЕАЛЬНЫЕ ДАННЫЕ ИЗ КЭША
        networkFee: 0.25,
        status: 'expensive',
        shortName: 'ETH'
      },
      btc: {
        name: 'Bitcoin (BTC)',
        minCoins: 532,   // РЕАЛЬНЫЕ ДАННЫЕ ИЗ КЭША
        networkFee: 0.5,
        status: 'expensive',
        shortName: 'BTC'
      },
      usdttrc20: {
        name: 'USDT (TRC20)',
        minCoins: 14160, // РЕАЛЬНЫЕ ДАННЫЕ ИЗ КЭША
        networkFee: 5.58,
        status: 'available',
        shortName: 'USDT'
      },
      trx: {
        name: 'TRO<PERSON> (TRX)',
        minCoins: 1500,  // FALLBACK (нет в кэше)
        networkFee: 0.5,
        status: 'good',
        shortName: 'TRX'
      }
    };

    // Флаг для отслеживания загрузки данных с сервера
    this.isServerDataLoaded = false;

    console.log('[CurrencyDataManager] Инициализирован с реальными данными из кэша');

    // Автоматически загружаем актуальные данные с сервера
    this.loadServerData();
  }

  /**
   * Получить все данные валюты
   */
  getCurrencyData(currency = null) {
    if (currency) {
      return this.currencyData[currency] || null;
    }
    return this.currencyData;
  }

  /**
   * Получить минимум монет для валюты
   */
  getMinCoins(currency) {
    const data = this.currencyData[currency];
    return data ? data.minCoins : 1000;
  }

  /**
   * Получить комиссию для валюты
   */
  getNetworkFee(currency) {
    const data = this.currencyData[currency];
    return data ? data.networkFee : 1.0;
  }

  /**
   * Получить название валюты
   */
  getCurrencyName(currency) {
    const data = this.currencyData[currency];
    return data ? data.name : currency.toUpperCase();
  }

  /**
   * Получить короткое название валюты
   */
  getShortName(currency) {
    const data = this.currencyData[currency];
    return data ? data.shortName : currency.toUpperCase();
  }

  /**
   * Получить статус валюты
   */
  getCurrencyStatus(currency) {
    const data = this.currencyData[currency];
    return data ? data.status : 'available';
  }

  /**
   * Проверить достаточность монет для минимума
   */
  checkMinimumAmount(currency, coinAmount) {
    const minCoins = this.getMinCoins(currency);
    return {
      sufficient: coinAmount >= minCoins,
      minCoins: minCoins,
      shortage: Math.max(0, minCoins - coinAmount)
    };
  }

  /**
   * Рассчитать сумму после комиссии
   */
  calculateAfterFee(currency, coinAmount) {
    const originalUsd = coinAmount * 0.001; // 1 монета = $0.001
    const networkFee = this.getNetworkFee(currency);
    const afterFee = Math.max(0, originalUsd - networkFee);
    
    return {
      originalUsd: originalUsd,
      networkFee: networkFee,
      afterFee: afterFee,
      feeExceedsAmount: afterFee <= 0
    };
  }

  /**
   * Получить полную информацию для отображения
   */
  getDisplayInfo(currency, coinAmount) {
    const data = this.currencyData[currency];
    if (!data) {
      return {
        success: false,
        error: 'Неизвестная валюта'
      };
    }

    // Проверяем минимум
    const minimumCheck = this.checkMinimumAmount(currency, coinAmount);
    if (!minimumCheck.sufficient) {
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_for_withdrawal') :
        'Недостаточно для вывода';
      const coinsText = window.appLocalization ?
        window.appLocalization.get('currency.coins') :
        'монет';
      return {
        success: false,
        type: 'insufficient_minimum',
        message: insufficientText,
        details: `Минимум для ${data.shortName}: ${minimumCheck.minCoins.toLocaleString()} ${coinsText}`,
        minCoins: minimumCheck.minCoins,
        shortage: minimumCheck.shortage
      };
    }

    // Рассчитываем комиссию
    const feeCalc = this.calculateAfterFee(currency, coinAmount);
    if (feeCalc.feeExceedsAmount) {
      const feeExceedsText = window.appLocalization ?
        window.appLocalization.get('earnings.fee_exceeds_amount') :
        'Комиссия превышает сумму';
      return {
        success: false,
        type: 'fee_exceeds_amount',
        message: feeExceedsText,
        details: `Комиссия $${feeCalc.networkFee} больше суммы $${feeCalc.originalUsd.toFixed(2)}`,
        networkFee: feeCalc.networkFee,
        originalUsd: feeCalc.originalUsd
      };
    }

    // Успешный расчет
    return {
      success: true,
      type: 'success',
      currency: currency,
      currencyName: data.name,
      shortName: data.shortName,
      coinAmount: coinAmount,
      originalUsd: feeCalc.originalUsd,
      networkFee: feeCalc.networkFee,
      afterFee: feeCalc.afterFee,
      minCoins: data.minCoins,
      status: data.status
    };
  }

  /**
   * Обновить данные валют (для загрузки с сервера)
   */
  updateCurrencyData(newData) {
    if (newData && typeof newData === 'object') {
      Object.assign(this.currencyData, newData);
      console.log('[CurrencyDataManager] Данные валют обновлены:', this.currencyData);
      
      // Уведомляем другие компоненты об обновлении
      this.notifyUpdate();
    }
  }

  /**
   * Уведомить компоненты об обновлении данных
   */
  notifyUpdate() {
    // Уведомляем калькулятор
    if (window.calculatorManager && window.calculatorManager.onCurrencyDataUpdate) {
      window.calculatorManager.onCurrencyDataUpdate(this.currencyData);
    }

    // Уведомляем форму выплат
    if (window.withdrawalFormManager && window.withdrawalFormManager.onCurrencyDataUpdate) {
      window.withdrawalFormManager.onCurrencyDataUpdate(this.currencyData);
    }

    console.log('[CurrencyDataManager] Компоненты уведомлены об обновлении данных');
  }

  /**
   * Получить список всех валют
   */
  getAllCurrencies() {
    return Object.keys(this.currencyData);
  }

  /**
   * Получить валюты отсортированные по статусу
   */
  getCurrenciesByStatus() {
    const currencies = Object.entries(this.currencyData);
    
    const statusOrder = { 'best': 1, 'good': 2, 'expensive': 3 };
    
    return currencies.sort((a, b) => {
      const statusA = statusOrder[a[1].status] || 4;
      const statusB = statusOrder[b[1].status] || 4;
      return statusA - statusB;
    });
  }

  /**
   * Форматировать сумму для отображения
   */
  formatDisplayAmount(currency, coinAmount, cryptoAmount) {
    const info = this.getDisplayInfo(currency, coinAmount);
    
    if (!info.success) {
      return {
        display: info.message,
        title: info.details,
        type: info.type
      };
    }

    const formattedCrypto = cryptoAmount ? cryptoAmount.toFixed(8) : '0.00000000';
    const display = `$${info.afterFee.toFixed(2)} (${info.shortName} ${formattedCrypto})`;
    const title = `Вы получите: ${formattedCrypto} ${info.shortName} (~$${info.afterFee.toFixed(2)})`;

    return {
      display: display,
      title: title,
      type: 'success'
    };
  }

  /**
   * НОВЫЙ МЕТОД: Загрузка актуальных данных с сервера
   */
  async loadServerData() {
    try {
      console.log('[CurrencyDataManager] Загружаем актуальные данные с сервера...');

      const response = await fetch(`${window.API_BASE_URL || ''}/getCachedCurrencyData.php`);

      if (response.ok) {
        const data = await response.json();

        if (data.success && data.currencies) {
          console.log('[CurrencyDataManager] Получены данные с сервера:', data.currencies);

          // Обновляем данные, сохраняя структуру
          Object.keys(data.currencies).forEach(currency => {
            const serverData = data.currencies[currency];

            if (this.currencyData[currency]) {
              // Обновляем существующую валюту
              this.currencyData[currency].minCoins = serverData.minCoins || serverData.minCoinsBase || this.currencyData[currency].minCoins;
              this.currencyData[currency].networkFee = serverData.networkFee || this.currencyData[currency].networkFee;
              this.currencyData[currency].name = serverData.name || this.currencyData[currency].name;
              this.currencyData[currency].status = serverData.status || this.currencyData[currency].status;
            } else {
              // Добавляем новую валюту
              this.currencyData[currency] = {
                name: serverData.name || currency.toUpperCase(),
                minCoins: serverData.minCoins || serverData.minCoinsBase || 1000,
                networkFee: serverData.networkFee || 1.0,
                status: serverData.status || 'available',
                shortName: this.getShortNameFromCurrency(currency)
              };
            }
          });

          this.isServerDataLoaded = true;
          console.log('[CurrencyDataManager] Данные успешно обновлены с сервера');

          // Уведомляем компоненты об обновлении
          this.notifyUpdate();

        } else {
          console.warn('[CurrencyDataManager] Сервер вернул некорректные данные:', data);
        }
      } else {
        console.warn('[CurrencyDataManager] Ошибка HTTP при загрузке данных:', response.status);
      }

    } catch (error) {
      console.warn('[CurrencyDataManager] Ошибка загрузки данных с сервера:', error);
      console.log('[CurrencyDataManager] Используем fallback данные');
    }
  }

  /**
   * Получить короткое название из кода валюты
   */
  getShortNameFromCurrency(currency) {
    const shortNames = {
      'eth': 'ETH',
      'btc': 'BTC',
      'ton': 'TON',
      'usdttrc20': 'USDT',
      'trx': 'TRX',
      'ltc': 'LTC',
      'bch': 'BCH',
      'xrp': 'XRP',
      'ada': 'ADA',
      'dot': 'DOT'
    };
    return shortNames[currency] || currency.toUpperCase();
  }

  /**
   * Получить отладочную информацию
   */
  getDebugInfo() {
    return {
      currencyCount: Object.keys(this.currencyData).length,
      currencies: this.getAllCurrencies(),
      isServerDataLoaded: this.isServerDataLoaded,
      dataStructure: this.currencyData
    };
  }
}

// Создаем глобальный экземпляр
window.currencyDataManager = new CurrencyDataManager();

// Экспорт для совместимости
window.getCurrencyData = (currency) => window.currencyDataManager.getCurrencyData(currency);
window.getMinCoins = (currency) => window.currencyDataManager.getMinCoins(currency);
window.getNetworkFee = (currency) => window.currencyDataManager.getNetworkFee(currency);

console.log('🔄 [CurrencyDataManager] Единый менеджер данных валют загружен');
console.log('📊 [CurrencyDataManager] Доступные валюты:', window.currencyDataManager.getAllCurrencies());
