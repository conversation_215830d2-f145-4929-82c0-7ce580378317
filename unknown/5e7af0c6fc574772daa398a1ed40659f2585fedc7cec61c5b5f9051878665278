# Исправление стилей заголовков таблиц в разделе Безопасность

## Проблема
Заголовки всех таблиц в разделе "Безопасность" админки не были видны из-за конфликта CSS стилей. Общий стиль `.table th` устанавливал светлый фон, который перекрывал темный фон класса `table-dark`.

## Решение
Добавлены специфичные CSS стили для всех таблиц в разделе Безопасность с приоритетом `!important`.

### Затронутые таблицы:
1. **Журнал фрода** (`#fraud-log-content`)
2. **Заблокированные устройства** (`#blocked-devices-content`)
3. **Отпечатки устройств** (`#fingerprints-content`)
4. **RichAds логи** (`#richadds-log-content`)

### Добавленные стили:

```css
/* Стили для заголовков всех таблиц в разделе Безопасность */
#fraud-log-content .table-dark th,
#blocked-devices-content .table-dark th,
#fingerprints-content .table-dark th,
#richadds-log-content .table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    border-color: #454d55 !important;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 12px 8px;
}

#fraud-log-content .table thead.table-dark th,
#blocked-devices-content .table thead.table-dark th,
#fingerprints-content .table thead.table-dark th,
#richadds-log-content .table thead.table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    border-bottom: 2px solid #454d55 !important;
}

/* Дополнительные стили для обеспечения видимости заголовков */
.table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
}

/* Переопределяем общий стиль для таблиц с темными заголовками */
.table thead.table-dark th {
    background-color: #343a40 !important;
    color: #ffffff !important;
    border-color: #454d55 !important;
}
```

## Результат
- ✅ Все заголовки таблиц теперь имеют **темный фон** (#343a40)
- ✅ Текст заголовков **белый** и хорошо читаемый
- ✅ Добавлена **жирность шрифта** (font-weight: 600)
- ✅ **Центрирование текста** в заголовках
- ✅ **Увеличенный padding** (12px 8px) для лучшего внешнего вида
- ✅ **Контрастная граница** между заголовком и телом таблицы

## Файлы изменены
- `api/admin/security.php` - добавлены CSS стили

## Тестирование
1. Откройте админку → Безопасность
2. Проверьте все вкладки в разделе "Антифрод система"
3. Убедитесь, что заголовки всех таблиц хорошо видны

## Совместимость
Стили используют Bootstrap 5 классы и переменные, полностью совместимы с существующим дизайном админки.
