<?php
/**
 * api/admin/save_settings.php
 * Универсальный API для сохранения всех настроек админки
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    echo json_encode([
        'success' => false,
        'error' => 'Не авторизован'
    ]);
    exit;
}

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'error' => 'Метод не поддерживается'
    ]);
    exit;
}

try {
    // Получаем данные из POST
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Если данные не в JSON, пробуем $_POST
    if (!$input) {
        $input = $_POST;
    }
    
    // Проверяем тип действия
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'change_credentials':
            $result = changeCredentials($input);
            break;
            
        case 'change_settings':
            $result = changeAppSettings($input);
            break;
            
        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log('Ошибка в save_settings.php: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера: ' . $e->getMessage()
    ]);
}

/**
 * Изменение учетных данных администратора
 */
function changeCredentials($input) {
    $newUsername = trim($input['new_username'] ?? '');
    $newPassword = $input['new_password'] ?? '';
    $confirmPassword = $input['confirm_password'] ?? '';
    
    if (empty($newUsername)) {
        return ['success' => false, 'error' => 'Имя пользователя не может быть пустым'];
    }
    
    if (empty($newPassword)) {
        return ['success' => false, 'error' => 'Пароль не может быть пустым'];
    }
    
    if ($newPassword !== $confirmPassword) {
        return ['success' => false, 'error' => 'Пароли не совпадают'];
    }
    
    if (changeAdminCredentials($newUsername, $newPassword)) {
        // Обновляем имя пользователя в сессии
        $_SESSION['admin_username'] = $newUsername;
        
        return [
            'success' => true,
            'message' => 'Учетные данные успешно изменены'
        ];
    } else {
        return ['success' => false, 'error' => 'Не удалось изменить учетные данные'];
    }
}

/**
 * Изменение настроек приложения
 */
function changeAppSettings($input) {
    // Читаем config.php
    $configFile = __DIR__ . '/../config.php';
    $configContent = file_get_contents($configFile);
    
    if ($configContent === false) {
        throw new Exception('Не удалось прочитать config.php');
    }
    
    $updated = false;
    $updatedSettings = [];
    
    // Обновляем награды за типы рекламы
    if (isset($input['ad_reward_native_banner']) && is_numeric($input['ad_reward_native_banner'])) {
        $value = intval($input['ad_reward_native_banner']);
        $pattern = "/define\('AD_REWARD_NATIVE_BANNER',\s*\d+\);/";
        $replacement = "define('AD_REWARD_NATIVE_BANNER', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['ad_reward_native_banner'] = $value;
    }
    
    if (isset($input['ad_reward_interstitial']) && is_numeric($input['ad_reward_interstitial'])) {
        $value = intval($input['ad_reward_interstitial']);
        $pattern = "/define\('AD_REWARD_INTERSTITIAL',\s*\d+\);/";
        $replacement = "define('AD_REWARD_INTERSTITIAL', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['ad_reward_interstitial'] = $value;
    }
    
    if (isset($input['ad_reward_rewarded_video']) && is_numeric($input['ad_reward_rewarded_video'])) {
        $value = intval($input['ad_reward_rewarded_video']);
        $pattern = "/define\('AD_REWARD_REWARDED_VIDEO',\s*\d+\);/";
        $replacement = "define('AD_REWARD_REWARDED_VIDEO', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['ad_reward_rewarded_video'] = $value;
    }
    
    // Обновляем REFERRAL_BONUS_PERCENT
    if (isset($input['referral_bonus_percent']) && is_numeric($input['referral_bonus_percent'])) {
        $inputPercent = floatval($input['referral_bonus_percent']); // Исходное значение в процентах
        $referralBonusPercent = $inputPercent / 100; // Конвертируем в десятичную дробь для config.php
        $pattern = "/define\('REFERRAL_BONUS_PERCENT',\s*[\d\.]+\);/";
        $replacement = "define('REFERRAL_BONUS_PERCENT', $referralBonusPercent);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['referral_bonus_percent'] = $inputPercent; // Возвращаем исходное значение в процентах
    }
    
    // Обновляем MIN_WITHDRAWAL_AMOUNT
    if (isset($input['min_withdrawal_amount']) && is_numeric($input['min_withdrawal_amount'])) {
        $minWithdrawalAmount = intval($input['min_withdrawal_amount']);
        $pattern = "/define\('MIN_WITHDRAWAL_AMOUNT',\s*\d+\);/";
        $replacement = "define('MIN_WITHDRAWAL_AMOUNT', $minWithdrawalAmount);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['min_withdrawal_amount'] = $minWithdrawalAmount;
    }
    
    // Обновляем MIN_BALANCE_FOR_WITHDRAWAL
    if (isset($input['min_balance_for_withdrawal']) && is_numeric($input['min_balance_for_withdrawal'])) {
        $minBalanceForWithdrawal = intval($input['min_balance_for_withdrawal']);
        $pattern = "/define\('MIN_BALANCE_FOR_WITHDRAWAL',\s*\d+\);/";
        $replacement = "define('MIN_BALANCE_FOR_WITHDRAWAL', $minBalanceForWithdrawal);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['min_balance_for_withdrawal'] = $minBalanceForWithdrawal;
    }
    
    // Обновляем CONVERSION_RATE
    if (isset($input['conversion_rate']) && is_numeric($input['conversion_rate'])) {
        $conversionRate = floatval($input['conversion_rate']);
        $pattern = "/define\('CONVERSION_RATE',\s*[\d\.]+\);/";
        $replacement = "define('CONVERSION_RATE', $conversionRate);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['conversion_rate'] = $conversionRate;
    }
    
    // Обновляем SHOW_FEES_TO_USER
    $showFeesToUser = isset($input['show_fees_to_user']) ? 'true' : 'false';
    $pattern = "/define\('SHOW_FEES_TO_USER',\s*(true|false)\);/";
    $replacement = "define('SHOW_FEES_TO_USER', $showFeesToUser);";
    $configContent = preg_replace($pattern, $replacement, $configContent);
    $updated = true;
    $updatedSettings['show_fees_to_user'] = $showFeesToUser;
    
    // Обновляем NOWPAYMENTS_EMAIL
    if (isset($input['nowpayments_email']) && !empty($input['nowpayments_email'])) {
        $email = addslashes($input['nowpayments_email']);
        $pattern = "/define\('NOWPAYMENTS_EMAIL',\s*'[^']*'\);/";
        $replacement = "define('NOWPAYMENTS_EMAIL', '$email');";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['nowpayments_email'] = $email;
    }
    
    // Обновляем NOWPAYMENTS_PASSWORD
    if (isset($input['nowpayments_password']) && !empty($input['nowpayments_password'])) {
        $password = addslashes($input['nowpayments_password']);
        $pattern = "/define\('NOWPAYMENTS_PASSWORD',\s*'[^']*'\);/";
        $replacement = "define('NOWPAYMENTS_PASSWORD', '$password');";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['nowpayments_password'] = '***';
    }
    
    // Обновляем NOWPAYMENTS_API_KEY
    if (isset($input['nowpayments_api_key']) && !empty($input['nowpayments_api_key'])) {
        $apiKey = addslashes($input['nowpayments_api_key']);
        $pattern = "/define\('NOWPAYMENTS_API_KEY',\s*'[^']*'\);/";
        $replacement = "define('NOWPAYMENTS_API_KEY', '$apiKey');";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['nowpayments_api_key'] = '***';
    }

    // Обновляем пользовательские лимиты рекламы
    if (isset($input['user_ad_limit_native_banner']) && is_numeric($input['user_ad_limit_native_banner'])) {
        $value = intval($input['user_ad_limit_native_banner']);
        $pattern = "/define\('USER_AD_LIMIT_NATIVE_BANNER',\s*\d+\);/";
        $replacement = "define('USER_AD_LIMIT_NATIVE_BANNER', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['user_ad_limit_native_banner'] = $value;
    }

    if (isset($input['user_ad_limit_interstitial']) && is_numeric($input['user_ad_limit_interstitial'])) {
        $value = intval($input['user_ad_limit_interstitial']);
        $pattern = "/define\('USER_AD_LIMIT_INTERSTITIAL',\s*\d+\);/";
        $replacement = "define('USER_AD_LIMIT_INTERSTITIAL', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['user_ad_limit_interstitial'] = $value;
    }

    if (isset($input['user_ad_limit_rewarded_video']) && is_numeric($input['user_ad_limit_rewarded_video'])) {
        $value = intval($input['user_ad_limit_rewarded_video']);
        $pattern = "/define\('USER_AD_LIMIT_REWARDED_VIDEO',\s*\d+\);/";
        $replacement = "define('USER_AD_LIMIT_REWARDED_VIDEO', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
        $updatedSettings['user_ad_limit_rewarded_video'] = $value;
    }
    
    // Сохраняем файл с проверкой синтаксиса
    if ($updated) {
        // Проверяем синтаксис PHP перед сохранением
        $tempFile = $configFile . '.tmp';
        file_put_contents($tempFile, $configContent);

        // Проверяем синтаксис
        $output = [];
        $returnCode = 0;
        exec("php -l \"$tempFile\" 2>&1", $output, $returnCode);

        if ($returnCode === 0) {
            // Синтаксис корректен, сохраняем
            if (rename($tempFile, $configFile)) {
                // Логируем изменения
                error_log('Настройки приложения обновлены: ' . json_encode($updatedSettings));

                return [
                    'success' => true,
                    'message' => 'Настройки успешно сохранены',
                    'updated' => $updatedSettings
                ];
            } else {
                unlink($tempFile);
                throw new Exception('Не удалось сохранить config.php');
            }
        } else {
            // Синтаксическая ошибка, не сохраняем
            unlink($tempFile);
            throw new Exception('Ошибка синтаксиса в конфигурации: ' . implode(' ', $output));
        }
    } else {
        return [
            'success' => false,
            'error' => 'Нет изменений для сохранения'
        ];
    }
}
?>
