<?php
/**
 * api/calculateCrypto.php
 * Простой API для расчета криптовалютных сумм
 * Используется как fallback для калькулятора
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/FeeCalculator.php';

try {
    // Получение входных данных
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if (!isset($input['coins_amount']) || !isset($input['currency'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Отсутствуют обязательные параметры: coins_amount, currency']);
        exit;
    }

    $coinsAmount = intval($input['coins_amount']);
    $currency = strtolower(trim($input['currency']));

    if ($coinsAmount <= 0) {
        echo json_encode([
            'success' => true,
            'crypto_amount' => 0,
            'usd_amount' => 0,
            'message' => 'Сумма должна быть больше 0'
        ]);
        exit;
    }

    // Используем FeeCalculator для точного расчета
    error_log("calculateCrypto INFO: Расчет для {$coinsAmount} монет -> {$currency}");

    $calculator = FeeCalculator::getInstance();
    $result = $calculator->calculateWithdrawalAmount($coinsAmount, $currency);

    // Проверяем результат расчета
    if (!$result['success']) {
        error_log("calculateCrypto ERROR: " . $result['error']);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'error_code' => $result['error_code'] ?? 'CALCULATION_ERROR',
            'crypto_amount' => 0,
            'usd_amount' => $coinsAmount * CONVERSION_RATE,
            'details' => $result
        ]);
        exit;
    }

    // Успешный результат
    echo json_encode([
        'success' => true,
        'crypto_amount' => $result['user_crypto_amount'],
        'usd_amount' => $coinsAmount * CONVERSION_RATE,
        'fee_details' => [
            'nowpayments_fee' => $result['nowpayments_fee'] ?? 0,
            'user_receives' => $result['user_crypto_amount'],
            'total_sent_to_nowpayments' => $result['nowpayments_crypto_amount']
        ],
        'calculation_method' => 'fee_calculator_v2'
    ]);

} catch (Exception $e) {
    error_log("calculateCrypto CRITICAL ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при расчете',
        'details' => $e->getMessage()
    ]);
}
?>
