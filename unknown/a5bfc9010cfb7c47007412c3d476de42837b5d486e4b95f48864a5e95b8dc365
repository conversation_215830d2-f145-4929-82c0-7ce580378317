<?php
/**
 * api/admin/update_withdrawal_status.php
 * Ручное обновление статуса выплаты администратором.
 */

ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/../db_mock.php';

session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['error' => 'Доступ запрещен']);
    exit;
}

$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

$userId = $input['user_id'] ?? null;
$withdrawalIndex = $input['withdrawal_index'] ?? null;
$newStatus = $input['new_status'] ?? null;

if (!$userId || !isset($withdrawalIndex) || !$newStatus) {
    http_response_code(400);
    echo json_encode(['error' => 'Неверные параметры запроса']);
    exit;
}

$possible_statuses = ['waiting', 'processing', 'sending', 'finished', 'failed', 'rejected', 'cancelled', 'expired'];
if (!in_array($newStatus, $possible_statuses)) {
    http_response_code(400);
    echo json_encode(['error' => 'Недопустимый статус']);
    exit;
}

$userData = loadUserData();

if (!isset($userData[$userId]['withdrawals'][$withdrawalIndex])) {
    http_response_code(404);
    echo json_encode(['error' => 'Запись о выводе не найдена']);
    exit;
}

$userData[$userId]['withdrawals'][$withdrawalIndex]['status'] = $newStatus;
$userData[$userId]['withdrawals'][$withdrawalIndex]['manual_update'] = true;
$userData[$userId]['withdrawals'][$withdrawalIndex]['updated_at'] = date('Y-m-d H:i:s');

if (saveUserData($userData)) {
    echo json_encode(['success' => true]);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Не удалось сохранить изменения']);
}
