/**
 * НОВЫЙ ЧИСТЫЙ КАЛЬКУЛЯТОР - Решает все задачи без лишнего кода
 */
class CleanCalculator {
  constructor() {
    this.selectedCurrency = 'ton'; // По умолчанию TON
    this.elements = {};
    this.init();
  }

  init() {
    this.bindElements();
    this.bindEvents();
    this.hideBalanceTooltip(); // Скрываем облачко сразу
    console.log('🧮 [CleanCalculator] Чистый калькулятор загружен');
  }

  bindElements() {
    this.elements = {
      coinAmountInput: document.getElementById('coin-amount'),
      balanceCheck: document.getElementById('balance-check'),
      actionStatus: document.getElementById('action-status'),
      requirementValues: document.querySelectorAll('.requirement-value'),
      warningSection: document.getElementById('warning-section'),
      minimumInfoItem: document.getElementById('minimum-info-item'),
      missingInfoItem: document.getElementById('missing-info-item'),
      minimumRequiredInfo: document.getElementById('minimum-required-info'),
      amountMissingInfo: document.getElementById('amount-missing-info')
    };
  }

  bindEvents() {
    // Обработчик ввода суммы
    if (this.elements.coinAmountInput) {
      this.elements.coinAmountInput.addEventListener('input', (e) => {
        const amount = parseFloat(e.target.value) || 0;
        this.updateCalculator(amount);
      });
    }

    // Обработчики переключения валют
    document.querySelectorAll('.currency-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const currency = e.target.dataset.currency;
        if (currency) {
          this.selectedCurrency = currency;
          this.updateCalculator(parseFloat(this.elements.coinAmountInput?.value) || 0);
        }
      });
    });
  }

  /**
   * ГЛАВНЫЙ МЕТОД: Обновляет весь калькулятор
   */
  async updateCalculator(coinAmount) {
    const userBalance = window.balanceManager?.getBalance() || 0;

    // Получаем актуальный курс конвертации из настроек
    const conversionRate = window.appSettings?.getCoinValue() || 0.001;
    const dollarAmount = coinAmount * conversionRate;

    // Обновляем все поля
    this.updateRequirementValues(coinAmount, dollarAmount, userBalance);
    this.updateActionButton(coinAmount, userBalance);
    this.updateMinimumWarning(coinAmount);
  }

  /**
   * Получает минимальные суммы для валют
   */
  getCurrencyMinimums() {
    return {
      ton: { minCoins: 1489, name: 'TON (Telegram)' },
      eth: { minCoins: 3000, name: 'Ethereum (ETH)' },
      btc: { minCoins: 5000, name: 'Bitcoin (BTC)' },
      usdttrc20: { minCoins: 12924, name: 'USDT (TRC20)' },
      trx: { minCoins: 1500, name: 'TRON (TRX)' }
    };
  }

  /**
   * Обновляет значения в карточке (Сумма, Доллары, Комиссия, Вы получите, Эффективность)
   */
  async updateRequirementValues(coinAmount, dollarAmount, userBalance) {
    const values = this.elements.requirementValues;
    if (!values || values.length < 5) return;

    // Получаем минимальную сумму для текущей валюты
    const minimums = this.getCurrencyMinimums();
    const currentCurrency = minimums[this.selectedCurrency];
    const minCoins = currentCurrency ? currentCurrency.minCoins : 0;

    // Проверяем, достаточно ли средств для минимального вывода
    const hasMinimumAmount = coinAmount >= minCoins;
    const hasSufficientBalance = coinAmount <= userBalance;

    // Управляем отображением области "Вы получите"
    this.updateResultSectionVisibility(coinAmount, hasMinimumAmount, hasSufficientBalance);

    // 0: Сумма в монетах
    const coinsText = window.appLocalization ?
      window.appLocalization.get('currency.coins') :
      'монет';
    values[0].textContent = coinAmount > 0 ? `${coinAmount} ${coinsText}` : '-';

    // 1: Сумма в долларах
    values[1].textContent = coinAmount > 0 ? `$${dollarAmount.toFixed(2)}` : '-';

    // 2: Комиссия (фиксированная)
    values[2].textContent = '$0.25';

    // 3: Вы получите (с префиксом и криптосуммой)
    if (coinAmount === 0) {
      values[3].textContent = '-';
      values[4].textContent = '-';
    } else if (coinAmount > userBalance) {
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds') :
        'Недостаточно средств';
      values[3].textContent = insufficientText;
      values[4].textContent = '-';
    } else {
      await this.updateFinalAmountWithCrypto(values, coinAmount, dollarAmount);
    }
  }

  /**
   * КЛЮЧЕВОЙ МЕТОД: Обновляет "Вы получите" с префиксом и криптосуммой
   */
  async updateFinalAmountWithCrypto(values, coinAmount, dollarAmount) {
    try {
      // Получаем криптосумму
      const cryptoAmount = await this.getCryptoAmount(coinAmount);
      const afterFeeUsd = dollarAmount - 0.25; // Вычитаем комиссию $0.25
      const efficiency = afterFeeUsd > 0 ? ((afterFeeUsd / dollarAmount) * 100) : 0;
      
      if (cryptoAmount > 0 && afterFeeUsd > 0) {
        const prefix = this.getCurrencyPrefix();
        // ФОРМАТ: $X.XX (PREFIX CRYPTO_AMOUNT)
        values[3].textContent = `$${afterFeeUsd.toFixed(2)} (${prefix} ${cryptoAmount.toFixed(8)})`;
        values[4].textContent = `${efficiency.toFixed(1)}%`;
      } else {
        const errorText = window.appLocalization ?
          window.appLocalization.get('earnings.calculation_error') :
          'Ошибка расчёта';
        values[3].textContent = errorText;
        values[4].textContent = '0%';
      }
    } catch (error) {
      console.error('[CleanCalculator] Ошибка:', error);
      values[3].textContent = 'Ошибка расчёта';
      values[4].textContent = '0%';
    }
  }

  /**
   * Получает криптосумму через API или fallback
   */
  async getCryptoAmount(coinAmount) {
    try {
      // Пробуем API
      const response = await fetch(`${window.API_BASE_URL}/calculateWithdrawalAmount.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          coins_amount: coinAmount,
          currency: this.selectedCurrency
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.crypto_amount) {
          return parseFloat(data.crypto_amount);
        }
      }
    } catch (error) {
      console.warn('[CleanCalculator] API недоступен, используем fallback');
    }
    
    // Fallback расчёт с вычетом сетевых комиссий
    return this.calculateFallbackCrypto(coinAmount);
  }

  /**
   * Fallback расчёт с вычетом сетевых комиссий для пользователя
   */
  calculateFallbackCrypto(coinAmount) {
    // Получаем актуальный курс конвертации из настроек
    const conversionRate = window.appSettings?.getCoinValue() || 0.001;
    const dollarAmount = coinAmount * conversionRate;
    
    // Курсы валют
    const rates = {
      eth: 2670,
      btc: 97000, 
      ton: 6.7,
      usdttrc20: 1,
      trx: 0.25
    };
    
    // Сетевые комиссии (вычитаем для пользователя)
    const networkFees = {
      eth: 0.002,
      btc: 0.0001,
      ton: 0.05,
      usdttrc20: 1.5,
      trx: 1.0
    };
    
    const rate = rates[this.selectedCurrency] || 1;
    const fee = networkFees[this.selectedCurrency] || 0;
    
    const grossAmount = dollarAmount / rate; // До вычета комиссии
    const netAmount = grossAmount - fee;     // После вычета комиссии (для пользователя)
    
    return Math.max(0, netAmount);
  }

  /**
   * Обновляет кнопку действия (зелёная кликабельная)
   */
  updateActionButton(coinAmount, userBalance) {
    const button = this.elements.actionStatus;
    if (!button) return;

    if (coinAmount === 0) {
      const enterAmountText = window.appLocalization ?
        window.appLocalization.get('earnings.enter_amount_for_calculation') :
        'Введите сумму для расчета';
      button.textContent = enterAmountText;
      button.className = 'action-status-card neutral';
      button.style.cursor = 'default';
      button.onclick = null;
    } else if (coinAmount > userBalance) {
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_balance') :
        '❌ Недостаточно средств';
      button.textContent = insufficientText;
      button.className = 'action-status-card insufficient';
      button.style.cursor = 'default';
      button.onclick = null;
    } else {
      // ЗЕЛЁНАЯ КЛИКАБЕЛЬНАЯ КНОПКА (без проверок минималок)
      const availableText = window.appLocalization ?
        window.appLocalization.get('earnings.available_for_withdrawal_status') :
        '✅ Доступно для вывода';
      button.textContent = availableText;
      button.className = 'action-status-card available';
      button.style.cursor = 'pointer';
      button.style.backgroundColor = '#00ff88';
      button.style.color = '#1a1a1a';
      button.style.fontWeight = 'bold';
      
      // Переход к форме выплат
      button.onclick = () => this.switchToWithdrawal(coinAmount);
    }
  }

  /**
   * Переключение на форму выплат с автозаполнением
   */
  switchToWithdrawal(coinAmount) {
    // Переключаем вкладку
    const withdrawalTab = document.querySelector('[data-tab="withdrawal"]');
    if (withdrawalTab) withdrawalTab.click();

    // Заполняем форму
    setTimeout(() => {
      const amountInput = document.getElementById('withdrawal-amount');
      const currencySelect = document.getElementById('crypto-currency');
      
      if (amountInput) {
        amountInput.value = coinAmount;
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
      }
      
      if (currencySelect) {
        currencySelect.value = this.selectedCurrency;
        currencySelect.dispatchEvent(new Event('change', { bubbles: true }));
      }
      
      console.log(`[CleanCalculator] Переход: ${coinAmount} монет, ${this.selectedCurrency}`);
    }, 100);
  }

  /**
   * Управляет отображением области "Вы получите" в зависимости от условий
   */
  updateResultSectionVisibility(coinAmount, hasMinimumAmount, hasSufficientBalance) {
    const resultSection = document.querySelector('.result-section');
    const mainInfoSection = document.querySelector('.main-info-section');

    // Скрываем области, если:
    // 1. Сумма равна 0
    // 2. Недостаточно средств для минимального вывода
    // 3. Недостаточно средств на балансе
    const shouldHide = coinAmount === 0 || !hasMinimumAmount || !hasSufficientBalance;

    // Скрываем/показываем область "Вы получите"
    if (resultSection) {
      if (shouldHide) {
        resultSection.style.display = 'none';
      } else {
        resultSection.style.display = 'block';
      }
    }

    // Скрываем/показываем основную информацию (Минимум к выводу, Сумма к выводу, Сетевая комиссия)
    if (mainInfoSection) {
      if (shouldHide) {
        mainInfoSection.style.display = 'none';
      } else {
        mainInfoSection.style.display = 'block';
      }
    }

    console.log(`[CleanCalculator] Sections visibility: ${shouldHide ? 'hidden' : 'visible'} (amount: ${coinAmount}, hasMin: ${hasMinimumAmount}, hasBalance: ${hasSufficientBalance})`);
  }

  /**
   * Получает префикс валюты
   */
  getCurrencyPrefix() {
    const prefixes = {
      eth: 'ETH',
      btc: 'BTC',
      ton: 'TON',
      usdttrc20: 'USDT',
      trx: 'TRX'
    };
    return prefixes[this.selectedCurrency] || this.selectedCurrency.toUpperCase();
  }

  /**
   * Обновляет информацию о минимальной сумме в карточке валюты
   */
  updateMinimumWarning(coinAmount) {
    if (!this.elements.warningSection) return;

    const minimums = this.getCurrencyMinimums();
    const currentCurrency = minimums[this.selectedCurrency];

    if (!currentCurrency) {
      this.elements.warningSection.style.display = 'none';
      return;
    }

    const minCoins = currentCurrency.minCoins;

    // Показываем секцию предупреждения только если введена сумма и она меньше минимума
    if (coinAmount > 0 && coinAmount < minCoins) {
      const minUsd = (minCoins * 0.001).toFixed(2);
      const missingCoins = minCoins - coinAmount;
      const missingUsd = (missingCoins * 0.001).toFixed(2);

      // Обновляем текст с переносами строк
      if (this.elements.minimumRequiredInfo) {
        const coinsText = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        this.elements.minimumRequiredInfo.innerHTML = `${minCoins.toLocaleString()} ${coinsText}<br>($${minUsd})`;
      }

      if (this.elements.amountMissingInfo) {
        const coinsText2 = window.appLocalization ?
          window.appLocalization.get('currency.coins') :
          'монет';
        this.elements.amountMissingInfo.innerHTML = `${missingCoins.toLocaleString()} ${coinsText2}<br>($${missingUsd})`;
      }

      // Показываем всю секцию предупреждения
      this.elements.warningSection.style.display = 'block';
    } else {
      // Скрываем секцию предупреждения
      this.elements.warningSection.style.display = 'none';
    }
  }

  /**
   * Скрывает облачко с подсказками
   */
  hideBalanceTooltip() {
    if (this.elements.balanceCheck) {
      this.elements.balanceCheck.style.display = 'none';
    }
  }
}

// Инициализация
window.cleanCalculator = new CleanCalculator();

// Экспорт для совместимости
window.calculateCryptoAmount = (coinAmount, currency) => {
  return window.cleanCalculator.getCryptoAmount(coinAmount);
};

console.log('🚀 [CleanCalculator] Новый чистый калькулятор готов!');
