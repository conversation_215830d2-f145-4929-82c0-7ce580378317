<?php
/**
 * bot_config_loader.php
 * Загрузчик настроек бота из JSON файла
 */

/**
 * Загрузка настроек бота из JSON файла
 */
function loadBotSettings() {
    // Используем глобальный кэш для возможности сброса
    if (isset($GLOBALS['bot_settings_cache']) && $GLOBALS['bot_settings_cache'] !== null) {
        return $GLOBALS['bot_settings_cache'];
    }

    $settings = null;
        $settingsFile = __DIR__ . '/../database/bot_settings.json';
        
        // Настройки по умолчанию (fallback) - НЕ ИСПОЛЬЗУЕМ ЗАХАРДКОЖЕННЫЕ URL!
        $defaultSettings = [
            'TELEGRAM_BOT_TOKEN' => '**********************************************',
            'BOT_USERNAME' => 'uniqpaid_paid_bot',
            'WEBAPP_URL' => 'https://app.uniqpaid.com/test4',
            'WEBHOOK_URL' => 'https://app.uniqpaid.com/test4/bot/webhook.php',
            'SUPPORT_BOT_TOKEN' => '7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M',
            'SUPPORT_BOT_USERNAME' => 'uniqpaid_support_bot',
            'SUPPORT_WEBHOOK_URL' => 'https://app.uniqpaid.com/test4/api/admin/support_webhook.php'
        ];
        
        if (file_exists($settingsFile)) {
            $content = file_get_contents($settingsFile);
            $loadedSettings = json_decode($content, true);
            
            if ($loadedSettings && is_array($loadedSettings)) {
                // Объединяем с настройками по умолчанию
                $settings = array_merge($defaultSettings, $loadedSettings);
            } else {
                error_log("Ошибка загрузки настроек бота из JSON: " . json_last_error_msg());
                $settings = $defaultSettings;
            }
        } else {
            error_log("Файл настроек бота не найден: " . $settingsFile);
            $settings = $defaultSettings;
        }

    // Сохраняем в глобальный кэш
    $GLOBALS['bot_settings_cache'] = $settings;
    return $settings;
}

/**
 * Получение конкретной настройки бота
 */
function getBotSetting($key, $default = null) {
    $settings = loadBotSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * Сохранение настроек бота в JSON файл
 */
function saveBotSettings($newSettings, $updatedBy = null) {
    $settingsFile = __DIR__ . '/../database/bot_settings.json';
    
    // Загружаем текущие настройки
    $currentSettings = loadBotSettings();
    
    // Объединяем с новыми настройками
    $updatedSettings = array_merge($currentSettings, $newSettings);
    
    // Добавляем метаданные
    $updatedSettings['last_updated'] = date('Y-m-d H:i:s');
    $updatedSettings['updated_by'] = $updatedBy;
    
    // Сохраняем в файл
    $result = file_put_contents(
        $settingsFile, 
        json_encode($updatedSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
    );
    
    if ($result !== false) {
        // Сбрасываем кэш (обнуляем статическую переменную)
        $GLOBALS['bot_settings_cache'] = null;
        return true;
    }
    
    return false;
}

/**
 * Определение констант бота на основе загруженных настроек
 */
function defineBotConstants() {
    $settings = loadBotSettings();
    
    // Основные настройки бота
    if (!defined('TELEGRAM_BOT_TOKEN')) {
        define('TELEGRAM_BOT_TOKEN', $settings['TELEGRAM_BOT_TOKEN']);
    }
    if (!defined('BOT_TOKEN')) {
        define('BOT_TOKEN', $settings['TELEGRAM_BOT_TOKEN']);
    }
    if (!defined('BOT_USERNAME')) {
        define('BOT_USERNAME', $settings['BOT_USERNAME']);
    }
    if (!defined('WEBAPP_URL')) {
        define('WEBAPP_URL', $settings['WEBAPP_URL']);
    }
    if (!defined('WEBHOOK_URL')) {
        define('WEBHOOK_URL', $settings['WEBHOOK_URL']);
    }
    
    // Настройки бота поддержки
    if (!defined('SUPPORT_BOT_TOKEN')) {
        define('SUPPORT_BOT_TOKEN', $settings['SUPPORT_BOT_TOKEN']);
    }
    if (!defined('SUPPORT_BOT_USERNAME')) {
        define('SUPPORT_BOT_USERNAME', $settings['SUPPORT_BOT_USERNAME']);
    }
    if (!defined('SUPPORT_WEBHOOK_URL')) {
        define('SUPPORT_WEBHOOK_URL', $settings['SUPPORT_WEBHOOK_URL']);
    }
    
    // Производные константы
    if (!defined('TELEGRAM_API_URL')) {
        define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . $settings['TELEGRAM_BOT_TOKEN'] . '/');
    }
    if (!defined('SUPPORT_TELEGRAM_API_URL')) {
        define('SUPPORT_TELEGRAM_API_URL', 'https://api.telegram.org/bot' . $settings['SUPPORT_BOT_TOKEN'] . '/');
    }
}
?>
