<?php
/**
 * api/admin/test_session.php
 * Тестовый endpoint для проверки сессии
 */

session_start();

header('Content-Type: application/json; charset=utf-8');

$response = [
    'session_id' => session_id(),
    'session_data' => $_SESSION,
    'admin_logged_in' => $_SESSION['admin_logged_in'] ?? null,
    'is_authenticated' => isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true,
    'cookies' => $_COOKIE,
    'headers' => getallheaders(),
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
