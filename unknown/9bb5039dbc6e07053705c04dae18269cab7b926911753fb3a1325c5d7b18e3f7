<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 🌞 СУПЕР ЯРКИЙ ЖЁЛТЫЙ ФОН -->
    <linearGradient id="yellowBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF59D;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFF59D;stop-opacity:1" />
    </linearGradient>

    <!-- 💛 ЖЁЛТЫЙ ГРАДИЕНТ ДЛЯ КРИПТОВАЛЮТ -->
    <radialGradient id="yellowCrypto" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFF8D;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </radialGradient>

    <!-- 🌸 РОЗОВЫЙ ГРАДИЕНТ -->
    <linearGradient id="pinkGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF80AB;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AD1457;stop-opacity:1" />
    </linearGradient>

    <!-- 🍃 САЛАТОВЫЙ ГРАДИЕНТ -->
    <linearGradient id="limeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#CCFF90;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8BC34A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#689F38;stop-opacity:1" />
    </linearGradient>

    <!-- 🌟 ТЁМНЫЙ ГРАДИЕНТ ДЛЯ ТЕКСТА -->
    <linearGradient id="darkText" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1A1A1A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2D2D2D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1A1A1A;stop-opacity:1" />
    </linearGradient>

    <!-- ✨ ФИЛЬТРЫ БЕЗ РАЗМЫТИЙ -->
    <filter id="sharpShadow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="2" dy="2" stdDeviation="0" flood-color="#1A1A1A" flood-opacity="0.8"/>
    </filter>
  </defs>

  <!-- 🌞 ЯРКИЙ ЖЁЛТЫЙ ФОН -->
  <rect width="600" height="400" fill="url(#yellowBg)"/>

  <!-- 🌈 КРУТЫЕ УГЛОВЫЕ АКЦЕНТЫ -->
  <polygon points="0,0 80,0 0,80" fill="url(#pinkGrad)" opacity="0.9"/>
  <polygon points="600,0 520,0 600,80" fill="url(#limeGrad)" opacity="0.9"/>
  <polygon points="0,400 80,400 0,320" fill="url(#limeGrad)" opacity="0.9"/>
  <polygon points="600,400 520,400 600,320" fill="url(#pinkGrad)" opacity="0.9"/>

  <!-- 🎨 ДИЗАЙНЕРСКИЕ ЛИНИИ -->
  <rect x="30" y="30" width="160" height="8" fill="url(#pinkGrad)" opacity="0.9"/>
  <rect x="410" y="30" width="160" height="8" fill="url(#limeGrad)" opacity="0.9"/>
  <rect x="30" y="362" width="160" height="8" fill="url(#limeGrad)" opacity="0.9"/>
  <rect x="410" y="362" width="160" height="8" fill="url(#pinkGrad)" opacity="0.9"/>

  <!-- 🌟 ГЛАВНЫЙ ЗАГОЛОВОК UniQPaid -->
  <text x="300" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="url(#darkText)" filter="url(#sharpShadow)">UniQPaid</text>

  <!-- 💰 ПОДЗАГОЛОВОК -->
  <text x="300" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="600" fill="#1A1A1A" opacity="0.9">💰 Crypto Rewards • Instant Payouts 🚀</text>

  <!-- 🪙 ЦЕНТРАЛЬНАЯ ГЛАВНАЯ МОНЕТА -->
  <circle cx="300" cy="220" r="80" fill="url(#yellowCrypto)" stroke="url(#pinkGrad)" stroke-width="8" filter="url(#sharpShadow)"/>
  <circle cx="300" cy="220" r="65" fill="none" stroke="url(#limeGrad)" stroke-width="6" opacity="0.9"/>
  <circle cx="300" cy="220" r="50" fill="none" stroke="#1A1A1A" stroke-width="3" opacity="0.8"/>
  <text x="300" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="65" font-weight="bold" fill="#1A1A1A">$</text>

  <!-- 💛 ЖЁЛТЫЕ КРИПТОВАЛЮТЫ -->
  <!-- TON (верх-лево) -->
  <circle cx="150" cy="160" r="40" fill="url(#yellowCrypto)" stroke="url(#pinkGrad)" stroke-width="5"/>
  <circle cx="150" cy="160" r="30" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <!-- TON иконка -->
  <circle cx="150" cy="160" r="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <path d="M136 148l14 14M164 148l-14 14" stroke="#1A1A1A" stroke-width="3" fill="none"/>
  <circle cx="150" cy="160" r="8" fill="#1A1A1A" opacity="0.4"/>

  <!-- USDT (верх-право) -->
  <circle cx="450" cy="160" r="40" fill="url(#yellowCrypto)" stroke="url(#limeGrad)" stroke-width="5"/>
  <circle cx="450" cy="160" r="30" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <!-- USDT иконка -->
  <circle cx="450" cy="160" r="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <rect x="440" y="148" width="20" height="6" fill="#1A1A1A"/>
  <rect x="442" y="154" width="16" height="12" fill="none" stroke="#1A1A1A" stroke-width="3"/>

  <!-- BTC (низ-лево) -->
  <circle cx="150" cy="280" r="40" fill="url(#yellowCrypto)" stroke="url(#limeGrad)" stroke-width="5"/>
  <circle cx="150" cy="280" r="30" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <!-- Bitcoin иконка -->
  <circle cx="150" cy="280" r="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <path d="M138 270h12M138 280h14M138 290h12M138 270h8a4 4 0 0 1 0 8M138 280h10a4 4 0 0 1 0 8" fill="none" stroke="#1A1A1A" stroke-width="3"/>

  <!-- ETH (низ-право) -->
  <circle cx="450" cy="280" r="40" fill="url(#yellowCrypto)" stroke="url(#pinkGrad)" stroke-width="5"/>
  <circle cx="450" cy="280" r="30" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <!-- Ethereum иконка -->
  <polygon points="450,262 438,280 450,288 462,280" fill="#1A1A1A" opacity="0.9"/>
  <polygon points="450,288 438,280 450,298 462,280" fill="#1A1A1A" opacity="0.7"/>

  <!-- 🔗 КРУТЫЕ СОЕДИНИТЕЛЬНЫЕ ЛИНИИ -->
  <line x1="190" y1="175" x2="260" y2="205" stroke="url(#pinkGrad)" stroke-width="6" opacity="0.9"/>
  <line x1="410" y1="175" x2="340" y2="205" stroke="url(#limeGrad)" stroke-width="6" opacity="0.9"/>
  <line x1="190" y1="265" x2="260" y2="235" stroke="url(#limeGrad)" stroke-width="6" opacity="0.9"/>
  <line x1="410" y1="265" x2="340" y2="235" stroke="url(#pinkGrad)" stroke-width="6" opacity="0.9"/>

  <!-- 🌸 РОЗОВЫЕ ДИЗАЙНЕРСКИЕ ЭЛЕМЕНТЫ -->
  <circle cx="80" cy="120" r="15" fill="url(#pinkGrad)" opacity="0.9"/>
  <circle cx="80" cy="280" r="15" fill="url(#pinkGrad)" opacity="0.9"/>
  <polygon points="100,200 115,220 100,240 85,220" fill="url(#pinkGrad)" opacity="0.8"/>
  <polygon points="180,80 195,100 180,120 165,100" fill="url(#pinkGrad)" opacity="0.8"/>
  <polygon points="180,320 195,340 180,360 165,340" fill="url(#pinkGrad)" opacity="0.8"/>

  <!-- 🍃 САЛАТОВЫЕ ДИЗАЙНЕРСКИЕ ЭЛЕМЕНТЫ -->
  <circle cx="520" cy="120" r="15" fill="url(#limeGrad)" opacity="0.9"/>
  <circle cx="520" cy="280" r="15" fill="url(#limeGrad)" opacity="0.9"/>
  <polygon points="500,200 515,220 500,240 485,220" fill="url(#limeGrad)" opacity="0.8"/>
  <polygon points="420,80 435,100 420,120 405,100" fill="url(#limeGrad)" opacity="0.8"/>
  <polygon points="420,320 435,340 420,360 405,340" fill="url(#limeGrad)" opacity="0.8"/>

  <!-- ⭐ ДОПОЛНИТЕЛЬНЫЕ ЗВЁЗДОЧКИ -->
  <polygon points="250,140 260,160 270,140 260,120" fill="url(#pinkGrad)" opacity="0.7"/>
  <polygon points="350,140 360,160 370,140 360,120" fill="url(#limeGrad)" opacity="0.7"/>
  <polygon points="250,300 260,320 270,300 260,280" fill="url(#limeGrad)" opacity="0.7"/>
  <polygon points="350,300 360,320 370,300 360,280" fill="url(#pinkGrad)" opacity="0.7"/>

  <!-- 🎯 КРУТЫЕ ИНФОРМАЦИОННЫЕ БЛОКИ -->
  <!-- Левый блок - розовый -->
  <rect x="20" y="330" width="170" height="50" rx="25" fill="url(#pinkGrad)" opacity="0.95"/>
  <rect x="23" y="333" width="164" height="44" rx="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <circle cx="50" cy="355" r="8" fill="#1A1A1A" opacity="0.7"/>
  <text x="105" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1A1A1A">Watch Ads</text>

  <!-- Правый блок - салатовый -->
  <rect x="410" y="330" width="170" height="50" rx="25" fill="url(#limeGrad)" opacity="0.95"/>
  <rect x="413" y="333" width="164" height="44" rx="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <circle cx="550" cy="355" r="8" fill="#1A1A1A" opacity="0.7"/>
  <text x="495" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1A1A1A">Instant Payouts</text>

  <!-- Центральный блок - жёлтый -->
  <rect x="215" y="330" width="170" height="50" rx="25" fill="url(#yellowCrypto)" opacity="0.95"/>
  <rect x="218" y="333" width="164" height="44" rx="22" fill="none" stroke="#1A1A1A" stroke-width="2"/>
  <circle cx="245" cy="355" r="8" fill="#1A1A1A" opacity="0.7"/>
  <text x="300" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1A1A1A">Referrals 10%</text>

  <!-- 💫 ФИНАЛЬНЫЕ ДЕКОРАТИВНЫЕ ЭЛЕМЕНТЫ -->
  <circle cx="300" cy="140" r="6" fill="url(#pinkGrad)" opacity="0.8"/>
  <circle cx="300" cy="300" r="6" fill="url(#limeGrad)" opacity="0.8"/>
  <circle cx="200" cy="220" r="4" fill="url(#limeGrad)" opacity="0.7"/>
  <circle cx="400" cy="220" r="4" fill="url(#pinkGrad)" opacity="0.7"/>
</svg>
