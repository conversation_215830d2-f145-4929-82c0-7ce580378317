<?php
/**
 * api/log_click.php
 * Безопасный API эндпоинт для логирования ВСЕХ кликов по рекламным кнопкам для аналитики.
 * Этот скрипт НЕ начисляет награды.
 */

header('Content-Type: application/json');

// Базовая валидация
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Метод не разрешен']);
    exit;
}

$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['adType']) || !isset($input['clickType'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Недостаточно данных для логирования']);
    exit;
}

// Подключаем validate_initdata.php для извлечения ID пользователя
require_once __DIR__ . '/validate_initdata.php';

$userId = 0;
if (isset($input['initData']) && !empty($input['initData'])) {
    // Используем "расслабленную" валидацию, чтобы просто извлечь ID, не блокируя запрос
    $validatedData = validateTelegramInitData($input['initData'], true); // true = relaxed mode
    if ($validatedData && isset($validatedData['user']['id'])) {
        $userId = intval($validatedData['user']['id']);
    }
}

$logFile = __DIR__ . '/../database/analytics_clicks.json';

$logEntry = [
    'timestamp' => time(),
    'user_id' => $userId,
    'ad_type' => $input['adType'],
    'click_type' => $input['clickType'],
    'reason' => $input['reason'] ?? ''
];

$logLine = json_encode($logEntry) . "\n";

// Записываем в лог
file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);

// Отвечаем успехом
http_response_code(200);
echo json_encode(['success' => true, 'message' => 'Клик залогирован']);
