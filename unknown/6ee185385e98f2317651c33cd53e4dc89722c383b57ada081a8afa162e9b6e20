/**
 * js/advanced-fraud-manager.js
 * Расширенный менеджер антифрод системы для Фазы 4
 * 
 * Интегрирует:
 * - AdvancedDeviceFingerprint
 * - Автоматическую блокировку высокорисковых устройств
 * - Серверную синхронизацию данных fingerprinting
 * - Мониторинг и логирование
 */

class AdvancedFraudManager {
    constructor() {
        this.advancedFingerprint = null;
        this.fraudBlocker = null;
        this.vpnDetector = null;
        this.selfReferralDetector = null;
        
        this.fingerprintData = null;
        this.riskAssessment = null;
        this.isInitialized = false;
        
        console.log('[AdvancedFraudManager] 🛡️ Инициализация расширенной антифрод системы');
    }
    
    /**
     * Инициализирует расширенную антифрод систему
     */
    async initialize(initData) {
        try {
            console.log('[AdvancedFraudManager] 🚀 Запуск расширенной инициализации...');
            
            // Инициализируем компоненты
            await this.initializeComponents();
            
            // Генерируем расширенный отпечаток устройства
            console.log('[AdvancedFraudManager] 📊 Генерация расширенного отпечатка устройства...');
            this.fingerprintData = await this.advancedFingerprint.generateAdvancedFingerprint();
            
            // Выполняем анализ рисков
            this.riskAssessment = await this.performRiskAssessment(initData);
            
            // Проверяем необходимость блокировки
            await this.checkAndBlock();
            
            // Синхронизируем с сервером
            await this.syncWithServer(initData);
            
            this.isInitialized = true;
            console.log('[AdvancedFraudManager] ✅ Расширенная антифрод система инициализирована');
            
            return {
                success: true,
                fingerprint: this.fingerprintData.fingerprint,
                riskScore: this.fingerprintData.riskScore,
                isHighRisk: this.fingerprintData.isHighRisk,
                shouldBlock: this.fingerprintData.shouldBlock
            };
            
        } catch (error) {
            console.error('[AdvancedFraudManager] ❌ Ошибка инициализации:', error);
            
            // Fallback инициализация
            return await this.fallbackInitialization(initData);
        }
    }
    
    /**
     * Инициализирует все компоненты системы
     */
    async initializeComponents() {
        // Инициализируем расширенный fingerprinting
        if (window.AdvancedDeviceFingerprint) {
            this.advancedFingerprint = new window.AdvancedDeviceFingerprint();
            console.log('[AdvancedFraudManager] ✅ AdvancedDeviceFingerprint инициализирован');
        } else {
            throw new Error('AdvancedDeviceFingerprint не найден');
        }
        
        // Инициализируем блокировщик
        if (window.FraudBlocker) {
            this.fraudBlocker = new window.FraudBlocker();
            console.log('[AdvancedFraudManager] ✅ FraudBlocker инициализирован');
        }
        
        // Инициализируем VPN детектор
        if (window.VPNDetector) {
            this.vpnDetector = new window.VPNDetector();
            console.log('[AdvancedFraudManager] ✅ VPNDetector инициализирован');
        }
        
        // Инициализируем детектор самореферралов
        if (window.SelfReferralDetector) {
            this.selfReferralDetector = new window.SelfReferralDetector();
            console.log('[AdvancedFraudManager] ✅ SelfReferralDetector инициализирован');
        }
    }
    
    /**
     * Выполняет комплексный анализ рисков
     */
    async performRiskAssessment(initData) {
        const assessment = {
            totalRiskScore: 0,
            riskFactors: [],
            recommendations: [],
            blockingReasons: []
        };
        
        try {
            // 1. Анализ расширенного fingerprint
            if (this.fingerprintData) {
                assessment.totalRiskScore += this.fingerprintData.riskScore;
                assessment.riskFactors.push({
                    type: 'device_fingerprint',
                    score: this.fingerprintData.riskScore,
                    indicators: this.fingerprintData.suspiciousIndicators
                });
                
                if (this.fingerprintData.shouldBlock) {
                    assessment.blockingReasons.push('high_risk_device_fingerprint');
                }
            }
            
            // 2. VPN проверка
            if (this.vpnDetector) {
                const vpnResult = await this.vpnDetector.detectVPN();
                if (vpnResult.isVPN) {
                    assessment.totalRiskScore += 50;
                    assessment.riskFactors.push({
                        type: 'vpn_detected',
                        score: 50,
                        details: vpnResult
                    });
                    assessment.blockingReasons.push('vpn_usage_detected');
                }
            }
            
            // 3. Проверка самореферралов
            if (this.selfReferralDetector && initData) {
                const selfReferralResult = await this.selfReferralDetector.analyzeReferralRelations(
                    initData, 
                    this.fingerprintData.fingerprint
                );
                
                if (selfReferralResult.shouldBlock) {
                    assessment.totalRiskScore += 40;
                    assessment.riskFactors.push({
                        type: 'self_referral_detected',
                        score: 40,
                        details: selfReferralResult
                    });
                    assessment.blockingReasons.push('self_referral_violation');
                }
            }
            
            // 4. Дополнительные проверки
            await this.performAdditionalChecks(assessment);
            
            // 5. Генерируем рекомендации
            this.generateRecommendations(assessment);
            
            console.log(`[AdvancedFraudManager] 📊 Risk Assessment Complete. Total Score: ${assessment.totalRiskScore}`);
            
            return assessment;
            
        } catch (error) {
            console.error('[AdvancedFraudManager] ❌ Ошибка анализа рисков:', error);
            
            // Возвращаем безопасную оценку
            return {
                totalRiskScore: 75, // Высокий риск при ошибке
                riskFactors: [{ type: 'assessment_error', score: 75 }],
                recommendations: ['manual_review_required'],
                blockingReasons: ['assessment_failed']
            };
        }
    }
    
    /**
     * Выполняет дополнительные проверки безопасности
     */
    async performAdditionalChecks(assessment) {
        // Проверка времени загрузки страницы
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        if (loadTime < 100) {
            assessment.totalRiskScore += 15;
            assessment.riskFactors.push({
                type: 'suspicious_load_time',
                score: 15,
                details: { loadTime }
            });
        }
        
        // Проверка наличия DevTools
        if (this.detectDevTools()) {
            assessment.totalRiskScore += 10;
            assessment.riskFactors.push({
                type: 'devtools_detected',
                score: 10
            });
        }
        
        // Проверка размера viewport
        const viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };
        
        if (viewport.width < 300 || viewport.height < 300) {
            assessment.totalRiskScore += 10;
            assessment.riskFactors.push({
                type: 'suspicious_viewport',
                score: 10,
                details: viewport
            });
        }
    }
    
    /**
     * Детектирует открытые DevTools
     */
    detectDevTools() {
        try {
            const threshold = 160;
            const widthThreshold = window.outerWidth - window.innerWidth > threshold;
            const heightThreshold = window.outerHeight - window.innerHeight > threshold;
            
            return widthThreshold || heightThreshold;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Генерирует рекомендации на основе анализа
     */
    generateRecommendations(assessment) {
        if (assessment.totalRiskScore >= 85) {
            assessment.recommendations.push('immediate_block');
        } else if (assessment.totalRiskScore >= 70) {
            assessment.recommendations.push('enhanced_monitoring');
        } else if (assessment.totalRiskScore >= 50) {
            assessment.recommendations.push('additional_verification');
        } else {
            assessment.recommendations.push('normal_access');
        }
        
        // Специфичные рекомендации
        const hasVPN = assessment.riskFactors.some(f => f.type === 'vpn_detected');
        if (hasVPN) {
            assessment.recommendations.push('vpn_blocking_required');
        }
        
        const hasSelfReferral = assessment.riskFactors.some(f => f.type === 'self_referral_detected');
        if (hasSelfReferral) {
            assessment.recommendations.push('referral_investigation_required');
        }
    }
    
    /**
     * Проверяет необходимость блокировки и выполняет её
     */
    async checkAndBlock() {
        try {
            const shouldBlock = this.shouldBlockUser();
            
            if (shouldBlock.block) {
                console.log('[AdvancedFraudManager] 🚫 Блокировка пользователя:', shouldBlock.reason);
                
                if (this.fraudBlocker) {
                    await this.fraudBlocker.blockUser(shouldBlock.reason, {
                        riskScore: this.riskAssessment?.totalRiskScore || this.fingerprintData?.riskScore || 0,
                        indicators: this.fingerprintData?.suspiciousIndicators || [],
                        blockingReasons: this.riskAssessment?.blockingReasons || []
                    });
                }
                
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('[AdvancedFraudManager] ❌ Ошибка проверки блокировки:', error);
            return false;
        }
    }
    
    /**
     * Определяет необходимость блокировки пользователя
     */
    shouldBlockUser() {
        // Проверяем fingerprint данные
        if (this.fingerprintData?.shouldBlock) {
            return {
                block: true,
                reason: 'high_risk_device_detected',
                details: this.fingerprintData.suspiciousIndicators
            };
        }
        
        // Проверяем общий risk score
        const totalRisk = this.riskAssessment?.totalRiskScore || this.fingerprintData?.riskScore || 0;
        if (totalRisk >= 85) {
            return {
                block: true,
                reason: 'high_risk_score',
                details: { riskScore: totalRisk }
            };
        }
        
        // Проверяем критичные индикаторы
        if (this.riskAssessment?.blockingReasons?.length > 0) {
            return {
                block: true,
                reason: 'critical_security_violations',
                details: this.riskAssessment.blockingReasons
            };
        }
        
        return { block: false };
    }
    
    /**
     * Синхронизирует данные с сервером
     */
    async syncWithServer(initData) {
        try {
            const syncData = {
                action: 'sync_advanced_fingerprint',
                fingerprint: this.fingerprintData.fingerprint,
                components: this.fingerprintData.components,
                riskScore: this.fingerprintData.riskScore,
                riskAssessment: this.riskAssessment,
                initData: initData,
                timestamp: Date.now()
            };
            
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(syncData)
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('[AdvancedFraudManager] ✅ Данные синхронизированы с сервером');
                return result;
            } else {
                console.warn('[AdvancedFraudManager] ⚠️ Ошибка синхронизации с сервером');
            }
            
        } catch (error) {
            console.error('[AdvancedFraudManager] ❌ Ошибка синхронизации:', error);
        }
    }
    
    /**
     * Fallback инициализация при ошибках
     */
    async fallbackInitialization(initData) {
        console.log('[AdvancedFraudManager] 🔄 Запуск fallback инициализации...');
        
        try {
            // Пытаемся использовать базовый FraudManager
            if (window.FraudManager) {
                const basicFraudManager = new window.FraudManager();
                const result = await basicFraudManager.initialize(initData);
                
                console.log('[AdvancedFraudManager] ✅ Fallback инициализация завершена');
                return {
                    success: true,
                    fallback: true,
                    ...result
                };
            }
            
            // Если и базовый не работает, возвращаем минимальный результат
            return {
                success: false,
                fallback: true,
                error: 'all_fraud_systems_failed'
            };
            
        } catch (error) {
            console.error('[AdvancedFraudManager] ❌ Fallback инициализация не удалась:', error);
            return {
                success: false,
                fallback: true,
                error: error.message
            };
        }
    }
    
    /**
     * Получает текущий статус системы
     */
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            fingerprint: this.fingerprintData?.fingerprint || null,
            riskScore: this.fingerprintData?.riskScore || 0,
            isHighRisk: this.fingerprintData?.isHighRisk || false,
            shouldBlock: this.fingerprintData?.shouldBlock || false,
            components: {
                advancedFingerprint: !!this.advancedFingerprint,
                fraudBlocker: !!this.fraudBlocker,
                vpnDetector: !!this.vpnDetector,
                selfReferralDetector: !!this.selfReferralDetector
            }
        };
    }
}

// Экспортируем класс
window.AdvancedFraudManager = AdvancedFraudManager;

console.log('✅ [AdvancedFraudManager] Расширенный менеджер антифрод системы загружен');
