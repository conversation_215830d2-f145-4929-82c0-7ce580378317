/**
 * Базовые данные о валютах с пользовательскими минимумами
 * Эти данные используются сразу при загрузке, а затем обновляются динамически
 * Обновлено: 2025-06-17 01:09:54
 */

// Пользовательские минимумы (удобные для пользователей)
const defaultCurrencyData = {
  ton: {
    name: 'TON (Telegram)',
    minCoins: 500,        // $0.50 - удобно для пользователей
    networkFee: 0.15,
    status: 'best'
  },
  eth: {
    name: 'Ethereum (ETH)',
    minCoins: 300,        // $0.30 - удобно для пользователей
    networkFee: 0.25,
    status: 'best'
  },
  btc: {
    name: 'Bitcoin (BTC)',
    minCoins: 600,        // $0.60 - удобно для пользователей
    networkFee: 0.50,
    status: 'good'
  },
  usdttrc20: {
    name: 'USDT (TRC20)',
    minCoins: 9000,       // $9.00 - удобно для пользователей
    networkFee: 5.58,
    status: 'expensive'
  },
  ltc: {
    name: 'Litecoin (LTC)',
    minCoins: 600,        // $0.60 - удобно для пользователей
    networkFee: 0.50,
    status: 'good'
  },
  bch: {
    name: 'Bitcoin Cash (BCH)',
    minCoins: 400,        // $0.40 - удобно для пользователей
    networkFee: 0.30,
    status: 'good'
  },
  xrp: {
    name: 'Ripple (XRP)',
    minCoins: 300,        // $0.30 - удобно для пользователей
    networkFee: 0.20,
    status: 'good'
  },
  ada: {
    name: 'Cardano (ADA)',
    minCoins: 400,        // $0.40 - удобно для пользователей
    networkFee: 0.30,
    status: 'good'
  },
  dot: {
    name: 'Polkadot (DOT)',
    minCoins: 500,        // $0.50 - удобно для пользователей
    networkFee: 0.40,
    status: 'good'
  }
};

// Актуальные минимумы из API (обновляются динамически)
const actualCurrencyData = {
  ton: {
    name: 'TON (Telegram)',
    minCoins: 1740,       // Актуальный минимум из API
    networkFee: 0.15,
    status: 'good'
  },
  eth: {
    name: 'Ethereum (ETH)',
    minCoins: 609,        // Актуальный минимум из API
    networkFee: 0.25,
    status: 'good'
  },
  btc: {
    name: 'Bitcoin (BTC)',
    minCoins: 1136,       // Актуальный минимум из API
    networkFee: 0.50,
    status: 'good'
  },
  usdttrc20: {
    name: 'USDT (TRC20)',
    minCoins: 14260,      // Актуальный минимум из API
    networkFee: 5.58,
    status: 'expensive'
  },
  ltc: {
    name: 'Litecoin (LTC)',
    minCoins: 688,        // Актуальный минимум из API
    networkFee: 0.50,
    status: 'good'
  },
  bch: {
    name: 'Bitcoin Cash (BCH)',
    minCoins: 868,        // Актуальный минимум из API
    networkFee: 0.30,
    status: 'good'
  },
  xrp: {
    name: 'Ripple (XRP)',
    minCoins: 2552,       // Актуальный минимум из API
    networkFee: 0.20,
    status: 'good'
  },
  ada: {
    name: 'Cardano (ADA)',
    minCoins: 1034,       // Актуальный минимум из API
    networkFee: 0.30,
    status: 'good'
  },
  dot: {
    name: 'Polkadot (DOT)',
    minCoins: 888,        // Актуальный минимум из API
    networkFee: 0.40,
    status: 'good'
  }
};

/**
 * Получает данные о валюте с учетом динамического обновления
 * @param {string} currency - код валюты
 * @param {boolean} useActual - использовать актуальные данные (true) или пользовательские (false)
 * @returns {object} данные о валюте
 */
function getCurrencyData(currency, useActual = false) {
  const source = useActual ? actualCurrencyData : defaultCurrencyData;
  return source[currency] || defaultCurrencyData[currency];
}

/**
 * Обновляет актуальные данные о валютах
 * @param {object} newData - новые данные с сервера
 */
function updateActualCurrencyData(newData) {
  if (!newData || !newData.currencies) return;
  
  console.log('🔄 Обновление актуальных данных о валютах...');
  
  Object.keys(newData.currencies).forEach(currency => {
    const serverData = newData.currencies[currency];
    if (actualCurrencyData[currency] && serverData.minCoins) {
      const oldMin = actualCurrencyData[currency].minCoins;
      const newMin = serverData.minCoins;
      
      actualCurrencyData[currency].minCoins = newMin;
      actualCurrencyData[currency].networkFee = serverData.networkFee || actualCurrencyData[currency].networkFee;
      
      if (oldMin !== newMin) {
        const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        console.log(`📊 ${currency.toUpperCase()}: минимум обновлен ${oldMin} → ${newMin} ${coinsText}`);
      }
    }
  });
  
  console.log('✅ Актуальные данные обновлены');
}

/**
 * Получает эффективный минимум (максимум из пользовательского и актуального)
 * @param {string} currency - код валюты
 * @returns {number} эффективный минимум в монетах
 */
function getEffectiveMinimum(currency) {
  const userMin = defaultCurrencyData[currency]?.minCoins || 0;
  const actualMin = actualCurrencyData[currency]?.minCoins || 0;
  
  // Возвращаем максимум из двух значений для безопасности
  return Math.max(userMin, actualMin);
}

/**
 * Проверяет, нужно ли использовать актуальные минимумы
 * @param {string} currency - код валюты
 * @returns {boolean} true если актуальный минимум больше пользовательского
 */
function shouldUseActualMinimum(currency) {
  const userMin = defaultCurrencyData[currency]?.minCoins || 0;
  const actualMin = actualCurrencyData[currency]?.minCoins || 0;
  
  return actualMin > userMin;
}

// Экспорт для использования в других файлах
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    defaultCurrencyData,
    actualCurrencyData,
    getCurrencyData,
    updateActualCurrencyData,
    getEffectiveMinimum,
    shouldUseActualMinimum
  };
}
