// === dom-elements.js ===
// Ссылки на элементы DOM

// --- Основные страницы/секции ---
export const mainContentEl = document.getElementById("main-content");
export const earnSectionEl = document.getElementById("earn-section");
export const friendsSectionEl = document.getElementById("friends-section");
export const allPages = [mainContentEl, earnSectionEl, friendsSectionEl];

// --- Элементы пользовательского интерфейса ---
export const userNameEl = document.getElementById("user-name");
export const balanceAmountEl = document.getElementById("balance-amount");
export const headerBalanceInfoEl = document.getElementById("header-balance-info");
export const statusMessageEl = document.getElementById("status-message");

// --- Кнопки рекламы ---
// ИСПРАВЛЕНИЕ: Правильное соответствие кнопок и их ID
export const watchAdButton = document.getElementById("openLinkButton");    // "Открыть ссылку" - баннерная реклама
export const watchVideoButton = document.getElementById("watchVideoButton"); // "Смотреть видео" - видеореклама
export const openLinkButton = document.getElementById("openAdButton");      // "Кликнуть по баннеру" - интерстициальная реклама

// --- Реферальная система ---
export const shareAppButton = document.getElementById("share-app-button");
export const referralLinkInput = document.getElementById("referral-link-input");
export const copyReferralButton = document.getElementById("copy-referral-button");

// --- Элементы вывода средств ---
export const earnBalanceAmountEl = document.getElementById("earn-balance-amount");
export const availableWithdrawalEl = document.getElementById("available-withdrawal");
export const minWithdrawalEl = document.getElementById("min-withdrawal");
export const withdrawalAmountInput = document.getElementById("withdrawal-amount");
export const withdrawalAddressInput = document.getElementById("withdrawal-address");
export const cryptoCurrencySelect = document.getElementById("crypto-currency");
export const requestWithdrawalButton = document.getElementById("request-withdrawal-button");
export const withdrawalErrorEl = document.getElementById("withdrawal-error");

// --- Навигация ---
export const navHomeButton = document.getElementById("nav-home");
export const navEarnButton = document.getElementById("nav-earn");
export const navFriendsButton = document.getElementById("nav-friends");

// --- Калькулятор ---
export const calcAmountInput = document.getElementById('calc-amount');
export const calcBalance = document.getElementById('calc-balance');

// --- Telegram WebApp ---
export const tg = window.Telegram?.WebApp || null;

// --- Функции для работы с DOM ---

/**
 * Получает элемент по ID с проверкой существования
 * @param {string} id - ID элемента
 * @returns {HTMLElement|null}
 */
export function getElement(id) {
  const element = document.getElementById(id);
  if (!element) {
    console.warn(`Элемент с ID "${id}" не найден`);
  }
  return element;
}

/**
 * Получает элементы по селектору с проверкой существования
 * @param {string} selector - CSS селектор
 * @returns {NodeList}
 */
export function getElements(selector) {
  const elements = document.querySelectorAll(selector);
  if (elements.length === 0) {
    console.warn(`Элементы с селектором "${selector}" не найдены`);
  }
  return elements;
}

/**
 * Безопасно устанавливает текстовое содержимое элемента
 * @param {HTMLElement|null} element - Элемент
 * @param {string} text - Текст для установки
 */
export function setTextContent(element, text) {
  if (element) {
    element.textContent = text;
  } else {
    console.warn('Попытка установить текст для несуществующего элемента');
  }
}

/**
 * Безопасно устанавливает значение input элемента
 * @param {HTMLElement|null} element - Input элемент
 * @param {string} value - Значение для установки
 */
export function setValue(element, value) {
  if (element && element.tagName === 'INPUT') {
    element.value = value;
  } else {
    console.warn('Попытка установить значение для несуществующего input элемента');
  }
}

/**
 * Безопасно добавляет класс элементу
 * @param {HTMLElement|null} element - Элемент
 * @param {string} className - Класс для добавления
 */
export function addClass(element, className) {
  if (element) {
    element.classList.add(className);
  } else {
    console.warn(`Попытка добавить класс "${className}" для несуществующего элемента`);
  }
}

/**
 * Безопасно удаляет класс у элемента
 * @param {HTMLElement|null} element - Элемент
 * @param {string} className - Класс для удаления
 */
export function removeClass(element, className) {
  if (element) {
    element.classList.remove(className);
  } else {
    console.warn(`Попытка удалить класс "${className}" у несуществующего элемента`);
  }
}

/**
 * Проверяет наличие класса у элемента
 * @param {HTMLElement|null} element - Элемент
 * @param {string} className - Класс для проверки
 * @returns {boolean}
 */
export function hasClass(element, className) {
  return element ? element.classList.contains(className) : false;
}
