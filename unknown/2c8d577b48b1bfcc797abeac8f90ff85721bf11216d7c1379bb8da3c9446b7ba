// === ads-manager.js ===
// Управление рекламой и начислением наград

// Контроллер рекламы
let adsController = null;

// ИСПРАВЛЕНО: Определяем кнопки рекламы
const watchAdButton = document.getElementById("openLinkButton");    // "Открыть ссылку" - баннерная реклама
const watchVideoButton = document.getElementById("watchVideoButton"); // "Смотреть видео" - видеореклама
const openLinkButton = document.getElementById("openAdButton");      // "Кликнуть по баннеру" - интерстициальная реклама

// ИСПРАВЛЕНО: Определяем необходимые переменные
const API_BASE_URL = window.API_BASE_URL || 'api';
const appSettings = window.appSettings || { ad_rewards: { native_banner: 10, rewarded_video: 15, interstitial: 12 } };

// Переменные для контроля показа рекламы
let isAdShowing = false;
let lastAdShownTime = 0;
const adCooldownTime = 20000; // 20 секунд между показами

/**
 * Функции для управления состоянием показа рекламы
 */
function setIsAdShowing(value) {
  isAdShowing = value;
}

function setLastAdShownTime(time) {
  lastAdShownTime = time;
}

function checkAdCooldown() {
  const now = Date.now();
  const timeSinceLastAd = now - lastAdShownTime;

  if (timeSinceLastAd < adCooldownTime) {
    const remainingTime = Math.ceil((adCooldownTime - timeSinceLastAd) / 1000);
    showStatus(`Подождите ${remainingTime} секунд перед следующей рекламой`, "info");
    return false;
  }

  return true;
}

/**
 * Показывает статусное сообщение
 */
function showStatus(message, type = "info") {
  const statusMessageEl = document.getElementById('status-message');
  if (!statusMessageEl) return;

  statusMessageEl.textContent = message;
  statusMessageEl.className = "status-message";
  if (type === "success") {
    statusMessageEl.classList.add("success");
  } else if (type === "error") {
    statusMessageEl.classList.add("error");
  }
  console.log(`Status [${type}]: ${message}`);
}

/**
 * Воспроизводит звук монеток при получении награды
 * ИСПРАВЛЕНИЕ: Используем точно такую же логику как в оригинале
 */
function playCoinsSound(reward = 10) {
  try {
    console.log('[ЗВУК] Проверяем возможность воспроизведения звука...');

    // ИСПРАВЛЕНИЕ: Используем window.playCoinsSound как в оригинале
    if (window.playCoinsSound) {
      console.log(`[ЗВУК] Воспроизводим звук для ${reward} монет`);
      window.playCoinsSound(reward);
      console.log('🔊 Звук монеток воспроизведен через window.playCoinsSound');
    } else {
      console.warn('[ЗВУК] window.playCoinsSound не найден!');

      // Fallback - создаем простой звук
      const audio = new Audio('audio/zvuk-monety.mp3');
      audio.volume = 0.5;
      audio.play().catch(e => console.log('Звук не удалось воспроизвести:', e));
    }
  } catch (error) {
    console.log('Ошибка воспроизведения звука:', error);
  }
}

/**
 * Обрабатывает успешный просмотр рекламы
 * ИСПРАВЛЕНИЕ: Полностью переписано по образцу оригинальной функции recordAdView
 */
async function handleAdSuccess(adType, reward = null) {
  // ИСПРАВЛЕНИЕ: Проверяем данные Telegram как в оригинале
  const tg = window.Telegram?.WebApp || {};
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    if (tg.showAlert) tg.showAlert("Критическая ошибка: Невозможно засчитать награду.");
    return false;
  }

  console.log(`[API] Запись просмотра рекламы типа: ${adType}`);
  showStatus("Запись просмотра...");

  try {
    // ИСПРАВЛЕНИЕ: Определяем награду из настроек приложения как в оригинале
    const finalReward = reward || (appSettings.ad_rewards && appSettings.ad_rewards[adType]) || 1;

    // ИСПРАВЛЕНИЕ: Используем точно такую же структуру запроса как в оригинале
    const response = await fetch(`${API_BASE_URL}/recordAdView.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        adType: adType,
        reward: finalReward // Явно передаем награду на сервер
      }),
    });

    // ИСПРАВЛЕНИЕ: Обработка ошибок HTTP как в оригинале
    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();
    if (data.error) throw new Error(data.error);

    // ИСПРАВЛЕНО: Обновляем баланс пользователя через глобальную функцию
    if (window.updateBalanceDisplay) {
      window.updateBalanceDisplay(data.newBalance);
    } else if (window.balanceManager && window.balanceManager.updateBalance) {
      window.balanceManager.updateBalance(data.newBalance);
    } else {
      // Fallback - обновляем элемент баланса напрямую
      const balanceEl = document.getElementById('balance-amount');
      if (balanceEl) {
        balanceEl.textContent = data.newBalance.toLocaleString();
      }
    }

    // ИСПРАВЛЕНИЕ: Воспроизводим звук монет как в оригинале
    console.log('[ЗВУК] Проверяем возможность воспроизведения звука...');
    if (window.playCoinsSound) {
      console.log(`[ЗВУК] Воспроизводим звук для ${finalReward} монет (тип: ${adType})`);
      window.playCoinsSound(finalReward);
    } else {
      console.warn('[ЗВУК] window.playCoinsSound не найден!');
    }

    // ИСПРАВЛЕНИЕ: Показываем сообщение и вибрацию как в оригинале
    showStatus(`Награда зачислена! Баланс: ${data.newBalance}`, "success");
    tg.HapticFeedback.notificationOccurred("success");

    setTimeout(() => {
      if (statusMessageEl.textContent.startsWith("Награда зачислена"))
        showStatus("");
    }, 2500);

    return true;
  } catch (error) {
    // ИСПРАВЛЕНИЕ: Обработка ошибок как в оригинале
    console.error("[Record Ad View] Ошибка:", error);
    showStatus(`Ошибка записи: ${error.message}`, "error");
    tg.showAlert(`Не удалось засчитать награду: ${error.message}`);
    tg.HapticFeedback.notificationOccurred("error");
    return false;
  }
}



/**
 * Показывает баннерную рекламу
 * ИСПРАВЛЕНИЕ: Используем правильные типы рекламы как в оригинале
 */
async function showBannerAd() {
  if (!checkAdCooldown() || isAdShowing) return;

  try {
    setIsAdShowing(true);
    setLastAdShownTime(Date.now());

    showStatus("Загрузка рекламы...", "info");

    if (adsController && adsController.showBanner) {
      const result = await adsController.showBanner();
      if (result.success) {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы как в оригинале
        await handleAdSuccess('native_banner');
      } else {
        showStatus("Реклама недоступна", "error");
      }
    } else {
      // Fallback - имитация просмотра рекламы
      console.log('🎯 Имитация просмотра баннерной рекламы');
      setTimeout(async () => {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы
        await handleAdSuccess('native_banner');
      }, 1000);
    }
  } catch (error) {
    console.error('Ошибка показа баннерной рекламы:', error);
    showStatus("Ошибка загрузки рекламы", "error");
  } finally {
    setIsAdShowing(false);
  }
}

/**
 * Показывает видеорекламу
 * ИСПРАВЛЕНИЕ: Используем правильные типы рекламы как в оригинале
 */
async function showVideoAd() {
  if (!checkAdCooldown() || isAdShowing) return;

  try {
    setIsAdShowing(true);
    setLastAdShownTime(Date.now());

    showStatus("Загрузка видеорекламы...", "info");

    if (adsController && adsController.showVideo) {
      const result = await adsController.showVideo();
      if (result.success) {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы как в оригинале
        await handleAdSuccess('rewarded_video');
      } else {
        showStatus("Видеореклама недоступна", "error");
      }
    } else {
      // Fallback - имитация просмотра видеорекламы
      console.log('🎯 Имитация просмотра видеорекламы');
      setTimeout(async () => {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы
        await handleAdSuccess('rewarded_video');
      }, 2000);
    }
  } catch (error) {
    console.error('Ошибка показа видеорекламы:', error);
    showStatus("Ошибка загрузки видеорекламы", "error");
  } finally {
    setIsAdShowing(false);
  }
}

/**
 * Показывает интерстициальную рекламу
 * ИСПРАВЛЕНИЕ: Используем правильные типы рекламы как в оригинале
 */
async function showInterstitialAd() {
  if (!checkAdCooldown() || isAdShowing) return;

  try {
    setIsAdShowing(true);
    setLastAdShownTime(Date.now());

    showStatus("Загрузка полноэкранной рекламы...", "info");

    if (adsController && adsController.showInterstitial) {
      const result = await adsController.showInterstitial();
      if (result.success) {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы как в оригинале
        await handleAdSuccess('interstitial');
      } else {
        showStatus("Полноэкранная реклама недоступна", "error");
      }
    } else {
      // Fallback - имитация просмотра интерстициальной рекламы
      console.log('🎯 Имитация просмотра интерстициальной рекламы');
      setTimeout(async () => {
        // ИСПРАВЛЕНИЕ: Используем правильный тип рекламы
        await handleAdSuccess('interstitial');
      }, 1500);
    }
  } catch (error) {
    console.error('Ошибка показа интерстициальной рекламы:', error);
    showStatus("Ошибка загрузки рекламы", "error");
  } finally {
    setIsAdShowing(false);
  }
}

/**
 * Инициализирует рекламный SDK
 */
async function initAdsManager() {
  try {
    console.log('📺 Инициализация рекламного SDK...');
    
    // Проверяем наличие рекламного SDK
    if (window.TelegramGameProxy && window.TelegramGameProxy.initAds) {
      adsController = await window.TelegramGameProxy.initAds({
        pubId: MY_PUB_ID,
        appId: MY_APP_ID
      });
      
      if (adsController) {
        console.log('✅ Рекламный SDK инициализирован');
      } else {
        console.warn('⚠️ Рекламный SDK не удалось инициализировать');
      }
    } else {
      console.warn('⚠️ Рекламный SDK недоступен, используем fallback режим');
    }
    
    // Инициализируем обработчики кнопок
    initAdButtons();
    
    return adsController;
  } catch (error) {
    console.error('❌ Ошибка инициализации рекламного SDK:', error);
    // Все равно инициализируем кнопки для fallback режима
    initAdButtons();
    return null;
  }
}

/**
 * Инициализирует обработчики кнопок рекламы
 */
function initAdButtons() {
  // ИСПРАВЛЕНИЕ: Проверяем наличие элементов перед добавлением обработчиков

  // Кнопка "Открыть ссылку" (баннерная реклама)
  if (watchAdButton) {
    // Удаляем старые обработчики если есть
    watchAdButton.removeEventListener('click', showBannerAd);
    watchAdButton.addEventListener('click', showBannerAd);
    console.log('🔗 Обработчик баннерной рекламы подключен');
  } else {
    console.warn('⚠️ Кнопка "Открыть ссылку" не найдена');
  }

  // Кнопка "Смотреть видео" (видеореклама)
  if (watchVideoButton) {
    watchVideoButton.removeEventListener('click', showVideoAd);
    watchVideoButton.addEventListener('click', showVideoAd);
    console.log('📹 Обработчик видеорекламы подключен');
  } else {
    console.warn('⚠️ Кнопка "Смотреть видео" не найдена');
  }

  // Кнопка "Кликнуть по баннеру" (интерстициальная реклама)
  if (openLinkButton) {
    openLinkButton.removeEventListener('click', showInterstitialAd);
    openLinkButton.addEventListener('click', showInterstitialAd);
    console.log('🖱️ Обработчик интерстициальной рекламы подключен');
  } else {
    console.warn('⚠️ Кнопка "Кликнуть по баннеру" не найдена');
  }
}

/**
 * Получает контроллер рекламы
 */
function getAdsController() {
  return adsController;
}

/**
 * Проверяет доступность рекламы
 */
function isAdAvailable() {
  return !isAdShowing && checkAdCooldown();
}

/**
 * Получает время до следующей доступной рекламы
 */
function getTimeUntilNextAd() {
  const now = Date.now();
  const timeSinceLastAd = now - lastAdShownTime;
  const remainingTime = Math.max(0, adCooldownTime - timeSinceLastAd);
  return Math.ceil(remainingTime / 1000);
}

// Экспортируем функции для глобального доступа
window.AdsManager = {
  showBannerAd,
  showVideoAd,
  showInterstitialAd,
  isAdAvailable,
  getTimeUntilNextAd
};

// Делаем функции доступными глобально
window.initAdsManager = initAdsManager;
window.getAdsController = getAdsController;
window.isAdAvailable = isAdAvailable;
window.getTimeUntilNextAd = getTimeUntilNextAd;
