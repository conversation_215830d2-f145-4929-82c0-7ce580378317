<?php
/**
 * check_syntax.php
 * Проверка синтаксиса и выполнения send_manual_notification.php
 */

echo "<h1>🔍 Проверка синтаксиса и ошибок</h1>\n";

// Включаем отображение всех ошибок
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

echo "<h2>1. Проверка синтаксиса PHP файлов</h2>\n";

$files_to_check = [
    'api/admin/send_manual_notification.php',
    'api/config.php',
    'api/functions.php',
    'api/db_mock.php',
    'includes/bot_config_loader.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<p>✅ <code>$file</code> - синтаксис OK</p>\n";
        } else {
            echo "<p>❌ <code>$file</code> - ОШИБКА СИНТАКСИСА:</p>\n";
            echo "<pre>" . implode("\n", $output) . "</pre>\n";
        }
    } else {
        echo "<p>❌ <code>$file</code> - файл не найден</p>\n";
    }
}

echo "<h2>2. Проверка логов ошибок</h2>\n";

$error_log = 'api/error.log';
if (file_exists($error_log)) {
    $log_content = file_get_contents($error_log);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -10); // Последние 10 строк
    
    echo "<p>Последние 10 строк из error.log:</p>\n";
    echo "<pre>" . htmlspecialchars(implode("\n", $recent_lines)) . "</pre>\n";
} else {
    echo "<p>Файл error.log не найден</p>\n";
}

echo "<h2>3. Тест подключения файлов по отдельности</h2>\n";

try {
    echo "<p>Тестируем config.php...</p>\n";
    require_once __DIR__ . '/api/config.php';
    echo "<p>✅ config.php подключен успешно</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Ошибка в config.php: " . $e->getMessage() . "</p>\n";
} catch (Error $e) {
    echo "<p>❌ Фатальная ошибка в config.php: " . $e->getMessage() . "</p>\n";
}

try {
    echo "<p>Тестируем functions.php...</p>\n";
    require_once __DIR__ . '/api/functions.php';
    echo "<p>✅ functions.php подключен успешно</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Ошибка в functions.php: " . $e->getMessage() . "</p>\n";
} catch (Error $e) {
    echo "<p>❌ Фатальная ошибка в functions.php: " . $e->getMessage() . "</p>\n";
}

try {
    echo "<p>Тестируем db_mock.php...</p>\n";
    require_once __DIR__ . '/api/db_mock.php';
    echo "<p>✅ db_mock.php подключен успешно</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Ошибка в db_mock.php: " . $e->getMessage() . "</p>\n";
} catch (Error $e) {
    echo "<p>❌ Фатальная ошибка в db_mock.php: " . $e->getMessage() . "</p>\n";
}

echo "<h2>4. Проверка функций</h2>\n";

if (function_exists('loadUserData')) {
    echo "<p>✅ Функция loadUserData найдена</p>\n";
    try {
        $userData = loadUserData();
        echo "<p>✅ loadUserData выполнена успешно, загружено " . count($userData) . " пользователей</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ Ошибка выполнения loadUserData: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p>❌ Функция loadUserData НЕ найдена</p>\n";
}

if (defined('TELEGRAM_BOT_TOKEN')) {
    $token = TELEGRAM_BOT_TOKEN;
    $tokenPreview = substr($token, 0, 10) . '...' . substr($token, -10);
    echo "<p>✅ TELEGRAM_BOT_TOKEN определен: <code>$tokenPreview</code></p>\n";
} else {
    echo "<p>❌ TELEGRAM_BOT_TOKEN НЕ определен</p>\n";
}

echo "<h2>5. Прямой тест send_manual_notification.php</h2>\n";

// Создаем минимальный POST запрос
$_POST['recipient_type'] = 'all';
$_POST['message'] = 'Тест';
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<p>Попытка выполнить send_manual_notification.php...</p>\n";

// Перехватываем вывод
ob_start();
$error_occurred = false;

try {
    // Эмулируем сессию администратора
    session_start();
    $_SESSION['admin_authenticated'] = true;
    
    include __DIR__ . '/api/admin/send_manual_notification.php';
} catch (Exception $e) {
    $error_occurred = true;
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>\n";
    echo "<p>Файл: " . $e->getFile() . "</p>\n";
    echo "<p>Строка: " . $e->getLine() . "</p>\n";
} catch (Error $e) {
    $error_occurred = true;
    echo "<p>❌ Fatal Error: " . $e->getMessage() . "</p>\n";
    echo "<p>Файл: " . $e->getFile() . "</p>\n";
    echo "<p>Строка: " . $e->getLine() . "</p>\n";
}

$output = ob_get_clean();

if (!$error_occurred) {
    echo "<p>✅ Файл выполнен без фатальных ошибок</p>\n";
    echo "<p>Вывод:</p>\n";
    echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
} else {
    echo "<p>❌ Произошла ошибка при выполнении файла</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Время проверки:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
