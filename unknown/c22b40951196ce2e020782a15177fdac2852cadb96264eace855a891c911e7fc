<?php
/**
 * Тест исправления баннера бота
 */

require_once __DIR__ . '/config.php';

echo "🔧 ТЕСТ ИСПРАВЛЕНИЯ БАННЕРА БОТА\n";
echo str_repeat("=", 50) . "\n\n";

// Проверяем webhook.php
echo "1️⃣ Проверяем webhook.php:\n";
$webhookContent = file_get_contents(__DIR__ . '/webhook.php');

if (strpos($webhookContent, 'bot_welcome_super_banner.png') !== false) {
    echo "   ✅ webhook.php использует bot_welcome_super_banner.png\n";
} else if (strpos($webhookContent, 'bot_welcome_perfect_banner.png') !== false) {
    echo "   ❌ webhook.php все еще использует bot_welcome_perfect_banner.png\n";
} else {
    echo "   ⚠️ Не найдено упоминание баннера в webhook.php\n";
}

// Проверяем доступность файла баннера
echo "\n2️⃣ Проверяем доступность баннера:\n";
$bannerUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png';
$headers = @get_headers($bannerUrl);

if ($headers && strpos($headers[0], '200') !== false) {
    echo "   ✅ Баннер доступен по URL: $bannerUrl\n";
} else {
    echo "   ❌ Баннер недоступен по URL: $bannerUrl\n";
    if ($headers) {
        echo "   📊 Статус: {$headers[0]}\n";
    }
}

// Проверяем размер файла
echo "\n3️⃣ Проверяем файл баннера:\n";
$localBannerPath = __DIR__ . '/../images/bot_welcome_super_banner.png';

if (file_exists($localBannerPath)) {
    $fileSize = filesize($localBannerPath);
    $fileSizeMB = round($fileSize / 1024 / 1024, 2);
    echo "   ✅ Файл существует локально\n";
    echo "   📊 Размер файла: {$fileSizeMB} MB\n";
    
    if ($fileSize > 10 * 1024 * 1024) {
        echo "   ⚠️ Файл больше 10MB - может быть проблема с Telegram\n";
    } else {
        echo "   ✅ Размер файла подходит для Telegram\n";
    }
} else {
    echo "   ❌ Файл не найден локально: $localBannerPath\n";
}

// Проверяем webhook URL в коде
echo "\n4️⃣ Проверяем URL в коде:\n";
if (preg_match('/logoUrl = [\'"]([^\'"]+)[\'"]/', $webhookContent, $matches)) {
    $codeUrl = $matches[1];
    echo "   📊 URL в коде: $codeUrl\n";
    
    if (strpos($codeUrl, 'bot_welcome_super_banner.png') !== false) {
        echo "   ✅ URL указывает на правильный файл\n";
    } else {
        echo "   ❌ URL указывает на неправильный файл\n";
    }
} else {
    echo "   ⚠️ Не удалось найти URL в коде\n";
}

// Тестируем отправку баннера
echo "\n5️⃣ Тестируем отправку баннера:\n";

function testSendPhoto($chatId, $photoUrl, $caption = '', $keyboard = null) {
    $url = "https://api.telegram.org/bot" . BOT_TOKEN . "/sendPhoto";

    $data = [
        'chat_id' => $chatId,
        'photo' => $photoUrl,
        'caption' => $caption,
        'parse_mode' => 'HTML'
    ];

    if ($keyboard) {
        $data['reply_markup'] = json_encode($keyboard);
    }

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    return json_decode($result, true);
}

// Получаем chat_id из параметров
$testChatId = $_GET['chat_id'] ?? null;

if ($testChatId) {
    echo "   📤 Отправляем тестовый баннер в чат: $testChatId\n";
    
    $testUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?' . time();
    $testCaption = "🧪 <b>Тест исправления баннера</b>\n\n";
    $testCaption .= "✅ Файл: bot_welcome_super_banner.png\n";
    $testCaption .= "⏰ Время: " . date('Y-m-d H:i:s');
    
    $result = testSendPhoto($testChatId, $testUrl, $testCaption);
    
    if ($result && isset($result['ok']) && $result['ok']) {
        echo "   ✅ Баннер успешно отправлен!\n";
        if (isset($result['result']['photo'])) {
            $fileId = end($result['result']['photo'])['file_id'];
            echo "   🆔 File ID: $fileId\n";
        }
    } else {
        echo "   ❌ Ошибка отправки баннера\n";
        if ($result && isset($result['description'])) {
            echo "   📊 Ошибка: {$result['description']}\n";
        }
    }
} else {
    echo "   ⚠️ Не указан chat_id для тестирования\n";
    echo "   💡 Добавьте ?chat_id=YOUR_CHAT_ID к URL\n";
}

// Проверяем логи бота
echo "\n6️⃣ Проверяем логи бота:\n";
$logFile = __DIR__ . '/bot.log';

if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $logLines = explode("\n", $logContent);
    $recentLines = array_slice($logLines, -10); // Последние 10 строк
    
    echo "   📊 Последние записи в логе:\n";
    foreach ($recentLines as $line) {
        if (trim($line)) {
            echo "   📝 " . trim($line) . "\n";
        }
    }
} else {
    echo "   ⚠️ Файл лога не найден: $logFile\n";
}

// Итоговые рекомендации
echo "\n🎯 ИТОГОВЫЕ РЕКОМЕНДАЦИИ:\n";
echo str_repeat("-", 30) . "\n";

$issues = [];
$fixes = [];

// Проверяем основные проблемы
if (strpos($webhookContent, 'bot_welcome_perfect_banner.png') !== false) {
    $issues[] = "webhook.php использует неправильный файл баннера";
    $fixes[] = "Заменить bot_welcome_perfect_banner.png на bot_welcome_super_banner.png в webhook.php";
}

if (!file_exists($localBannerPath)) {
    $issues[] = "Файл bot_welcome_super_banner.png не найден";
    $fixes[] = "Загрузить файл bot_welcome_super_banner.png в папку images/";
}

$headers = @get_headers($bannerUrl);
if (!$headers || strpos($headers[0], '200') === false) {
    $issues[] = "Баннер недоступен по URL";
    $fixes[] = "Проверить права доступа к файлу и настройки сервера";
}

if (empty($issues)) {
    echo "✅ Все проверки пройдены успешно!\n";
    echo "🎉 Баннер должен отображаться корректно\n";
    
    if (!$testChatId) {
        echo "\n💡 Для финального теста:\n";
        echo "   1. Добавьте ?chat_id=YOUR_CHAT_ID к URL этого скрипта\n";
        echo "   2. Отправьте /start боту @uniqpaid_paid_bot\n";
        echo "   3. Проверьте отображение баннера\n";
    }
} else {
    echo "❌ Найдены проблемы:\n";
    foreach ($issues as $i => $issue) {
        echo "   " . ($i + 1) . ". $issue\n";
    }
    
    echo "\n🔧 Необходимые исправления:\n";
    foreach ($fixes as $i => $fix) {
        echo "   " . ($i + 1) . ". $fix\n";
    }
}

echo "\n📋 Дополнительные действия:\n";
echo "1. Очистить кэш Telegram (подождать 5-10 минут)\n";
echo "2. Перезапустить webhook бота\n";
echo "3. Протестировать команду /start\n";
echo "4. Проверить логи на наличие ошибок\n";

echo "\n🎉 ТЕСТ ЗАВЕРШЕН!\n";
?>
