<?php
/**
 * api/recordAdView.php
 * API эндпоинт для ЗАПРОСА на просмотр рекламы.
 * Генерирует и возвращает токен для последующего подтверждения награды.
 */

header('Content-Type: application/json');

// --- Подключение зависимостей ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }
if (!(@require_once __DIR__ . '/security.php')) { http_response_code(500); error_log('FATAL: security.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: SEC']); exit; }
// --- Конец зависимостей ---

// 1. Получение IP и User-Agent
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// 2. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    error_log("recordAdView ERROR: Нет данных или неверный JSON от IP: {$clientIp}");
    http_response_code(400); echo json_encode(['error' => 'Ошибка запроса: Нет данных']); exit;
}
$initData = $input['initData'];

// 3. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    http_response_code(403); echo json_encode(['error' => 'Ошибка: Неверные данные']); exit;
}
$userId = intval($validatedData['user']['id']);
error_log("recordAdView INFO: initData валидирован для user $userId от IP: {$clientIp}");

// 4. Загрузка данных пользователя
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("recordAdView ERROR: loadUserData вернул не массив."); http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: LD1']); exit;
}

// 5. Проверка лимитов и блокировок
$adType = $input['adType'] ?? 'default';

if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("recordAdView WARNING: Попытка запроса рекламы заблокированным пользователем $userId");
    http_response_code(403); echo json_encode(['error' => 'Ваш аккаунт заблокирован']); exit;
}

if (!checkAdViewLimitByType($userId, $adType, $userData) || !checkAdViewLimit($userId, $userData)) {
    error_log("recordAdView WARNING: Превышен лимит просмотров рекламы для пользователя $userId");
    http_response_code(429); echo json_encode(['error' => 'Превышен лимит просмотров рекламы.']); exit;
}

// 6. Генерация и сохранение токена
$token = bin2hex(random_bytes(16)); // Генерируем безопасный токен
$tokenExpiry = time() + 300; // Срок жизни токена - 10 секунд
$tokenFile = __DIR__ . '/../database/ad_tokens.json';
$tokenDir = dirname($tokenFile);

// --- ИСПРАВЛЕНИЕ: Проверка прав на запись ---
if (!is_dir($tokenDir)) {
    // Попробуем создать директорию, если ее нет. Права 0775 дают полный доступ владельцу и группе, и чтение/выполнение остальным.
    if (!@mkdir($tokenDir, 0775, true)) {
        $last_error = error_get_last();
        error_log("recordAdView CRITICAL: Не удалось создать директорию для токенов: {$tokenDir}. Ошибка: " . ($last_error['message'] ?? 'Нет данных об ошибке'));
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка сервера: Не удалось создать директорию для токенов.']);
        exit;
    }
} elseif (!is_writable($tokenDir)) {
    error_log("recordAdView CRITICAL: Директория для токенов не доступна для записи: {$tokenDir}");
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: Директория для токенов не доступна для записи. Проверьте права.']);
    exit;
}
// --- КОНЕЦ ИСПРАВЛЕНИЯ ---

$tokens = [];
if (file_exists($tokenFile)) {
    $tokensData = file_get_contents($tokenFile);
    if ($tokensData) {
        $tokens = json_decode($tokensData, true);
        // Проверка, что json_decode не вернул null из-за ошибки
        if ($tokens === null) {
            $tokens = []; // Если файл поврежден, начинаем с чистого листа
            error_log("recordAdView WARNING: Не удалось декодировать JSON из файла токенов: {$tokenFile}. Файл может быть поврежден.");
        }
    }
}

// Очистка старых токенов
$currentTime = time();
$tokens = array_filter($tokens, function($t) use ($currentTime) {
    return isset($t['expires_at']) && $t['expires_at'] > $currentTime;
});

// Добавляем новый токен
$tokens[] = [
    'token' => $token,
    'user_id' => $userId,
    'ad_type' => $adType,
    'used' => false,
    'created_at' => $currentTime,
    'expires_at' => $tokenExpiry
];

if (file_put_contents($tokenFile, json_encode($tokens, JSON_PRETTY_PRINT), LOCK_EX) === false) {
    $last_error = error_get_last();
    error_log("recordAdView CRITICAL: Не удалось сохранить токен для пользователя $userId. Ошибка: " . ($last_error['message'] ?? 'Нет данных об ошибке'));
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: Не удалось создать токен']);
    exit;
}

error_log("recordAdView INFO: Токен $token создан для пользователя $userId, тип: $adType");

// 7. Успешный ответ с токеном
http_response_code(200);
echo json_encode([
    'success' => true,
    'token' => $token,
    'expires' => $tokenExpiry,
    'message' => 'Токен для просмотра рекламы успешно создан'
]);
exit;
