<?php
echo "1. Начало скрипта\n";

echo "2. Подключение config.php\n";
require_once __DIR__ . '/../config.php';
echo "3. config.php подключен\n";

echo "4. Подключение functions.php\n";
try {
    require_once __DIR__ . '/../functions.php';
    echo "5. functions.php подключен\n";
} catch (Exception $e) {
    echo "ОШИБКА при подключении functions.php: " . $e->getMessage() . "\n";
    exit(1);
} catch (Error $e) {
    echo "ФАТАЛЬНАЯ ОШИБКА при подключении functions.php: " . $e->getMessage() . "\n";
    exit(1);
}

echo "6. Проверка функции loadUserData\n";
if (function_exists('loadUserData')) {
    echo "7. Функция loadUserData существует\n";
} else {
    echo "7. ОШИБКА: Функция loadUserData не найдена\n";
    exit(1);
}

echo "8. Тест завершен успешно\n";
?>
