<?php
/**
 * api/ad_functions.php
 * Функции для работы с рекламой (без выполнения основного кода)
 */

/**
 * Логирование рекламных запросов для статистики
 */
function logAdRequest($userId, $adType, $status, $clientIp, $userAgent = '', $country = '', $additionalData = []) {
    $logFile = __DIR__ . '/ad_requests.log';
    $timestamp = time();
    // ИЗМЕНЕНО: Используем UTC время для всех логов
    $date = gmdate('Y-m-d H:i:s', $timestamp);

    // Определяем страну по IP (простая заглушка, можно интегрировать с GeoIP)
    if (empty($country)) {
        $country = getCountryByIP($clientIp);
    }

    $logEntry = [
        'timestamp' => $timestamp,
        'date' => $date,
        'user_id' => $userId,
        'ad_type' => $adType,
        'status' => $status, // 'request', 'success', 'empty', 'error', 'limit_exceeded', 'click', 'ad_shown_no_reward', 'blocked_user'
        'ip' => $clientIp,
        'user_agent' => $userAgent,
        'country' => $country
    ];

    // Добавляем дополнительные данные если есть
    if (!empty($additionalData)) {
        $logEntry = array_merge($logEntry, $additionalData);
    }

    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

/**
 * Определение страны по IP
 * Использует кэширование для оптимизации
 */
function getCountryByIP($ip) {
    // Кэш для IP адресов
    static $ipCache = [];

    if (isset($ipCache[$ip])) {
        return $ipCache[$ip];
    }

    // Локальные IP адреса
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        $ipCache[$ip] = 'Local';
        return 'Local';
    }

    // Российские IP диапазоны (примерные)
    $russianRanges = [
        '/^(5\.45\.|5\.101\.|37\.9\.|37\.29\.|46\.17\.|46\.39\.|62\.117\.|77\.88\.|77\.234\.|78\.25\.|78\.108\.|79\.137\.|80\.66\.|80\.87\.|80\.249\.|81\.19\.|81\.23\.|82\.146\.|83\.102\.|83\.149\.|84\.201\.|85\.26\.|85\.143\.|86\.57\.|87\.226\.|87\.229\.|87\.236\.|87\.245\.|87\.250\.|89\.108\.|89\.111\.|89\.113\.|89\.208\.|89\.218\.|89\.232\.|91\.103\.|91\.105\.|91\.106\.|91\.108\.|91\.200\.|91\.201\.|91\.202\.|91\.203\.|91\.204\.|91\.205\.|91\.206\.|91\.207\.|91\.208\.|91\.209\.|91\.210\.|91\.211\.|91\.212\.|91\.213\.|91\.214\.|91\.215\.|91\.216\.|91\.217\.|91\.218\.|91\.219\.|91\.220\.|91\.221\.|91\.222\.|91\.223\.|92\.38\.|92\.39\.|92\.242\.|92\.243\.|92\.244\.|92\.245\.|92\.246\.|92\.247\.|92\.248\.|92\.249\.|92\.250\.|92\.251\.|92\.252\.|92\.253\.|92\.254\.|92\.255\.|93\.84\.|93\.158\.|93\.170\.|93\.171\.|93\.174\.|93\.175\.|94\.19\.|94\.25\.|94\.26\.|94\.73\.|94\.100\.|94\.101\.|94\.102\.|94\.103\.|94\.104\.|94\.105\.|94\.106\.|94\.107\.|94\.108\.|94\.109\.|94\.110\.|94\.111\.|94\.112\.|94\.113\.|94\.114\.|94\.115\.|94\.116\.|94\.117\.|94\.118\.|94\.119\.|94\.120\.|94\.121\.|94\.122\.|94\.123\.|94\.124\.|94\.125\.|94\.126\.|94\.127\.|95\.24\.|95\.25\.|95\.26\.|95\.27\.|95\.28\.|95\.29\.|95\.30\.|95\.31\.|95\.32\.|95\.33\.|95\.34\.|95\.35\.|95\.36\.|95\.37\.|95\.38\.|95\.39\.|95\.40\.|95\.41\.|95\.42\.|95\.43\.|95\.44\.|95\.45\.|95\.46\.|95\.47\.|95\.48\.|95\.49\.|95\.50\.|95\.51\.|95\.52\.|95\.53\.|95\.54\.|95\.55\.|95\.56\.|95\.57\.|95\.58\.|95\.59\.|95\.60\.|95\.61\.|95\.62\.|95\.63\.|109\.60\.|109\.61\.|109\.62\.|109\.63\.|109\.64\.|109\.65\.|109\.66\.|109\.67\.|109\.68\.|109\.69\.|109\.70\.|109\.71\.|109\.72\.|109\.73\.|109\.74\.|109\.75\.|109\.76\.|109\.77\.|109\.78\.|109\.79\.|109\.80\.|109\.81\.|109\.82\.|109\.83\.|109\.84\.|109\.85\.|109\.86\.|109\.87\.|109\.88\.|109\.89\.|109\.90\.|109\.91\.|109\.92\.|109\.93\.|109\.94\.|109\.95\.|109\.96\.|109\.97\.|109\.98\.|109\.99\.|109\.100\.|109\.101\.|109\.102\.|109\.103\.|109\.104\.|109\.105\.|109\.106\.|109\.107\.|109\.108\.|109\.109\.|109\.110\.|109\.111\.|109\.112\.|109\.113\.|109\.114\.|109\.115\.|109\.116\.|109\.117\.|109\.118\.|109\.119\.|109\.120\.|109\.121\.|109\.122\.|109\.123\.|109\.124\.|109\.125\.|109\.126\.|109\.127\.|109\.128\.|109\.129\.|109\.130\.|109\.131\.|109\.132\.|109\.133\.|109\.134\.|109\.135\.|109\.136\.|109\.137\.|109\.138\.|109\.139\.|109\.140\.|109\.141\.|109\.142\.|109\.143\.|109\.144\.|109\.145\.|109\.146\.|109\.147\.|109\.148\.|109\.149\.|109\.150\.|109\.151\.|109\.152\.|109\.153\.|109\.154\.|109\.155\.|109\.156\.|109\.157\.|109\.158\.|109\.159\.|109\.160\.|109\.161\.|109\.162\.|109\.163\.|109\.164\.|109\.165\.|109\.166\.|109\.167\.|109\.168\.|109\.169\.|109\.170\.|109\.171\.|109\.172\.|109\.173\.|109\.174\.|109\.175\.|109\.176\.|109\.177\.|109\.178\.|109\.179\.|109\.180\.|109\.181\.|109\.182\.|109\.183\.|109\.184\.|109\.185\.|109\.186\.|109\.187\.|109\.188\.|109\.189\.|109\.190\.|109\.191\.|109\.192\.|109\.193\.|109\.194\.|109\.195\.|109\.196\.|109\.197\.|109\.198\.|109\.199\.|109\.200\.|109\.201\.|109\.202\.|109\.203\.|109\.204\.|109\.205\.|109\.206\.|109\.207\.|109\.208\.|109\.209\.|109\.210\.|109\.211\.|109\.212\.|109\.213\.|109\.214\.|109\.215\.|109\.216\.|109\.217\.|109\.218\.|109\.219\.|109\.220\.|109\.221\.|109\.222\.|109\.223\.|109\.224\.|109\.225\.|109\.226\.|109\.227\.|109\.228\.|109\.229\.|109\.230\.|109\.231\.|109\.232\.|109\.233\.|109\.234\.|109\.235\.|109\.236\.|109\.237\.|109\.238\.|109\.239\.|109\.240\.|109\.241\.|109\.242\.|109\.243\.|109\.244\.|109\.245\.|109\.246\.|109\.247\.|109\.248\.|109\.249\.|109\.250\.|109\.251\.|109\.252\.|109\.253\.|109\.254\.|109\.255\.)/',
        '/^(176\.9\.|176\.10\.|176\.12\.|176\.14\.|176\.15\.|176\.16\.|176\.17\.|176\.18\.|176\.19\.|176\.20\.|176\.21\.|176\.22\.|176\.23\.|176\.24\.|176\.25\.|176\.26\.|176\.27\.|176\.28\.|176\.29\.|176\.30\.|176\.31\.|176\.32\.|176\.33\.|176\.34\.|176\.35\.|176\.36\.|176\.37\.|176\.38\.|176\.39\.|176\.40\.|176\.41\.|176\.42\.|176\.43\.|176\.44\.|176\.45\.|176\.46\.|176\.47\.|176\.48\.|176\.49\.|176\.50\.|176\.51\.|176\.52\.|176\.53\.|176\.54\.|176\.55\.|176\.56\.|176\.57\.|176\.58\.|176\.59\.|176\.60\.|176\.61\.|176\.62\.|176\.63\.|176\.64\.|176\.65\.|176\.66\.|176\.67\.|176\.68\.|176\.69\.|176\.70\.|176\.71\.|176\.72\.|176\.73\.|176\.74\.|176\.75\.|176\.76\.|176\.77\.|176\.78\.|176\.79\.|176\.80\.|176\.81\.|176\.82\.|176\.83\.|176\.84\.|176\.85\.|176\.86\.|176\.87\.|176\.88\.|176\.89\.|176\.90\.|176\.91\.|176\.92\.|176\.93\.|176\.94\.|176\.95\.|176\.96\.|176\.97\.|176\.98\.|176\.99\.|176\.100\.|176\.101\.|176\.102\.|176\.103\.|176\.104\.|176\.105\.|176\.106\.|176\.107\.|176\.108\.|176\.109\.|176\.110\.|176\.111\.|176\.112\.|176\.113\.|176\.114\.|176\.115\.|176\.116\.|176\.117\.|176\.118\.|176\.119\.|176\.120\.|176\.121\.|176\.122\.|176\.123\.|176\.124\.|176\.125\.|176\.126\.|176\.127\.|176\.128\.|176\.129\.|176\.130\.|176\.131\.|176\.132\.|176\.133\.|176\.134\.|176\.135\.|176\.136\.|176\.137\.|176\.138\.|176\.139\.|176\.140\.|176\.141\.|176\.142\.|176\.143\.|176\.144\.|176\.145\.|176\.146\.|176\.147\.|176\.148\.|176\.149\.|176\.150\.|176\.151\.|176\.152\.|176\.153\.|176\.154\.|176\.155\.|176\.156\.|176\.157\.|176\.158\.|176\.159\.|176\.160\.|176\.161\.|176\.162\.|176\.163\.|176\.164\.|176\.165\.|176\.166\.|176\.167\.|176\.168\.|176\.169\.|176\.170\.|176\.171\.|176\.172\.|176\.173\.|176\.174\.|176\.175\.|176\.176\.|176\.177\.|176\.178\.|176\.179\.|176\.180\.|176\.181\.|176\.182\.|176\.183\.|176\.184\.|176\.185\.|176\.186\.|176\.187\.|176\.188\.|176\.189\.|176\.190\.|176\.191\.|176\.192\.|176\.193\.|176\.194\.|176\.195\.|176\.196\.|176\.197\.|176\.198\.|176\.199\.|176\.200\.|176\.201\.|176\.202\.|176\.203\.|176\.204\.|176\.205\.|176\.206\.|176\.207\.|176\.208\.|176\.209\.|176\.210\.|176\.211\.|176\.212\.|176\.213\.|176\.214\.|176\.215\.|176\.216\.|176\.217\.|176\.218\.|176\.219\.|176\.220\.|176\.221\.|176\.222\.|176\.223\.|176\.224\.|176\.225\.|176\.226\.|176\.227\.|176\.228\.|176\.229\.|176\.230\.|176\.231\.|176\.232\.|176\.233\.|176\.234\.|176\.235\.|176\.236\.|176\.237\.|176\.238\.|176\.239\.|176\.240\.|176\.241\.|176\.242\.|176\.243\.|176\.244\.|176\.245\.|176\.246\.|176\.247\.|176\.248\.|176\.249\.|176\.250\.|176\.251\.|176\.252\.|176\.253\.|176\.254\.|176\.255\.)/',
        '/^(178\.0\.|178\.1\.|178\.2\.|178\.3\.|178\.4\.|178\.5\.|178\.6\.|178\.7\.|178\.8\.|178\.9\.|178\.10\.|178\.11\.|178\.12\.|178\.13\.|178\.14\.|178\.15\.|178\.16\.|178\.17\.|178\.18\.|178\.19\.|178\.20\.|178\.21\.|178\.22\.|178\.23\.|178\.24\.|178\.25\.|178\.26\.|178\.27\.|178\.28\.|178\.29\.|178\.30\.|178\.31\.|178\.32\.|178\.33\.|178\.34\.|178\.35\.|178\.36\.|178\.37\.|178\.38\.|178\.39\.|178\.40\.|178\.41\.|178\.42\.|178\.43\.|178\.44\.|178\.45\.|178\.46\.|178\.47\.|178\.48\.|178\.49\.|178\.50\.|178\.51\.|178\.52\.|178\.53\.|178\.54\.|178\.55\.|178\.56\.|178\.57\.|178\.58\.|178\.59\.|178\.60\.|178\.61\.|178\.62\.|178\.63\.|178\.64\.|178\.65\.|178\.66\.|178\.67\.|178\.68\.|178\.69\.|178\.70\.|178\.71\.|178\.72\.|178\.73\.|178\.74\.|178\.75\.|178\.76\.|178\.77\.|178\.78\.|178\.79\.|178\.80\.|178\.81\.|178\.82\.|178\.83\.|178\.84\.|178\.85\.|178\.86\.|178\.87\.|178\.88\.|178\.89\.|178\.90\.|178\.91\.|178\.92\.|178\.93\.|178\.94\.|178\.95\.|178\.96\.|178\.97\.|178\.98\.|178\.99\.|178\.100\.|178\.101\.|178\.102\.|178\.103\.|178\.104\.|178\.105\.|178\.106\.|178\.107\.|178\.108\.|178\.109\.|178\.110\.|178\.111\.|178\.112\.|178\.113\.|178\.114\.|178\.115\.|178\.116\.|178\.117\.|178\.118\.|178\.119\.|178\.120\.|178\.121\.|178\.122\.|178\.123\.|178\.124\.|178\.125\.|178\.126\.|178\.127\.|178\.128\.|178\.129\.|178\.130\.|178\.131\.|178\.132\.|178\.133\.|178\.134\.|178\.135\.|178\.136\.|178\.137\.|178\.138\.|178\.139\.|178\.140\.|178\.141\.|178\.142\.|178\.143\.|178\.144\.|178\.145\.|178\.146\.|178\.147\.|178\.148\.|178\.149\.|178\.150\.|178\.151\.|178\.152\.|178\.153\.|178\.154\.|178\.155\.|178\.156\.|178\.157\.|178\.158\.|178\.159\.|178\.160\.|178\.161\.|178\.162\.|178\.163\.|178\.164\.|178\.165\.|178\.166\.|178\.167\.|178\.168\.|178\.169\.|178\.170\.|178\.171\.|178\.172\.|178\.173\.|178\.174\.|178\.175\.|178\.176\.|178\.177\.|178\.178\.|178\.179\.|178\.180\.|178\.181\.|178\.182\.|178\.183\.|178\.184\.|178\.185\.|178\.186\.|178\.187\.|178\.188\.|178\.189\.|178\.190\.|178\.191\.|178\.192\.|178\.193\.|178\.194\.|178\.195\.|178\.196\.|178\.197\.|178\.198\.|178\.199\.|178\.200\.|178\.201\.|178\.202\.|178\.203\.|178\.204\.|178\.205\.|178\.206\.|178\.207\.|178\.208\.|178\.209\.|178\.210\.|178\.211\.|178\.212\.|178\.213\.|178\.214\.|178\.215\.|178\.216\.|178\.217\.|178\.218\.|178\.219\.|178\.220\.|178\.221\.|178\.222\.|178\.223\.|178\.224\.|178\.225\.|178\.226\.|178\.227\.|178\.228\.|178\.229\.|178\.230\.|178\.231\.|178\.232\.|178\.233\.|178\.234\.|178\.235\.|178\.236\.|178\.237\.|178\.238\.|178\.239\.|178\.240\.|178\.241\.|178\.242\.|178\.243\.|178\.244\.|178\.245\.|178\.246\.|178\.247\.|178\.248\.|178\.249\.|178\.250\.|178\.251\.|178\.252\.|178\.253\.|178\.254\.|178\.255\.)/'
    ];

    foreach ($russianRanges as $range) {
        if (preg_match($range, $ip)) {
            $ipCache[$ip] = 'RU';
            return 'RU';
        }
    }

    // Американские IP диапазоны (примерные)
    $usRanges = [
        '/^(8\.|4\.|12\.|13\.|15\.|16\.|17\.|18\.|19\.|20\.|21\.|22\.|23\.|24\.|25\.|26\.|27\.|28\.|29\.|30\.|31\.|32\.|33\.|34\.|35\.|36\.|37\.|38\.|39\.|40\.|41\.|42\.|43\.|44\.|45\.|46\.|47\.|48\.|49\.|50\.|51\.|52\.|53\.|54\.|55\.|56\.|57\.|58\.|59\.|60\.|61\.|62\.|63\.|64\.|65\.|66\.|67\.|68\.|69\.|70\.|71\.|72\.|73\.|74\.|75\.|76\.|96\.|97\.|98\.|99\.|100\.|101\.|102\.|103\.|104\.|105\.|106\.|107\.|108\.|142\.|143\.|144\.|145\.|146\.|147\.|148\.|149\.|150\.|151\.|152\.|153\.|154\.|155\.|156\.|157\.|158\.|159\.|160\.|161\.|162\.|163\.|164\.|165\.|166\.|167\.|168\.|169\.|170\.|171\.|172\.|173\.|174\.|184\.|192\.|198\.|199\.|204\.|205\.|206\.|207\.|208\.|209\.|216\.)/'
    ];

    foreach ($usRanges as $range) {
        if (preg_match($range, $ip)) {
            $ipCache[$ip] = 'US';
            return 'US';
        }
    }

    // Проверка для других стран
    $otherCountries = [
        'DE' => ['/^(46\.4\.|85\.25\.|217\.160\.|62\.75\.)/', '/^(195\.93\.|212\.227\.|217\.5\.|80\.67\.)/'],
        'FR' => ['/^(213\.186\.|80\.12\.|82\.64\.|90\.84\.)/', '/^(193\.252\.|212\.27\.|217\.128\.|80\.236\.)/'],
        'GB' => ['/^(212\.58\.|81\.2\.|86\.1\.|194\.74\.)/', '/^(195\.92\.|212\.219\.|217\.169\.|80\.68\.)/'],
        'CN' => ['/^(61\.135\.|123\.125\.|220\.181\.|202\.108\.)/', '/^(119\.75\.|180\.149\.|183\.232\.|14\.215\.)/'],
        'UA' => ['/^(91\.224\.|91\.225\.|91\.226\.|91\.227\.)/', '/^(178\.137\.|185\.25\.|188\.163\.|195\.138\.)/'],
        'BY' => ['/^(178\.172\.|178\.173\.|178\.174\.|178\.175\.)/', '/^(93\.84\.|178\.124\.|212\.98\.|217\.21\.)/'],
        'KZ' => ['/^(92\.46\.|92\.47\.|92\.48\.|92\.49\.)/', '/^(195\.210\.|212\.154\.|217\.174\.|80\.89\.)/']
    ];

    foreach ($otherCountries as $countryCode => $ranges) {
        foreach ($ranges as $range) {
            if (preg_match($range, $ip)) {
                $ipCache[$ip] = $countryCode;
                return $countryCode;
            }
        }
    }

    // Попробуем API для точного определения страны
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 3,
                'user_agent' => 'UniQPaid/1.0'
            ]
        ]);

        // Используем ip-api.com (бесплатный, без ключа)
        $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=countryCode", false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['countryCode']) && !empty($data['countryCode'])) {
                $country = strtoupper($data['countryCode']);
                $ipCache[$ip] = $country;
                return $country;
            }
        }
    } catch (Exception $e) {
        error_log("GeoIP API error: " . $e->getMessage());
    }

    $ipCache[$ip] = 'Unknown';
    return 'Unknown';
}
