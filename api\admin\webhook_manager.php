<?php
/**
 * api/admin/webhook_manager.php
 * Управление webhook бота поддержки
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Не авторизован']);
    exit;
}

require_once __DIR__ . '/support_config.php';

// Устанавливаем заголовок для JSON
header('Content-Type: application/json');

// Получаем действие
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'check':
        checkWebhookStatus();
        break;
    
    case 'setup':
        setupWebhook();
        break;
    
    case 'remove':
        removeWebhook();
        break;
    
    default:
        echo json_encode(['error' => 'Неизвестное действие']);
        break;
}

/**
 * Проверка статуса webhook
 */
function checkWebhookStatus() {
    try {
        // Получаем информацию о боте
        $botInfo = supportTelegramRequest('getMe', []);
        
        // Получаем информацию о webhook
        $webhookInfo = supportTelegramRequest('getWebhookInfo', []);
        
        if ($botInfo && $webhookInfo) {
            echo json_encode([
                'success' => true,
                'bot_info' => $botInfo['result'],
                'webhook_info' => $webhookInfo['result']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Не удалось получить информацию от Telegram API'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Настройка webhook
 */
function setupWebhook() {
    try {
        // Устанавливаем webhook
        $result = supportTelegramRequest('setWebhook', [
            'url' => SUPPORT_WEBHOOK_URL,
            'drop_pending_updates' => true
        ]);
        
        if ($result && $result['ok']) {
            supportBotLog("INFO: Webhook успешно установлен через админку");
            
            echo json_encode([
                'success' => true,
                'webhook_url' => SUPPORT_WEBHOOK_URL,
                'bot_username' => SUPPORT_BOT_USERNAME,
                'message' => 'Webhook успешно настроен'
            ]);
        } else {
            $error = $result['description'] ?? 'Неизвестная ошибка';
            supportBotLog("ERROR: Ошибка установки webhook: {$error}");
            
            echo json_encode([
                'success' => false,
                'error' => $error
            ]);
        }
    } catch (Exception $e) {
        supportBotLog("ERROR: Исключение при установке webhook: " . $e->getMessage());
        
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Удаление webhook
 */
function removeWebhook() {
    try {
        // Удаляем webhook
        $result = supportTelegramRequest('deleteWebhook', [
            'drop_pending_updates' => true
        ]);
        
        if ($result && $result['ok']) {
            supportBotLog("INFO: Webhook успешно удален через админку");
            
            echo json_encode([
                'success' => true,
                'message' => 'Webhook успешно удален'
            ]);
        } else {
            $error = $result['description'] ?? 'Неизвестная ошибка';
            supportBotLog("ERROR: Ошибка удаления webhook: {$error}");
            
            echo json_encode([
                'success' => false,
                'error' => $error
            ]);
        }
    } catch (Exception $e) {
        supportBotLog("ERROR: Исключение при удалении webhook: " . $e->getMessage());
        
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
