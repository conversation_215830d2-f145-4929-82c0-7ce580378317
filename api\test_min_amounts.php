<?php
/**
 * Тест минимальных сумм для всех валют
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🧪 ТЕСТ МИНИМАЛЬНЫХ СУММ NOWPayments\n";
echo str_repeat("=", 50) . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Список валют для тестирования
$currencies = [
    'ton' => 'TON (Telegram)',
    'usdttrc20' => 'USDT (TRC20)',
    'usdt' => 'USDT (ERC20)',
    'btc' => 'Bitcoin (BTC)',
    'eth' => 'Ethereum (ETH)',
    'ltc' => 'Litecoin (LTC)',
    'bch' => 'Bitcoin Cash (BCH)',
    'xrp' => 'Ripple (XRP)',
    'ada' => 'Cardano (ADA)',
    'dot' => 'Polkadot (DOT)',
    'bnb' => 'Binance Coin (BNB)',
    'doge' => 'Dogecoin (DOGE)',
    'matic' => 'Polygon (MATIC)',
    'sol' => 'Solana (SOL)',
    'avax' => 'Avalanche (AVAX)',
    'usdc' => 'USD Coin (USDC)',
    'dai' => 'Dai (DAI)',
    'link' => 'Chainlink (LINK)',
    'uni' => 'Uniswap (UNI)',
    'atom' => 'Cosmos (ATOM)'
];

echo "📊 Проверяем минимальные суммы для " . count($currencies) . " валют:\n\n";

$results = [];
$successful = 0;
$failed = 0;

foreach ($currencies as $code => $name) {
    echo "🔍 Проверяем {$name} ({$code})... ";
    
    $minAmount = $api->getMinWithdrawalAmount($code);
    
    if ($minAmount !== null) {
        echo "✅ Минимум: {$minAmount}\n";
        $results[$code] = [
            'name' => $name,
            'min_amount' => $minAmount,
            'status' => 'success'
        ];
        $successful++;
    } else {
        echo "❌ Не удалось получить\n";
        $results[$code] = [
            'name' => $name,
            'min_amount' => null,
            'status' => 'failed'
        ];
        $failed++;
    }
    
    // Небольшая пауза между запросами
    usleep(100000); // 0.1 секунды
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📈 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:\n\n";

echo "✅ Успешно получено: {$successful} валют\n";
echo "❌ Недоступно: {$failed} валют\n\n";

if ($successful > 0) {
    echo "💰 ДОСТУПНЫЕ ВАЛЮТЫ:\n";
    echo str_repeat("-", 50) . "\n";
    printf("%-12s %-20s %s\n", "Код", "Название", "Минимум");
    echo str_repeat("-", 50) . "\n";
    
    foreach ($results as $code => $data) {
        if ($data['status'] === 'success') {
            printf("%-12s %-20s %s\n", $code, $data['name'], $data['min_amount']);
        }
    }
}

if ($failed > 0) {
    echo "\n❌ НЕДОСТУПНЫЕ ВАЛЮТЫ:\n";
    echo str_repeat("-", 30) . "\n";
    
    foreach ($results as $code => $data) {
        if ($data['status'] === 'failed') {
            echo "- {$data['name']} ({$code})\n";
        }
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🔧 РЕКОМЕНДАЦИИ:\n\n";

echo "1. Обновите массив knownMinimums в NOWPaymentsAPI.php\n";
echo "2. Обновите minAmounts в main.js\n";
echo "3. Обновите currencyData в withdrawal-tabs.js\n";
echo "4. Запускайте этот тест еженедельно для актуальных данных\n\n";

// Проверяем текущие настройки
echo "⚙️ ТЕКУЩИЕ НАСТРОЙКИ:\n";
echo "- Курс конвертации: " . CONVERSION_RATE . " USD за монету\n";
echo "- Минимальный баланс для вывода: " . MIN_BALANCE_FOR_WITHDRAWAL . " монет\n";
echo "- Показывать комиссии: " . (SHOW_FEES_TO_USER ? 'Да' : 'Нет') . "\n\n";

echo "✅ Тест завершен!\n";
?>
