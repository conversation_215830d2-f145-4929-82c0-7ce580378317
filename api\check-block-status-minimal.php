<?php
/**
 * api/check-block-status-minimal.php
 * Минимальная версия проверки блокировки
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Простая проверка без сложной логики
    echo json_encode([
        'success' => true,
        'blocked' => false,
        'message' => 'Minimal check - user not blocked',
        'timestamp' => time(),
        'server' => $_SERVER['HTTP_HOST'] ?? 'unknown'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'blocked' => false
    ]);
}
?>
