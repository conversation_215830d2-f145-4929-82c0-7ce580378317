/**
 * status-localization.js
 * Дополнительные функции для локализации статусных сообщений
 */

/**
 * Обновляет статусное сообщение с переводом
 */
function showLocalizedStatus(messageKey, type = "info", duration = 3000) {
    const statusMessageEl = document.getElementById('status-message');
    if (!statusMessageEl) return;

    // Получаем перевод
    const message = window.appLocalization ? window.appLocalization.get(messageKey) : messageKey;
    
    statusMessageEl.textContent = message;
    statusMessageEl.className = "status-message";
    
    if (type === "success") statusMessageEl.classList.add("success");
    if (type === "error") statusMessageEl.classList.add("error");
    if (type === "warning") statusMessageEl.classList.add("warning");
    if (type === "info") statusMessageEl.classList.add("info");
    
    console.log(`[StatusLocalization] ${type}: ${message}`);
    
    if (message && duration > 0) {
        setTimeout(() => {
            if (statusMessageEl.textContent === message) {
                statusMessageEl.textContent = '';
            }
        }, duration);
    }
}

/**
 * Переводит стандартные статусные сообщения
 */
function translateStatusMessage(message) {
    if (!window.appLocalization) return message;

    const statusTranslations = {
        // Русские сообщения
        'Ожидание инициализации...': 'status.waiting_initialization',
        'Инициализация приложения...': 'status.initializing_app',
        'Загрузка модулей...': 'status.loading_modules',
        'Загрузка дополнительных компонентов...': 'status.loading_components',
        'Приложение готово к работе': 'status.app_ready',
        'Обновление рекламных блоков...': 'status.updating_ads',
        'Загрузка...': 'status.loading',
        'Готово': 'status.ready',
        'Ожидание инициализации...': 'status.waiting_initialization',
        'Определение языка...': 'status.detecting_language',
        'Ошибка аутентификации': 'status.authentication_error',
        'Статистика обновлена': 'status.stats_updated',
        'Инициализация рекламы...': 'status.initializing_ads',
        
        // Английские сообщения
        'Waiting for initialization...': 'status.waiting_initialization',
        'Initializing application...': 'status.initializing_app',
        'Loading modules...': 'status.loading_modules',
        'Loading additional components...': 'status.loading_components',
        'Application ready to work': 'status.app_ready',
        'Updating ad blocks...': 'status.updating_ads',
        'Loading...': 'status.loading',
        'Ready': 'status.ready',
        'Waiting for initialization...': 'status.waiting_initialization',
        'Detecting language...': 'status.detecting_language',
        'Authentication error': 'status.authentication_error',
        'Statistics updated': 'status.stats_updated',
        'Initializing ads...': 'status.initializing_ads',
    };

    const translationKey = statusTranslations[message];
    if (translationKey) {
        return window.appLocalization.get(translationKey);
    }

    return message;
}

/**
 * Переводит сообщения о рекламе
 */
function translateAdMessage(message) {
    if (!window.appLocalization) return message;

    const adTranslations = {
        // Русские сообщения
        'Награда за клик по баннеру зачислена!': 'ads.banner_reward_credited',
        'Награда за просмотр видео зачислена!': 'ads.video_reward_credited',
        'Нет доступных баннеров. Попробуйте позже.': 'ads.no_ads_available',
        
        // Английские сообщения
        'Banner click reward credited!': 'ads.banner_reward_credited',
        'Video viewing reward credited!': 'ads.video_reward_credited',
        'No banners available. Try again later.': 'ads.no_ads_available'
    };

    const translationKey = adTranslations[message];
    if (translationKey) {
        return window.appLocalization.get(translationKey);
    }

    return message;
}

/**
 * Переводит сообщения валидации
 */
function translateValidationMessage(message) {
    if (!window.appLocalization) return message;

    const validationTranslations = {
        // Русские сообщения
        'Недостаточно средств на балансе': 'earnings.insufficient_balance',
        'Адрес кошелька слишком короткий': 'earnings.wallet_address_short',
        'Введите корректную сумму для вывода': 'earnings.enter_correct_amount',
        'Неверный формат адреса кошелька': 'validation.invalid_address',
        
        // Английские сообщения
        'Insufficient balance': 'earnings.insufficient_balance',
        'Wallet address is too short': 'earnings.wallet_address_short',
        'Enter correct withdrawal amount': 'earnings.enter_correct_amount',
        'Invalid wallet address format': 'validation.invalid_address'
    };

    const translationKey = validationTranslations[message];
    if (translationKey) {
        return window.appLocalization.get(translationKey);
    }

    return message;
}

/**
 * Универсальная функция для показа переведенных уведомлений
 */
function showTranslatedNotification(messageKey, type = 'info', duration = 3000) {
    if (window.RefinedCyberpunkEffects && window.RefinedCyberpunkEffects.showRefinedNotification) {
        const message = window.appLocalization ? window.appLocalization.get(messageKey) : messageKey;
        window.RefinedCyberpunkEffects.showRefinedNotification(message, type);
    } else if (window.VibrantCyberpunkEffects && window.VibrantCyberpunkEffects.showVibrantNotification) {
        const message = window.appLocalization ? window.appLocalization.get(messageKey) : messageKey;
        window.VibrantCyberpunkEffects.showVibrantNotification(message, type);
    } else {
        // Fallback к обычному статусному сообщению
        showLocalizedStatus(messageKey, type, duration);
    }
}

// Экспортируем функции в глобальную область видимости
window.showLocalizedStatus = showLocalizedStatus;
window.translateStatusMessage = translateStatusMessage;
window.translateAdMessage = translateAdMessage;
window.translateValidationMessage = translateValidationMessage;
window.showTranslatedNotification = showTranslatedNotification;

console.log('📝 [StatusLocalization] Система локализации статусных сообщений загружена');
