// === main-modular.js ===
// Модульная версия основного файла приложения

// Импорты модулей
import { 
  API_BASE_URL, 
  DEBUG_MODE, 
  BOT_USERNAME,
  updateCurrentUserId,
  updateAdsController,
  appSettings,
  calculatorData
} from './config.js';

import { 
  tg,
  statusMessageEl,
  userNameEl
} from './dom-elements.js';

import {
  updateBalanceDisplay,
  getCurrentUserBalance
} from './balance-manager.js';

import {
  initNavigation,
  showMainContent
} from './navigation.js';

import {
  loadAppSettings,
  loadUserData,
  submitAdView
} from './api-client.js';

import {
  initCalculator,
  updateCalculatorDisplay,
  updateBalanceCheck
} from './calculator.js';

// Импорт данных о валютах из существующего файла
import './currency-defaults.js';

// Глобальные переменные (минимальный набор)
let currentUserId = null;

/**
 * Отображает статусное сообщение
 * @param {string} message - Текст сообщения
 * @param {'info' | 'success' | 'error'} type - Тип сообщения
 */
function showStatus(message, type = "info") {
  if (!statusMessageEl) {
    console.warn("Элемент status-message не найден.");
    return;
  }
  statusMessageEl.textContent = message;
  statusMessageEl.className = "status-message";
  if (type === "success") {
    statusMessageEl.classList.add("success");
  } else if (type === "error") {
    statusMessageEl.classList.add("error");
  }
  console.log(`Status [${type}]: ${message}`);
}

/**
 * Инициализирует Telegram WebApp
 */
function initTelegramWebApp() {
  if (!tg) {
    console.error("Telegram WebApp не доступен");
    return false;
  }

  try {
    // Настройка WebApp
    tg.ready();
    tg.expand();
    
    // Получаем данные пользователя
    const user = tg.initDataUnsafe?.user;
    if (user) {
      currentUserId = user.id.toString();
      updateCurrentUserId(currentUserId);
      
      // Отображаем имя пользователя
      if (userNameEl) {
        const displayName = user.first_name + (user.last_name ? ` ${user.last_name}` : '');
        userNameEl.textContent = displayName;
      }
      
      console.log(`👤 Пользователь: ${user.first_name} (ID: ${currentUserId})`);
      return true;
    } else {
      console.warn("Данные пользователя Telegram недоступны");
      return false;
    }
  } catch (error) {
    console.error("Ошибка инициализации Telegram WebApp:", error);
    return false;
  }
}

/**
 * Загружает данные пользователя с сервера
 */
async function loadUserDataFromServer() {
  if (!currentUserId) {
    console.warn("ID пользователя не определен");
    return;
  }

  try {
    showStatus("Загрузка данных пользователя...", "info");
    
    const userData = await loadUserData(currentUserId);
    
    if (userData) {
      // Обновляем баланс
      updateBalanceDisplay(userData.balance || 0);
      
      showStatus("Данные загружены успешно", "success");
      console.log("✅ Данные пользователя загружены:", userData);
    } else {
      showStatus("Не удалось загрузить данные пользователя", "error");
    }
  } catch (error) {
    console.error("Ошибка загрузки данных пользователя:", error);
    showStatus("Ошибка загрузки данных", "error");
  }
}

/**
 * Инициализирует рекламный SDK
 */
async function initAdSDK() {
  try {
    // Динамический импорт модуля рекламы
    const { initAdsManager } = await import('./ads-manager.js');
    const adsController = await initAdsManager();
    
    if (adsController) {
      updateAdsController(adsController);
      console.log("📺 Рекламный SDK инициализирован");
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Ошибка инициализации рекламного SDK:", error);
    return false;
  }
}

/**
 * Инициализирует обработчики событий
 */
function initEventHandlers() {
  // Обработчик для калькулятора
  const calcAmountInput = document.getElementById('calc-amount');
  if (calcAmountInput) {
    calcAmountInput.addEventListener('input', (e) => {
      const amount = parseInt(e.target.value) || 0;
      updateCalculatorDisplay(amount);
      updateBalanceCheck(amount);
    });
  }

  // Обработчики для вкладок валют
  const currencyTabs = document.querySelectorAll('.currency-tab');
  currencyTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const currency = tab.dataset.currency;
      if (currency) {
        import('./calculator.js').then(({ switchCurrencyTab }) => {
          switchCurrencyTab(currency);
        });
      }
    });
  });

  console.log("🎯 Обработчики событий инициализированы");
}

/**
 * Инициализирует дополнительные модули
 */
async function initAdditionalModules() {
  try {
    // Инициализация модуля вывода средств
    const { initWithdrawal } = await import('./withdrawal.js');
    await initWithdrawal();

    // Инициализация реферальной системы
    const { initReferralSystem } = await import('./referral-system.js');
    await initReferralSystem();

    // Инициализация табов вывода средств
    if (window.initWithdrawalTabs) {
      window.initWithdrawalTabs();
    }

    console.log("🔧 Дополнительные модули инициализированы");
  } catch (error) {
    console.error("Ошибка инициализации дополнительных модулей:", error);
  }
}

/**
 * Основная функция инициализации приложения
 */
async function initApp() {
  console.log("🚀 Запуск модульного приложения UniQPaid...");
  
  try {
    // 1. Инициализация Telegram WebApp
    const telegramInitialized = initTelegramWebApp();
    if (!telegramInitialized) {
      console.warn("⚠️ Telegram WebApp не инициализирован, продолжаем в режиме отладки");
    }

    // 2. Загрузка настроек приложения
    showStatus("Загрузка настроек...", "info");
    await loadAppSettings();

    // 3. Инициализация навигации
    initNavigation();

    // 4. Инициализация калькулятора
    initCalculator();

    // 5. Инициализация обработчиков событий
    initEventHandlers();

    // 6. Загрузка данных пользователя
    if (currentUserId) {
      await loadUserDataFromServer();
    }

    // 7. Инициализация рекламного SDK
    await initAdSDK();

    // 8. Инициализация дополнительных модулей
    await initAdditionalModules();

    // 9. Показываем главную страницу
    showMainContent();

    showStatus("Приложение готово к работе", "success");
    console.log("✅ Модульное приложение успешно инициализировано");

  } catch (error) {
    console.error("❌ Критическая ошибка инициализации:", error);
    showStatus("Ошибка инициализации приложения", "error");
  }
}

/**
 * Обработчик загрузки DOM
 */
document.addEventListener('DOMContentLoaded', () => {
  console.log("📄 DOM загружен, запуск инициализации...");
  initApp();
});

/**
 * Обработчик ошибок
 */
window.addEventListener('error', (event) => {
  console.error("🚨 Глобальная ошибка:", event.error);
  showStatus("Произошла ошибка в приложении", "error");
});

/**
 * Обработчик необработанных промисов
 */
window.addEventListener('unhandledrejection', (event) => {
  console.error("🚨 Необработанная ошибка промиса:", event.reason);
  showStatus("Ошибка выполнения операции", "error");
});

// Экспорт основных функций для глобального доступа
window.UniQPaid = {
  showStatus,
  getCurrentUserBalance,
  updateBalanceDisplay,
  calculatorData,
  appSettings
};

console.log("📦 Модульная система UniQPaid загружена");
