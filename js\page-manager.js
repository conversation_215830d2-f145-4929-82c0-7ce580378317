// === page-manager.js ===
// Файл: js/page-manager.js
// Описание: Управляет навигацией и вызывает действия при смене страниц.

class PageManager {
  constructor() {
    this.isTransitioning = false;
    this.pages = {
      main: document.getElementById("main-content"),
      earn: document.getElementById("earn-section"),
      friends: document.getElementById("friends-section")
    };
    this.navButtons = {
      home: document.getElementById('nav-home'),
      earn: document.getElementById('nav-earn'),
      friends: document.getElementById('nav-friends'),
    };
    this.currentPageId = 'main';
    this.currentPageElement = this.pages.main;

    // Проверяем, что все элементы найдены
    const missingPages = Object.entries(this.pages).filter(([key, element]) => !element).map(([key]) => key);
    const missingButtons = Object.entries(this.navButtons).filter(([key, element]) => !element).map(([key]) => key);

    if (missingPages.length > 0) {
      console.warn('[PageManager] Не найдены страницы:', missingPages);
    }
    if (missingButtons.length > 0) {
      console.warn('[PageManager] Не найдены кнопки навигации:', missingButtons);
    }

    // ИСПРАВЛЕНИЕ: Устанавливаем правильное начальное состояние
    this.initializePageStates();

    // Экспорт переменных для обратной совместимости (из оригинала)
    window.isTransitioning = this.isTransitioning;
    window.currentPageElement = this.currentPageElement;
  }

  /**
   * Инициализирует правильное состояние страниц
   */
  initializePageStates() {
    console.log('[PageManager] Инициализация состояний страниц');

    // Убеждаемся, что все страницы скрыты кроме главной
    Object.entries(this.pages).forEach(([pageId, pageElement]) => {
      if (pageElement) {
        if (pageId === 'main') {
          pageElement.classList.remove('page-hidden');
          pageElement.classList.add('active-section');
          console.log('[PageManager] Главная страница активна');
        } else {
          pageElement.classList.add('page-hidden');
          pageElement.classList.remove('active-section');
          console.log(`[PageManager] Страница ${pageId} скрыта`);
        }
      }
    });

    // Устанавливаем активную кнопку навигации
    if (this.navButtons.home) {
      this.navButtons.home.classList.add('active');
    }
  }

  init() {
    console.log('[PageManager] Инициализация.');
    this.setupEventListeners();
    this.restoreLastPage();
  }
  
  setupEventListeners() {
    console.log('[PageManager] Настройка обработчиков событий...');

    if (this.navButtons.home) {
      this.navButtons.home.addEventListener('click', () => {
        console.log('[PageManager] Клик по кнопке Главная');
        this.switchToPage('main');
      });
      console.log('[PageManager] ✅ Обработчик для кнопки Главная установлен');
    } else {
      console.warn('[PageManager] ❌ Кнопка Главная не найдена');
    }

    if (this.navButtons.earn) {
      this.navButtons.earn.addEventListener('click', () => {
        console.log('[PageManager] Клик по кнопке Заработок');
        this.switchToPage('earn');
      });
      console.log('[PageManager] ✅ Обработчик для кнопки Заработок установлен');
    } else {
      console.warn('[PageManager] ❌ Кнопка Заработок не найдена');
    }

    if (this.navButtons.friends) {
      this.navButtons.friends.addEventListener('click', () => {
        console.log('[PageManager] Клик по кнопке Друзья');
        this.switchToPage('friends');
      });
      console.log('[PageManager] ✅ Обработчик для кнопки Друзья установлен');
    } else {
      console.warn('[PageManager] ❌ Кнопка Друзья не найдена');
    }

    const headerBalance = document.getElementById("header-balance-info");
    if (headerBalance) {
      headerBalance.addEventListener("click", () => {
        console.log('[PageManager] Клик по балансу в шапке');
        this.switchToPage('earn');
      });
      console.log('[PageManager] ✅ Обработчик для баланса в шапке установлен');
    } else {
      console.warn('[PageManager] ❌ Баланс в шапке не найден');
    }
  }

  /**
   * Переключает видимую страницу с CSS-анимацией (v6 - сброс inline display).
   * @param {string} pageId ID страницы для переключения
   */
  switchToPage(pageId) {
    const nextPageElement = this.pages[pageId];

    // Правильно определяем кнопку навигации
    let activeNavButton;
    if (pageId === 'main') activeNavButton = this.navButtons.home;
    else if (pageId === 'earn') activeNavButton = this.navButtons.earn;
    else if (pageId === 'friends') activeNavButton = this.navButtons.friends;

    if (!nextPageElement || nextPageElement === this.currentPageElement || this.isTransitioning) {
      console.log(`[PageManager] Переключение отменено: next=${pageId}, current=${this.currentPageId}, transitioning=${this.isTransitioning}`);
      return;
    }

    console.log(`[PageManager] Переключение: ${this.currentPageId} -> ${pageId}`);
    console.log(`[PageManager] Элементы найдены: page=${!!nextPageElement}, button=${!!activeNavButton}`);

    console.log(`[PageManager] Анимация v6: ${this.currentPageId} -> ${pageId}`);
    this.isTransitioning = true;
    window.isTransitioning = true; // Обновляем глобальную переменную

    const pageOutElement = this.currentPageElement;

    // 1. Обновляем кнопку навигации
    this.updateActiveNavButton(activeNavButton);

    // --- ИСПРАВЛЕНИЕ: Работаем с CSS классами вместо inline стилей ---

    // Скрываем все страницы
    Object.values(this.pages).forEach(page => {
      if (page) {
        page.classList.add('page-hidden');
        page.classList.remove('active-section');
      }
    });

    // Показываем нужную страницу
    nextPageElement.classList.remove('page-hidden');
    nextPageElement.classList.add('active-section');

    // Обновляем состояние сразу
    this.currentPageElement = nextPageElement;
    window.currentPageElement = nextPageElement;
    this.currentPageId = pageId;
    this.isTransitioning = false;
    window.isTransitioning = false;

    console.log(`[PageManager] Переключение завершено: ${pageId}`);

    // Выполняем специальные действия для страниц
    this.onPageSwitched(pageId);
  }
  
  onPageSwitched(pageId) {
    // Специальные действия для страницы заработка (из оригинала)
    if (pageId === 'earn') {
      this.updateWithdrawalSection();
      this.initWithdrawalRecommendationsTooltip();

      // ПРИНУДИТЕЛЬНАЯ ЗАГРУЗКА ИСТОРИИ ВЫПЛАТ
      console.log('[PageManager] Принудительная загрузка истории выплат');

      // Загружаем историю сразу
      if (window.withdrawalManager && window.withdrawalManager.loadAndDisplayHistory) {
        window.withdrawalManager.loadAndDisplayHistory();
      }

      // Затем проверяем и обновляем статусы
      if (window.withdrawalManager && window.withdrawalManager.checkAndUpdateStatuses) {
        window.withdrawalManager.checkAndUpdateStatuses().then(() => {
          console.log('[PageManager] Статусы обновлены, перезагружаем историю');
          if (window.withdrawalManager && window.withdrawalManager.loadAndDisplayHistory) {
            window.withdrawalManager.loadAndDisplayHistory();
          }
        }).catch((error) => {
          console.log('[PageManager] Ошибка обновления статусов:', error);
          // Если проверка статусов не удалась, все равно загружаем историю
          if (window.withdrawalManager && window.withdrawalManager.loadAndDisplayHistory) {
            window.withdrawalManager.loadAndDisplayHistory();
          }
        });
      }

      // ИСПРАВЛЕНИЕ: НЕ переключаемся на калькулятор автоматически
      // Пользователь должен сам выбрать нужный раздел
      console.log('[PageManager] Страница заработка открыта, ожидаем выбор пользователя');
    }

    // Специальные действия для страницы друзей
    if (pageId === 'friends' && window.referralManager) {
      if (window.referralManager.loadReferralStats) {
        window.referralManager.loadReferralStats();
      } else if (window.referralManager.loadStats) {
        window.referralManager.loadStats();
      }
    }

    // Сохраняем последнюю активную страницу
    localStorage.setItem('lastActivePage', pageId);
  }

  // Обновление секции вывода (из оригинала)
  updateWithdrawalSection() {
    console.log('[PageManager] Обновление секции вывода...');
    // Здесь может быть дополнительная логика обновления
  }

  // Инициализация подсказок для рекомендаций вывода (из оригинала)
  initWithdrawalRecommendationsTooltip() {
    console.log('[PageManager] Инициализация подсказок для рекомендаций...');
    // Здесь может быть логика инициализации подсказок
  }

  // Методы для показа конкретных страниц (из оригинала для обратной совместимости)
  showMainContent() {
    this.switchToPage('main');
  }

  showEarnSection() {
    this.switchToPage('earn');
  }

  showFriendsSection() {
    this.switchToPage('friends');
  }

  // Обновление активной кнопки навигации
  updateActiveNavButton(activeButton) {
    Object.values(this.navButtons).forEach((button) => {
      if (button) button.classList.remove("active");
    });
    if (activeButton) {
      activeButton.classList.add("active");
    }
  }

  /**
   * Сохраняет текущую активную страницу в localStorage.
   */
  saveCurrentPage() {
    if (this.currentPageId) {
      localStorage.setItem('lastActivePage', this.currentPageId);
      console.log(`[PageManager] Страница ${this.currentPageId} сохранена.`);
    }
  }

  /**
   * Восстанавливает последнюю активную страницу из localStorage.
   */
  restoreLastPage() {
    const lastPageId = localStorage.getItem('lastActivePage');
    if (lastPageId && this.pages[lastPageId]) {
      console.log(`[PageManager] Восстановление страницы: ${lastPageId}`);
      this.switchToPage(lastPageId);
      localStorage.removeItem('lastActivePage');
    }
  }
}

try {
  window.pageManager = new PageManager();

  // Экспорт функций для обратной совместимости (из оригинала)
  window.switchPageAnimated = function(nextPageElement, activeNavButton) {
    // Определяем pageId по элементу
    let pageId = 'main';
    if (nextPageElement === window.pageManager.pages.earn) pageId = 'earn';
    else if (nextPageElement === window.pageManager.pages.friends) pageId = 'friends';

    window.pageManager.switchToPage(pageId);
  };

  window.showMainContent = () => window.pageManager.showMainContent();
  window.showEarnSection = () => window.pageManager.showEarnSection();
  window.showFriendsSection = () => window.pageManager.showFriendsSection();
  window.updateActiveNavButton = (activeButton) => window.pageManager.updateActiveNavButton(activeButton);

  console.log('📄 [PageManager] Менеджер страниц загружен с полной интеграцией.');
} catch (error) {
  console.error('📄 [PageManager] Ошибка при создании менеджера страниц:', error);
  // Создаем заглушку, чтобы не блокировать загрузку других модулей
  window.pageManager = {
    init: () => console.log('[PageManager] Заглушка инициализирована'),
    switchToPage: (pageId) => console.log(`[PageManager] Заглушка переключения на ${pageId}`),
    saveCurrentPage: () => console.log('[PageManager] Заглушка сохранения страницы'),
    restoreLastPage: () => console.log('[PageManager] Заглушка восстановления страницы'),
    showMainContent: () => console.log('[PageManager] Заглушка главной страницы'),
    showEarnSection: () => console.log('[PageManager] Заглушка страницы заработка'),
    showFriendsSection: () => console.log('[PageManager] Заглушка страницы друзей')
  };

  // Заглушки для глобальных функций
  window.switchPageAnimated = () => console.log('[PageManager] Заглушка switchPageAnimated');
  window.showMainContent = () => console.log('[PageManager] Заглушка showMainContent');
  window.showEarnSection = () => console.log('[PageManager] Заглушка showEarnSection');
  window.showFriendsSection = () => console.log('[PageManager] Заглушка showFriendsSection');
  window.updateActiveNavButton = () => console.log('[PageManager] Заглушка updateActiveNavButton');
}