<?php
// api/logAdClick.php

// Простая регистрация кликов по рекламе
// Этот скрипт регистрирует событие клика по рекламе для аналитики.

header('Content-Type: application/json');

// Получаем данные из POST-запроса
$input = json_decode(file_get_contents('php://input'), true);

$userId = $input['user_id'] ?? null;
$adType = $input['ad_type'] ?? 'unknown';
$timestamp = date('Y-m-d H:i:s');

if (!$userId) {
    // Если user_id отсутствует, мы не можем зарегистрировать клик.
    // В реальном приложении здесь может быть более строгая проверка.
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'User ID is required.']);
    exit;
}

// Формируем строку для лога
$logMessage = "Timestamp: $timestamp, UserID: $userId, AdType: $adType, Status: Clicked
";

// Указываем путь к файлу лога
// Убедитесь, что у веб-сервера есть права на запись в этот файл.
$logFilePath = __DIR__ . '/ad_requests.log';

// Записываем информацию в файл лога
// Использование FILE_APPEND для добавления в конец файла без его перезаписи
if (file_put_contents($logFilePath, $logMessage, FILE_APPEND | LOCK_EX) === false) {
    // Обработка ошибки, если запись в файл не удалась
    http_response_code(500);
    // Не выводим детали ошибки пользователю из соображений безопасности
    echo json_encode(['status' => 'error', 'message' => 'Failed to log ad click.']);
} else {
    // Успешный ответ
    echo json_encode(['status' => 'success', 'message' => 'Ad click logged successfully.']);
}