<?php
/**
 * Принудительное обновление статусов всех выплат
 */

// Включаем логирование ошибок в файл
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php-error.log');
error_reporting(E_ALL);

// Устанавливаем обработчик ошибок для перехвата фатальных ошибок
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        // Проверяем, не были ли уже отправлены заголовки
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code(500);
        }
        // Формируем JSON с ошибкой
        echo json_encode([
            'success' => false, 
            'error' => 'Критическая ошибка сервера.', 
            'details' => $error['message']
        ]);
        // Логируем ошибку
        error_log("FATAL in force_update_withdrawals.php: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);
    }
});

// Проверяем, нужен ли JSON ответ
$jsonMode = isset($_GET['json']);

if ($jsonMode) {
    header('Content-Type: application/json');
}

try {
    // --- Подключение зависимостей с проверкой ---
    if (!(@include_once __DIR__ . '/config.php')) throw new Exception("Не удалось подключить config.php");
    if (!(@include_once __DIR__ . '/db_mock.php')) throw new Exception("Не удалось подключить db_mock.php");
    if (!(@include_once __DIR__ . '/NOWPaymentsAPI.php')) throw new Exception("Не удалось подключить NOWPaymentsAPI.php");
    if (!(@include_once __DIR__ . '/admin/auth.php')) throw new Exception("Не удалось подключить auth.php");

    // --- Авторизация ---
    if ($jsonMode) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        if (!isAuthenticated()) {
            http_response_code(403);
            throw new Exception('Требуется авторизация администратора');
        }
    }

    if (!$jsonMode) {
        echo "Начинаем принудительное обновление статусов выплат...\n";
    }
    
    // --- Основная логика ---
    $userData = loadUserData();
    if (!$userData) {
        throw new Exception("Не удалось загрузить данные пользователей или данные пусты");
    }
    
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    $totalUpdated = 0;
    $totalChecked = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || empty($user['withdrawals'])) {
            continue;
        }
        
        if (!$jsonMode) {
            echo "Проверяем пользователя {$userId}...\n";
        }

        foreach ($user['withdrawals'] as &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';

            if (!$payoutId) continue;

            $totalChecked++;
            if (!$jsonMode) {
                echo "  Проверяем выплату {$payoutId} (текущий статус: {$currentStatus})...\n";
            }
            
            $statusResponse = $api->getPayoutStatus($payoutId);
            
            if ($statusResponse && isset($statusResponse['status'])) {
                $newStatus = strtolower($statusResponse['status']);

                if ($newStatus !== $currentStatus) {
                    $withdrawal['status'] = $newStatus;
                    $withdrawal['updated_at'] = date('Y-m-d H:i:s');

                    // Доп. инфо
                    if (isset($statusResponse['amount'])) $withdrawal['actual_amount'] = $statusResponse['amount'];
                    if (isset($statusResponse['fee'])) $withdrawal['actual_fee'] = $statusResponse['fee'];
                    if (isset($statusResponse['hash'])) $withdrawal['transaction_hash'] = $statusResponse['hash'];

                    $totalUpdated++;
                    if (!$jsonMode) echo "    ✅ Статус обновлен: {$currentStatus} -> {$newStatus}\n";
                }
            }
        }
    }

    if ($totalUpdated > 0) {
        if (!saveUserData($userData)) {
            throw new Exception("Ошибка сохранения данных пользователей после обновления статусов");
        }
    }

    if (!$jsonMode) {
        echo "\n📊 Итоги: Проверено {$totalChecked}, Обновлено {$totalUpdated}\n";
    }
    
    if ($jsonMode) {
        echo json_encode([
            'success' => true,
            'checked' => $totalChecked,
            'updated' => $totalUpdated,
            'message' => "Проверено: {$totalChecked}, обновлено: {$totalUpdated}"
        ]);
    }

} catch (Exception $e) {
    error_log("ERROR in force_update_withdrawals.php: " . $e->getMessage());
    if (!headers_sent()) {
        http_response_code(500);
    }
    if ($jsonMode) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>