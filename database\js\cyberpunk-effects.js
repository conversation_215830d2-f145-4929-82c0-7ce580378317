/* ======================================== */
/* CYBERPUNK EFFECTS - Динамические эффекты */
/* ======================================== */

// Инициализация киберпанк эффектов
function initCyberpunkEffects() {
  console.log('🚀 Инициализация киберпанк эффектов...');
  
  // Создаем плавающие частицы
  createFloatingParticles();
  
  // Добавляем глитч эффекты
  addGlitchEffects();
  
  // Инициализируем сканирующие линии
  initScannerEffects();
  
  // Добавляем интерактивные эффекты
  addInteractiveEffects();
  
  console.log('✨ Киберпанк эффекты активированы!');
}

// Создание плавающих частиц
function createFloatingParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'cyber-particles';
  document.body.appendChild(particlesContainer);
  
  // Создаем 20 частиц
  for (let i = 0; i < 20; i++) {
    const particle = document.createElement('div');
    particle.className = 'cyber-particle';
    
    // Случайное позиционирование
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    
    // Случайная задержка анимации
    particle.style.animationDelay = Math.random() * 6 + 's';
    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
    
    particlesContainer.appendChild(particle);
  }
}

// Добавление глитч эффектов
function addGlitchEffects() {
  // Добавляем глитч эффект к заголовкам
  const headers = document.querySelectorAll('h1, h2, h3');
  headers.forEach(header => {
    // Случайный глитч каждые 10-30 секунд
    setInterval(() => {
      if (Math.random() < 0.3) { // 30% шанс
        header.classList.add('cyber-glitch');
        setTimeout(() => {
          header.classList.remove('cyber-glitch');
        }, 300);
      }
    }, Math.random() * 20000 + 10000);
  });
  
  // Глитч эффект для баланса при изменении
  const balanceElements = document.querySelectorAll('.balance-amount');
  balanceElements.forEach(element => {
    const observer = new MutationObserver(() => {
      element.classList.add('cyber-glitch');
      setTimeout(() => {
        element.classList.remove('cyber-glitch');
      }, 200);
    });
    
    observer.observe(element, { childList: true, characterData: true, subtree: true });
  });
}

// Инициализация сканирующих эффектов
function initScannerEffects() {
  // Добавляем сканер к кнопкам при наведении
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      if (!button.classList.contains('cyber-scanner')) {
        button.classList.add('cyber-scanner');
      }
    });
    
    button.addEventListener('mouseleave', () => {
      setTimeout(() => {
        button.classList.remove('cyber-scanner');
      }, 3000);
    });
  });
}

// Добавление интерактивных эффектов
function addInteractiveEffects() {
  // Эффект пульсации для важных элементов
  const importantElements = document.querySelectorAll('.balance-info, .status-message');
  importantElements.forEach(element => {
    element.classList.add('cyber-glow-pulse');
  });
  
  // Добавляем киберпанк границы к карточкам
  const cards = document.querySelectorAll('.withdrawal-calculator, .friends-block, .referral-stats');
  cards.forEach(card => {
    card.classList.add('cyber-border');
  });
  
  // Эффект для навигационных кнопок
  const navButtons = document.querySelectorAll('.nav-button');
  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Создаем волновой эффект
      createRippleEffect(button);
    });
  });
}

// Создание волнового эффекта при клике
function createRippleEffect(element) {
  const ripple = document.createElement('div');
  ripple.style.position = 'absolute';
  ripple.style.borderRadius = '50%';
  ripple.style.background = 'rgba(0, 255, 255, 0.6)';
  ripple.style.transform = 'scale(0)';
  ripple.style.animation = 'ripple 0.6s linear';
  ripple.style.left = '50%';
  ripple.style.top = '50%';
  ripple.style.width = '20px';
  ripple.style.height = '20px';
  ripple.style.marginLeft = '-10px';
  ripple.style.marginTop = '-10px';
  ripple.style.pointerEvents = 'none';
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

// Добавляем CSS для волнового эффекта
function addRippleCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
}

// Эффект печатающегося текста
function typewriterEffect(element, text, speed = 50) {
  element.textContent = '';
  let i = 0;
  
  const timer = setInterval(() => {
    if (i < text.length) {
      element.textContent += text.charAt(i);
      i++;
    } else {
      clearInterval(timer);
    }
  }, speed);
}

// Эффект матричного дождя (опциональный, для особых случаев)
function createMatrixRain() {
  const canvas = document.createElement('canvas');
  canvas.style.position = 'fixed';
  canvas.style.top = '0';
  canvas.style.left = '0';
  canvas.style.width = '100%';
  canvas.style.height = '100%';
  canvas.style.pointerEvents = 'none';
  canvas.style.zIndex = '-2';
  canvas.style.opacity = '0.1';
  
  document.body.appendChild(canvas);
  
  const ctx = canvas.getContext('2d');
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  
  const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
  const matrixArray = matrix.split("");
  
  const fontSize = 10;
  const columns = canvas.width / fontSize;
  
  const drops = [];
  for (let x = 0; x < columns; x++) {
    drops[x] = 1;
  }
  
  function draw() {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    ctx.fillStyle = '#00ff00';
    ctx.font = fontSize + 'px monospace';
    
    for (let i = 0; i < drops.length; i++) {
      const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
      ctx.fillText(text, i * fontSize, drops[i] * fontSize);
      
      if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
        drops[i] = 0;
      }
      drops[i]++;
    }
  }
  
  setInterval(draw, 35);
}

// Функция для добавления звуковых эффектов (опционально)
function addSoundEffects() {
  // Создаем аудио контекст для синтетических звуков
  if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    // Звук при клике на кнопку
    function playClickSound() {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    }
    
    // Добавляем звук к кнопкам
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('action-button') || e.target.classList.contains('nav-button')) {
        playClickSound();
      }
    });
  }
}

// Функция для создания голографического эффекта
function addHolographicEffect(element) {
  element.style.background = `
    linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 0, 255, 0.1) 50%, transparent 70%)
  `;
  element.style.backgroundSize = '20px 20px';
  element.style.animation = 'holographic 2s linear infinite';
}

// Добавляем CSS для голографического эффекта
function addHolographicCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes holographic {
      0% { background-position: 0 0, 0 0; }
      100% { background-position: 20px 20px, -20px -20px; }
    }
  `;
  document.head.appendChild(style);
}

// Дополнительные эффекты для улучшения киберпанк атмосферы
function enhanceCyberpunkAtmosphere() {
  // Добавляем голографический эффект к заголовкам
  const headers = document.querySelectorAll('h2, h3');
  headers.forEach(header => {
    header.classList.add('hologram-text');
  });

  // Добавляем эффект печатающегося текста к статус сообщениям
  const statusMessages = document.querySelectorAll('.status-message');
  statusMessages.forEach(message => {
    const originalText = message.textContent;
    message.classList.add('typewriter');
    typewriterEffect(message, originalText, 30);
  });

  // Добавляем энергетическое поле к важным кнопкам
  const importantButtons = document.querySelectorAll('#request-withdrawal-button, #share-app-button');
  importantButtons.forEach(button => {
    button.classList.add('energy-field');
  });

  // Добавляем матричный эффект к числовым значениям
  const numericValues = document.querySelectorAll('.balance-amount, .stat-value');
  numericValues.forEach(value => {
    value.classList.add('matrix-text');
  });
}

// Функция для создания киберпанк курсора
function createCyberpunkCursor() {
  const cursor = document.createElement('div');
  cursor.className = 'cyber-cursor';
  cursor.style.cssText = `
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #00ffff 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
  `;
  document.body.appendChild(cursor);

  document.addEventListener('mousemove', (e) => {
    cursor.style.left = e.clientX - 10 + 'px';
    cursor.style.top = e.clientY - 10 + 'px';
  });

  // Эффект при клике
  document.addEventListener('click', () => {
    cursor.style.transform = 'scale(2)';
    setTimeout(() => {
      cursor.style.transform = 'scale(1)';
    }, 150);
  });
}

// Функция для добавления киберпанк эффектов к формам
function enhanceForms() {
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      input.classList.add('cyber-focus');
    });

    input.addEventListener('blur', () => {
      input.classList.remove('cyber-focus');
    });

    // Добавляем эффект сканирования при вводе
    input.addEventListener('input', () => {
      input.classList.add('scanning');
      setTimeout(() => {
        input.classList.remove('scanning');
      }, 1000);
    });
  });
}

// Функция для создания киберпанк уведомлений
function showCyberNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `cyber-notification cyber-notification-${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(10, 10, 10, 0.9);
    border: 2px solid var(--cyber-neon-blue);
    border-radius: 10px;
    padding: 15px 20px;
    color: var(--cyber-neon-blue);
    font-weight: 600;
    z-index: 10000;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
  `;

  if (type === 'success') {
    notification.style.borderColor = 'var(--cyber-neon-green)';
    notification.style.color = 'var(--cyber-neon-green)';
    notification.style.boxShadow = '0 0 20px rgba(0, 255, 0, 0.3)';
  } else if (type === 'error') {
    notification.style.borderColor = 'var(--cyber-neon-pink)';
    notification.style.color = 'var(--cyber-neon-pink)';
    notification.style.boxShadow = '0 0 20px rgba(255, 0, 255, 0.3)';
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // Анимация появления
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // Автоматическое скрытие
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}

// Инициализация всех эффектов при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка для загрузки основных стилей
  setTimeout(() => {
    addRippleCSS();
    addHolographicCSS();
    initCyberpunkEffects();
    enhanceCyberpunkAtmosphere();
    enhanceForms();

    // Создаем киберпанк курсор (только для десктопа)
    if (window.innerWidth > 768) {
      createCyberpunkCursor();
    }

    // Показываем приветственное уведомление
    setTimeout(() => {
      showCyberNotification('🚀 Киберпанк режим активирован!', 'success');
    }, 1000);

    // Опциональные эффекты (раскомментировать при необходимости)
    // createMatrixRain();
    // addSoundEffects();
  }, 500);
});

// Экспорт функций для использования в других скриптах
window.CyberpunkEffects = {
  typewriterEffect,
  createRippleEffect,
  addHolographicEffect,
  showCyberNotification,
  enhanceCyberpunkAtmosphere,
  createCyberpunkCursor,
  playClickSound: () => {}, // Заглушка, будет переопределена если звук включен
};
