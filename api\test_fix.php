<?php
/**
 * test_fix.php
 * Простая диагностика системы выплат
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== ДИАГНОСТИКА СИСТЕМЫ ВЫПЛАТ ===\n\n";

// 1. Проверяем файлы
echo "1. ПРОВЕРКА ФАЙЛОВ:\n";
$requiredFiles = [
    'config.php',
    'functions.php', 
    'NOWPaymentsAPI.php',
    'user_data.json'
];

foreach ($requiredFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "✅ {$file} - существует (" . filesize($path) . " байт)\n";
    } else {
        echo "❌ {$file} - НЕ НАЙДЕН!\n";
    }
}

// 2. Проверяем подключение файлов
echo "\n2. ПОДКЛЮЧЕНИЕ ФАЙЛОВ:\n";
try {
    require_once __DIR__ . '/config.php';
    echo "✅ config.php подключен\n";
} catch (Exception $e) {
    echo "❌ config.php ошибка: " . $e->getMessage() . "\n";
    exit;
}

try {
    require_once __DIR__ . '/functions.php';
    echo "✅ functions.php подключен\n";
} catch (Exception $e) {
    echo "❌ functions.php ошибка: " . $e->getMessage() . "\n";
    exit;
}

try {
    require_once __DIR__ . '/NOWPaymentsAPI.php';
    echo "✅ NOWPaymentsAPI.php подключен\n";
} catch (Exception $e) {
    echo "❌ NOWPaymentsAPI.php ошибка: " . $e->getMessage() . "\n";
    exit;
}

// 3. Проверяем константы
echo "\n3. ПРОВЕРКА КОНСТАНТ:\n";
$constants = [
    'NOWPAYMENTS_API_KEY',
    'NOWPAYMENTS_PUBLIC_KEY', 
    'NOWPAYMENTS_IPN_SECRET',
    'NOWPAYMENTS_API_URL'
];

foreach ($constants as $const) {
    if (defined($const)) {
        $value = constant($const);
        echo "✅ {$const} = " . substr($value, 0, 10) . "...\n";
    } else {
        echo "❌ {$const} - НЕ ОПРЕДЕЛЕНА!\n";
    }
}

// 4. Проверяем данные пользователей
echo "\n4. ДАННЫЕ ПОЛЬЗОВАТЕЛЕЙ:\n";
try {
    $userData = loadUserData();
    if (is_array($userData)) {
        $userCount = count($userData);
        echo "✅ Загружено пользователей: {$userCount}\n";
        
        // Анализируем выплаты
        $totalWithdrawals = 0;
        $pendingWithdrawals = 0;
        $missingPayoutIds = 0;
        
        foreach ($userData as $userId => $user) {
            if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
                foreach ($user['withdrawals'] as $withdrawal) {
                    $totalWithdrawals++;
                    
                    $status = $withdrawal['status'] ?? 'unknown';
                    if ($status === 'pending') {
                        $pendingWithdrawals++;
                    }
                    
                    $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
                    if (!$payoutId) {
                        $missingPayoutIds++;
                    }
                }
            }
        }
        
        echo "📊 Всего выплат: {$totalWithdrawals}\n";
        echo "⏳ В ожидании: {$pendingWithdrawals}\n";
        echo "❌ Без payout_id: {$missingPayoutIds}\n";
        
    } else {
        echo "❌ Не удалось загрузить данные пользователей\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка загрузки данных: " . $e->getMessage() . "\n";
}

// 5. Тест API NOWPayments
echo "\n5. ТЕСТ API NOWPAYMENTS:\n";
try {
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Проверяем баланс
    $balance = $api->getAccountBalance();
    if ($balance && !isset($balance['error'])) {
        echo "✅ API подключение работает\n";
        echo "💰 Доступные валюты: " . (isset($balance['currencies']) ? count($balance['currencies']) : 'неизвестно') . "\n";
    } else {
        echo "❌ Ошибка API: " . json_encode($balance) . "\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка API: " . $e->getMessage() . "\n";
}

// 6. Исправление проблем
echo "\n6. АВТОМАТИЧЕСКОЕ ИСПРАВЛЕНИЕ:\n";

if ($missingPayoutIds > 0) {
    echo "🔧 Исправляем отсутствующие payout_id...\n";
    $fixed = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals'])) continue;
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $id = $withdrawal['id'] ?? null;
            
            if (!$payoutId && $id) {
                $withdrawal['payout_id'] = $id;
                $fixed++;
                echo "✅ Пользователь {$userId}: добавлен payout_id = {$id}\n";
            } elseif (!$payoutId && !$id) {
                $tempId = 'temp_' . $userId . '_' . $index . '_' . time();
                $withdrawal['payout_id'] = $tempId;
                $withdrawal['id'] = $tempId;
                $fixed++;
                echo "⚠️ Пользователь {$userId}: создан временный ID = {$tempId}\n";
            }
        }
    }
    
    if ($fixed > 0) {
        if (saveUserData($userData)) {
            echo "✅ Сохранено {$fixed} исправлений\n";
        } else {
            echo "❌ Не удалось сохранить исправления\n";
        }
    }
}

// 7. Обновление статусов
if ($pendingWithdrawals > 0) {
    echo "\n🔄 Обновляем статусы выплат...\n";
    $updated = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals'])) continue;
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';
            
            if (!$payoutId || $currentStatus !== 'pending') continue;
            
            try {
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = $statusResponse['status'];
                    
                    if ($newStatus !== $currentStatus) {
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        $updated++;
                        
                        echo "✅ Пользователь {$userId}: {$currentStatus} → {$newStatus}\n";
                        
                        // Возвращаем средства за неудачные выплаты
                        if (in_array($newStatus, ['failed', 'cancelled', 'expired'])) {
                            $refundAmount = $withdrawal['coins_amount'] ?? 0;
                            if ($refundAmount > 0) {
                                $user['balance'] = ($user['balance'] ?? 0) + $refundAmount;
                                echo "💰 Возвращено {$refundAmount} монет\n";
                            }
                        }
                    }
                }
                
                // Задержка между запросами
                usleep(200000); // 0.2 секунды
                
            } catch (Exception $e) {
                echo "❌ Ошибка проверки {$payoutId}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    if ($updated > 0) {
        if (saveUserData($userData)) {
            echo "✅ Обновлено {$updated} статусов\n";
        } else {
            echo "❌ Не удалось сохранить обновления\n";
        }
    }
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
?>
