<?php
/**
 * Тест проверки замены TRX на TON в админке
 */

echo "🔄 ПРОВЕРКА ЗАМЕНЫ TRX НА TON В АДМИНКЕ\n";
echo str_repeat("=", 50) . "\n\n";

// Файлы для проверки
$filesToCheck = [
    'api/admin/index.php',
    'api/admin/balance.php',
    'api/test_min_amounts.php'
];

$foundIssues = [];
$successfulChanges = [];

foreach ($filesToCheck as $file) {
    echo "📁 Проверяем файл: {$file}\n";
    
    if (!file_exists($file)) {
        echo "   ❌ Файл не найден!\n\n";
        continue;
    }
    
    $content = file_get_contents($file);
    
    // Проверяем наличие TRX (должно отсутствовать)
    $trxMatches = [];
    preg_match_all('/\b(trx|TRX)\b/i', $content, $trxMatches, PREG_OFFSET_CAPTURE);
    
    if (!empty($trxMatches[0])) {
        echo "   ⚠️ Найдены упоминания TRX:\n";
        foreach ($trxMatches[0] as $match) {
            $line = substr_count(substr($content, 0, $match[1]), "\n") + 1;
            echo "      - Строка {$line}: '{$match[0]}'\n";
            $foundIssues[] = "{$file}:{$line} - {$match[0]}";
        }
    } else {
        echo "   ✅ TRX не найден (хорошо)\n";
    }
    
    // Проверяем наличие TON (должно присутствовать)
    $tonMatches = [];
    preg_match_all('/\b(ton|TON)\b/i', $content, $tonMatches, PREG_OFFSET_CAPTURE);
    
    if (!empty($tonMatches[0])) {
        echo "   ✅ Найдены упоминания TON:\n";
        $tonCount = 0;
        foreach ($tonMatches[0] as $match) {
            $line = substr_count(substr($content, 0, $match[1]), "\n") + 1;
            echo "      - Строка {$line}: '{$match[0]}'\n";
            $tonCount++;
            if ($tonCount >= 3) {
                echo "      - ... и еще " . (count($tonMatches[0]) - 3) . " упоминаний\n";
                break;
            }
        }
        $successfulChanges[] = "{$file} - " . count($tonMatches[0]) . " упоминаний TON";
    } else {
        echo "   ⚠️ TON не найден в файле\n";
    }
    
    echo "\n";
}

// Проверяем конкретные изменения
echo "🎯 ПРОВЕРКА КОНКРЕТНЫХ ИЗМЕНЕНИЙ\n";
echo str_repeat("-", 30) . "\n\n";

// 1. Проверяем массив валют в index.php
echo "1️⃣ Проверяем массив валют в index.php:\n";
$indexContent = file_get_contents('api/admin/index.php');
if (strpos($indexContent, "'ton'") !== false && strpos($indexContent, "'trx'") === false) {
    echo "   ✅ Массив availableCurrencies обновлен корректно\n";
} else {
    echo "   ❌ Массив availableCurrencies требует проверки\n";
    $foundIssues[] = "index.php - массив availableCurrencies";
}

// 2. Проверяем иконки в index.php
if (strpos($indexContent, "'ton' => '💎'") !== false && strpos($indexContent, "'trx'") === false) {
    echo "   ✅ Массив иконок обновлен корректно\n";
} else {
    echo "   ❌ Массив иконок требует проверки\n";
    $foundIssues[] = "index.php - массив иконок";
}

// 3. Проверяем balance.php
echo "\n2️⃣ Проверяем balance.php:\n";
$balanceContent = file_get_contents('api/admin/balance.php');
if (strpos($balanceContent, "'ton'") !== false && strpos($balanceContent, "'trx'") === false) {
    echo "   ✅ Условия баланса обновлены корректно\n";
} else {
    echo "   ❌ Условия баланса требуют проверки\n";
    $foundIssues[] = "balance.php - условия баланса";
}

if (strpos($balanceContent, 'TON') !== false && strpos($balanceContent, 'TRX') === false) {
    echo "   ✅ Текстовые описания обновлены корректно\n";
} else {
    echo "   ❌ Текстовые описания требуют проверки\n";
    $foundIssues[] = "balance.php - текстовые описания";
}

// 4. Проверяем test_min_amounts.php
echo "\n3️⃣ Проверяем test_min_amounts.php:\n";
$testContent = file_get_contents('api/test_min_amounts.php');
$tonCount = substr_count($testContent, "'ton'");
if ($tonCount === 1 && strpos($testContent, "'trx'") === false) {
    echo "   ✅ Список валют обновлен корректно (1 упоминание TON)\n";
} else {
    echo "   ❌ Список валют требует проверки (найдено {$tonCount} упоминаний TON)\n";
    if (strpos($testContent, "'trx'") !== false) {
        $foundIssues[] = "test_min_amounts.php - остались упоминания TRX";
    }
}

// Итоговый отчет
echo "\n📊 ИТОГОВЫЙ ОТЧЕТ\n";
echo str_repeat("=", 30) . "\n";

if (empty($foundIssues)) {
    echo "🎉 ВСЕ ИЗМЕНЕНИЯ ПРИМЕНЕНЫ УСПЕШНО!\n\n";
    echo "✅ Успешные изменения:\n";
    foreach ($successfulChanges as $change) {
        echo "   - {$change}\n";
    }
    echo "\n🎯 Результат:\n";
    echo "   - TRX полностью заменен на TON в админке\n";
    echo "   - Все массивы валют обновлены\n";
    echo "   - Иконки и описания изменены\n";
    echo "   - Условия баланса актуализированы\n";
} else {
    echo "⚠️ НАЙДЕНЫ ПРОБЛЕМЫ:\n\n";
    foreach ($foundIssues as $issue) {
        echo "   ❌ {$issue}\n";
    }
    echo "\n✅ Успешные изменения:\n";
    foreach ($successfulChanges as $change) {
        echo "   - {$change}\n";
    }
}

echo "\n🔍 РЕКОМЕНДАЦИИ:\n";
echo "1. Проверьте админку в браузере\n";
echo "2. Убедитесь что TON отображается вместо TRX\n";
echo "3. Проверьте балансы валют\n";
echo "4. Протестируйте функциональность\n";

echo "\n✨ Замена TRX на TON в админке завершена!\n";
?>
