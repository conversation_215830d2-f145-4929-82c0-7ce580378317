<?php
/**
 * api/test-getUserData.php
 * Минимальная версия getUserData для тестирования
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

$result = [
    'test' => 'getUserData_minimal',
    'timestamp' => time(),
    'steps' => [],
    'errors' => []
];

try {
    $result['steps'][] = 'Начало тестирования';
    
    // Шаг 1: Проверяем метод
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $result['errors'][] = 'Метод должен быть POST, получен: ' . $_SERVER['REQUEST_METHOD'];
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'Метод POST подтвержден';
    
    // Шаг 2: Читаем входные данные
    $inputJSON = file_get_contents('php://input');
    if ($inputJSON === false) {
        $result['errors'][] = 'Не удалось прочитать php://input';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'Входные данные прочитаны: ' . strlen($inputJSON) . ' байт';
    
    // Шаг 3: Парсим JSON
    $input = json_decode($inputJSON, true);
    if ($input === null) {
        $result['errors'][] = 'Ошибка парсинга JSON: ' . json_last_error_msg();
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'JSON распарсен успешно';
    
    // Шаг 4: Проверяем initData
    if (!isset($input['initData']) || empty($input['initData'])) {
        $result['errors'][] = 'Отсутствует initData в запросе';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'initData найден: ' . strlen($input['initData']) . ' символов';
    
    // Шаг 5: Подключаем файлы
    try {
        require_once __DIR__ . '/config.php';
        $result['steps'][] = 'config.php подключен';
    } catch (Exception $e) {
        $result['errors'][] = 'Ошибка подключения config.php: ' . $e->getMessage();
        echo json_encode($result);
        exit;
    }
    
    try {
        require_once __DIR__ . '/validate_initdata.php';
        $result['steps'][] = 'validate_initdata.php подключен';
    } catch (Exception $e) {
        $result['errors'][] = 'Ошибка подключения validate_initdata.php: ' . $e->getMessage();
        echo json_encode($result);
        exit;
    }
    
    try {
        require_once __DIR__ . '/db_mock.php';
        $result['steps'][] = 'db_mock.php подключен';
    } catch (Exception $e) {
        $result['errors'][] = 'Ошибка подключения db_mock.php: ' . $e->getMessage();
        echo json_encode($result);
        exit;
    }
    
    // Шаг 6: Проверяем функции
    if (!function_exists('validateTelegramInitData')) {
        $result['errors'][] = 'Функция validateTelegramInitData не найдена';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'validateTelegramInitData найдена';
    
    if (!function_exists('loadUserData')) {
        $result['errors'][] = 'Функция loadUserData не найдена';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'loadUserData найдена';
    
    if (!function_exists('getUserDetails')) {
        $result['errors'][] = 'Функция getUserDetails не найдена';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'getUserDetails найдена';
    
    // Шаг 7: Тестируем валидацию
    $initData = $input['initData'];
    $validatedData = validateTelegramInitData($initData);
    
    if ($validatedData === false) {
        $result['steps'][] = 'Стандартная валидация не прошла, пробуем упрощенную';
        
        // Упрощенная валидация
        $initDataParts = [];
        parse_str($initData, $initDataParts);
        
        if (isset($initDataParts['user'])) {
            $userArray = json_decode($initDataParts['user'], true);
            if ($userArray !== null && isset($userArray['id'])) {
                $validatedData = ['user' => $userArray];
                $result['steps'][] = 'Упрощенная валидация прошла для пользователя: ' . $userArray['id'];
            } else {
                $result['errors'][] = 'Не удалось извлечь данные пользователя из initData';
                echo json_encode($result);
                exit;
            }
        } else {
            $result['errors'][] = 'Отсутствуют данные пользователя в initData';
            echo json_encode($result);
            exit;
        }
    } else {
        $result['steps'][] = 'Стандартная валидация прошла успешно';
    }
    
    // Шаг 8: Получаем ID пользователя
    $userId = intval($validatedData['user']['id']);
    $result['steps'][] = 'ID пользователя: ' . $userId;
    
    // Шаг 9: Загружаем данные
    $userData = loadUserData();
    if (!is_array($userData)) {
        $result['errors'][] = 'loadUserData вернул не массив: ' . gettype($userData);
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'Данные пользователей загружены: ' . count($userData) . ' пользователей';
    
    // Шаг 10: Получаем детали пользователя
    $userDetails = getUserDetails($userId, $userData, null, $validatedData['user']);
    $result['steps'][] = 'getUserDetails выполнен успешно';
    
    // Шаг 11: Сохраняем данные
    if (!saveUserData($userData)) {
        $result['errors'][] = 'Не удалось сохранить данные';
        echo json_encode($result);
        exit;
    }
    $result['steps'][] = 'Данные сохранены успешно';
    
    // Успешный результат
    $response = [
        'success' => true,
        'userId' => $userId,
        'balance' => $userDetails['balance'],
        'min_withdrawal' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0,
        'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100,
        'withdrawals_count' => $userDetails['withdrawals_count'] ?? 0,
        'test_info' => $result
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    $result['errors'][] = 'Исключение: ' . $e->getMessage() . ' в файле ' . $e->getFile() . ' на строке ' . $e->getLine();
    echo json_encode($result);
} catch (Error $e) {
    $result['errors'][] = 'Фатальная ошибка: ' . $e->getMessage() . ' в файле ' . $e->getFile() . ' на строке ' . $e->getLine();
    echo json_encode($result);
}
?>
