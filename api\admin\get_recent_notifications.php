<?php
/**
 * api/admin/get_recent_notifications.php
 * API для получения последних отправленных уведомлений
 */

header('Content-Type: application/json');

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Не авторизован']);
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

try {
    // Получаем логи уведомлений из JSON файла
    $logsFile = __DIR__ . '/../cron/notification_logs.json';
    $notifications = [];

    if (file_exists($logsFile)) {
        $content = file_get_contents($logsFile);
        $logs = json_decode($content, true);

        if ($logs && is_array($logs)) {
            // Сортируем по времени отправки (новые сначала)
            usort($logs, function($a, $b) {
                return ($b['sent_at'] ?? 0) - ($a['sent_at'] ?? 0);
            });

            // Берем последние 20
            $logs = array_slice($logs, 0, 20);

            // Форматируем данные для отображения
            foreach ($logs as $log) {
                $notifications[] = [
                    'id' => $log['id'] ?? uniqid(),
                    'user_id' => $log['user_id'] ?? '',
                    'telegram_id' => $log['telegram_id'] ?? '',
                    'username' => $log['username'] ?? '',
                    'first_name' => $log['first_name'] ?? '',
                    'last_name' => $log['last_name'] ?? '',
                    'message_text' => $log['message_text'] ?? '',
                    'sent_at' => isset($log['sent_at']) ? date('d.m.Y H:i', $log['sent_at']) : '',
                    'status' => $log['status'] ?? 'unknown',
                    'error_message' => $log['error_message'] ?? '',
                    'last_activity' => isset($log['last_activity']) && $log['last_activity'] ? date('d.m.Y H:i', $log['last_activity']) : null
                ];
            }
        }
    }

    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'total' => count($notifications)
    ]);

} catch (Exception $e) {
    error_log("Error fetching recent notifications: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Ошибка получения данных: ' . $e->getMessage()]);
}
?>
