/**
 * js/richads-security.js
 * СИСТЕМА БЕЗОПАСНОСТИ для предотвращения накрутки монет
 * 
 * Генерирует токены подтверждения для RichAds и обеспечивает
 * начисление монет ТОЛЬКО после реального success от RichAds
 */

class RichAdsSecurityManager {
  constructor() {
    this.pendingRewards = new Map(); // Ожидающие подтверждения награды
    this.securityKey = this.generateSecurityKey();
    
    console.log('[RichAdsSecurity] 🔒 Система безопасности инициализирована');
  }
  
  /**
   * Генерирует уникальный ключ безопасности для сессии
   */
  generateSecurityKey() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const userAgent = navigator.userAgent.slice(-20);
    return btoa(`${timestamp}_${random}_${userAgent}`).replace(/[^a-zA-Z0-9]/g, '');
  }
  
  /**
   * Создает токен подтверждения для RichAds
   * Этот токен будет отправлен на сервер только после success от RichAds
   */
  createRewardToken(userId, adType) {
    const timestamp = Math.floor(Date.now() / 1000); // Unix timestamp
    const nonce = Math.random().toString(36).substring(2, 15);
    
    // Создаем токен с компонентами, которые сервер может проверить
    const tokenData = {
      userId: userId,
      adType: adType,
      timestamp: timestamp,
      nonce: nonce,
      sessionKey: this.securityKey.substring(0, 16)
    };
    
    // Простое кодирование (в продакшене можно использовать более сложную криптографию)
    const tokenString = `${userId}_${adType}_${timestamp}_${nonce}_${tokenData.sessionKey}`;
    const token = btoa(tokenString).replace(/[^a-zA-Z0-9]/g, '');
    
    console.log(`[RichAdsSecurity] 🎫 Создан токен для ${adType}: ${token.substring(0, 20)}...`);
    
    return token;
  }
  
  /**
   * Регистрирует ожидающую награду
   * Вызывается ПЕРЕД показом рекламы
   */
  registerPendingReward(userId, adType) {
    const token = this.createRewardToken(userId, adType);
    const rewardId = `${userId}_${adType}_${Date.now()}`;
    
    this.pendingRewards.set(rewardId, {
      userId: userId,
      adType: adType,
      token: token,
      timestamp: Date.now(),
      status: 'pending'
    });
    
    console.log(`[RichAdsSecurity] 📝 Зарегистрирована ожидающая награда: ${rewardId}`);
    
    // Автоматически удаляем через 5 минут если не подтверждена
    setTimeout(() => {
      if (this.pendingRewards.has(rewardId)) {
        console.log(`[RichAdsSecurity] ⏰ Удаляем неподтвержденную награду: ${rewardId}`);
        this.pendingRewards.delete(rewardId);
      }
    }, 5 * 60 * 1000);
    
    return { rewardId, token };
  }
  
  /**
   * Подтверждает награду после success от RichAds
   * Вызывается ТОЛЬКО после получения реального success от RichAds SDK
   */
  async confirmReward(userId, adType, richAdsResult) {
    console.log(`[RichAdsSecurity] ✅ Подтверждение награды для ${adType}:`, richAdsResult);
    
    // Ищем соответствующую ожидающую награду
    let matchingReward = null;
    let matchingRewardId = null;
    
    for (const [rewardId, reward] of this.pendingRewards.entries()) {
      if (reward.userId === userId && reward.adType === adType && reward.status === 'pending') {
        matchingReward = reward;
        matchingRewardId = rewardId;
        break;
      }
    }
    
    if (!matchingReward) {
      console.error(`[RichAdsSecurity] ❌ Не найдена ожидающая награда для ${userId}/${adType}`);
      throw new Error('Награда не зарегистрирована или уже обработана');
    }
    
    // Проверяем, что награда не слишком старая (максимум 5 минут)
    const age = Date.now() - matchingReward.timestamp;
    if (age > 5 * 60 * 1000) {
      console.error(`[RichAdsSecurity] ⏰ Награда устарела: ${age}ms`);
      this.pendingRewards.delete(matchingRewardId);
      throw new Error('Награда устарела');
    }
    
    // Отмечаем награду как обрабатываемую
    matchingReward.status = 'processing';
    
    try {
      // Отправляем защищенный запрос на сервер с токеном
      const response = await this.sendSecureRewardRequest(matchingReward);
      
      if (response.success) {
        // Награда успешно начислена
        matchingReward.status = 'confirmed';
        this.pendingRewards.delete(matchingRewardId);
        
        console.log(`[RichAdsSecurity] 🎉 Награда подтверждена и начислена: ${response.reward} монет`);
        
        return response;
      } else {
        throw new Error(response.error || 'Ошибка начисления награды');
      }
      
    } catch (error) {
      // Возвращаем статус обратно в pending при ошибке
      matchingReward.status = 'pending';
      console.error(`[RichAdsSecurity] ❌ Ошибка подтверждения награды:`, error);
      throw error;
    }
  }
  
  /**
   * Отправляет защищенный запрос на начисление награды
   */
  async sendSecureRewardRequest(rewardData) {
    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      // Fallback для локальной разработки
      const testUser = JSON.stringify({id: rewardData.userId, first_name: "Test", username: "testuser"});
      const fallbackInitData = `user=${encodeURIComponent(testUser)}&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test`;
      console.warn('[RichAdsSecurity] Используем fallback initData для разработки');
    }
    
    const requestData = {
      initData: initData || fallbackInitData,
      adType: rewardData.adType,
      richAdsToken: rewardData.token,
      timestamp: Date.now(),
      sessionId: window.sessionStorage?.getItem('session_id') || 'unknown'
    };
    
    console.log(`[RichAdsSecurity] 📤 Отправляем защищенный запрос:`, {
      adType: requestData.adType,
      token: requestData.richAdsToken.substring(0, 20) + '...'
    });
    
    const response = await fetch(`${window.API_BASE_URL || 'api'}/secure_reward.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(requestData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Получает статистику безопасности
   */
  getSecurityStats() {
    const stats = {
      pendingRewards: this.pendingRewards.size,
      securityKey: this.securityKey.substring(0, 10) + '...',
      rewards: []
    };
    
    for (const [rewardId, reward] of this.pendingRewards.entries()) {
      stats.rewards.push({
        id: rewardId,
        adType: reward.adType,
        status: reward.status,
        age: Date.now() - reward.timestamp
      });
    }
    
    return stats;
  }
  
  /**
   * Очищает устаревшие награды
   */
  cleanupExpiredRewards() {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [rewardId, reward] of this.pendingRewards.entries()) {
      if (now - reward.timestamp > 5 * 60 * 1000) { // 5 минут
        this.pendingRewards.delete(rewardId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      console.log(`[RichAdsSecurity] 🧹 Очищено ${cleaned} устаревших наград`);
    }
    
    return cleaned;
  }
}

// Создаем глобальный экземпляр
window.richAdsSecurityManager = new RichAdsSecurityManager();

// Автоматическая очистка каждые 2 минуты
setInterval(() => {
  window.richAdsSecurityManager.cleanupExpiredRewards();
}, 2 * 60 * 1000);

console.log('[RichAdsSecurity] 🔒 Модуль безопасности загружен');
