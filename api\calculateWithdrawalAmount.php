<?php
/**
 * api/calculateWithdrawalAmount.php
 * Рассчитывает точную сумму в криптовалюте для вывода (синхронизировано с requestWithdrawal.php)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/FeeCalculator.php';

try {
    // Получение входных данных
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if (!isset($input['coins_amount']) || !isset($input['currency'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Отсутствуют обязательные параметры: coins_amount, currency']);
        exit;
    }

    $coinsAmount = intval($input['coins_amount']);
    $currency = strtolower(trim($input['currency']));

    if ($coinsAmount <= 0) {
        echo json_encode([
            'success' => true,
            'crypto_amount' => 0,
            'usd_amount' => 0,
            'usd_after_fee' => 0,
            'fee_applied' => false,
            'message' => 'Сумма должна быть больше 0'
        ]);
        exit;
    }

    // НОВАЯ ЛОГИКА: Используем FeeCalculator для точного расчета
    error_log("calculateWithdrawalAmount INFO: Расчет для {$coinsAmount} монет -> {$currency} (новый алгоритм)");

    $calculator = FeeCalculator::getInstance();
    $result = $calculator->calculateWithdrawalAmount($coinsAmount, $currency);

    // Проверяем результат расчета
    if (!$result['success']) {
        error_log("calculateWithdrawalAmount ERROR: " . $result['error']);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'error_code' => $result['error_code'] ?? 'CALCULATION_ERROR',
            'crypto_amount' => 0,
            'usd_amount' => $coinsAmount * CONVERSION_RATE,
            'details' => $result
        ]);
        exit;
    }

    // Возвращаем результат в формате, совместимом с калькулятором и авторасчетом
    echo json_encode([
        'success' => true,
        'crypto_amount' => $result['crypto_amount'], // ТО ЧТО ПОЛУЧИТ ПОЛЬЗОВАТЕЛЬ (одинаково для калькулятора и авторасчета)
        'crypto_amount_gross' => $result['crypto_amount_for_nowpayments'] ?? $result['crypto_amount_gross'], // ТО ЧТО ОТПРАВЛЯЕМ В NOWPAYMENTS
        'usd_amount' => $result['usd_amount'],
        'nowpayments_fee' => $result['nowpayments_fee'],
        'currency' => $result['currency'],
        'coins_amount' => $result['coins_amount'],
        'conversion_rate' => $result['conversion_rate'],
        'calculated_at' => $result['calculated_at'],
        'calculation_method' => $result['calculation_method'],
        'fee_details' => $result['fee_details'] ?? [],
        'fee_handling' => $result['fee_handling'] ?? [],
        // Для совместимости со старым интерфейсом
        'usd_after_fee' => $result['usd_amount'],
        'network_fee' => $result['nowpayments_fee'],
        'fee_applied' => true,
        'data_source' => 'fee_calculator_v2',
        'raw_amount' => $result['crypto_amount'] // ВАЖНО: Используем crypto_amount для совместимости с авторасчетом
    ]);

} catch (Exception $e) {
    error_log("calculateWithdrawalAmount ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при расчете суммы',
        'details' => $e->getMessage()
    ]);
}


?>
