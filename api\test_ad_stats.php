<?php
/**
 * api/test_ad_stats.php
 * Тестовый скрипт для создания тестовых данных статистики рекламы
 * ТОЛЬКО ДЛЯ РАЗРАБОТКИ!
 */

// Проверяем, что мы в режиме разработки
if (!defined('DEVELOPMENT_MODE')) {
    define('DEVELOPMENT_MODE', true); // Установите false в продакшене
}

if (!DEVELOPMENT_MODE) {
    http_response_code(403);
    echo "Доступ запрещен в продакшене";
    exit;
}

require_once __DIR__ . '/recordAdView.php';

/**
 * Создание тестовых данных для статистики рекламы
 */
function createTestAdData() {
    $logFile = __DIR__ . '/ad_requests.log';
    
    // Очищаем существующий лог
    file_put_contents($logFile, '');
    
    $adTypes = ['native_banner', 'interstitial_banner_view', 'rewarded_video'];
    $statuses = ['request', 'success', 'empty', 'error', 'limit_exceeded'];
    $countries = ['RU', 'US', 'Unknown'];
    $ips = ['**************', '*************', '***********', '********'];
    $userIds = [5880288830, 405669007, 123456789, 987654321, 555666777];
    
    $now = time();
    $dayInSeconds = 24 * 60 * 60;
    
    // Создаем данные за последние 7 дней
    for ($day = 6; $day >= 0; $day--) {
        $dayStart = $now - ($day * $dayInSeconds);
        
        // Создаем от 50 до 200 записей в день
        $recordsPerDay = rand(50, 200);
        
        for ($i = 0; $i < $recordsPerDay; $i++) {
            // Случайное время в течение дня
            $timestamp = $dayStart + rand(0, $dayInSeconds - 1);
            $date = date('Y-m-d H:i:s', $timestamp);
            
            // Случайные данные
            $userId = $userIds[array_rand($userIds)];
            $adType = $adTypes[array_rand($adTypes)];
            $ip = $ips[array_rand($ips)];
            $country = $countries[array_rand($countries)];
            
            // Определяем статус с весами (больше успешных)
            $statusWeights = [
                'request' => 100,
                'success' => 70,
                'empty' => 15,
                'error' => 10,
                'limit_exceeded' => 5
            ];
            
            $status = weightedRandom($statusWeights);
            
            // Создаем запись
            $logEntry = [
                'timestamp' => $timestamp,
                'date' => $date,
                'user_id' => $userId,
                'ad_type' => $adType,
                'status' => $status,
                'ip' => $ip,
                'user_agent' => 'Mozilla/5.0 (Test Browser)',
                'country' => $country
            ];
            
            $logLine = json_encode($logEntry) . "\n";
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        }
    }
    
    return "Создано тестовых записей: " . (7 * $recordsPerDay);
}

/**
 * Выбор случайного элемента с весами
 */
function weightedRandom($weights) {
    $totalWeight = array_sum($weights);
    $random = rand(1, $totalWeight);
    
    $currentWeight = 0;
    foreach ($weights as $item => $weight) {
        $currentWeight += $weight;
        if ($random <= $currentWeight) {
            return $item;
        }
    }
    
    return array_key_first($weights);
}

// Если скрипт вызван напрямую
if (basename($_SERVER['PHP_SELF']) === 'test_ad_stats.php') {
    header('Content-Type: text/plain; charset=utf-8');
    
    echo "=== Создание тестовых данных для статистики рекламы ===\n\n";
    
    try {
        $result = createTestAdData();
        echo "✅ Успешно: " . $result . "\n";
        echo "📁 Файл лога: " . __DIR__ . '/ad_requests.log' . "\n";
        echo "🔗 Проверить статистику: /api/admin/ad_statistics.php\n";
        
    } catch (Exception $e) {
        echo "❌ Ошибка: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== Готово ===\n";
}
?>
