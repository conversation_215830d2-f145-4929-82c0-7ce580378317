<?php
/**
 * Тестовый скрипт для проверки прав доступа
 */

header('Content-Type: application/json');

$results = [];

// Проверяем основную директорию
$mainDir = __DIR__ . '/../../';
$mainFile = $mainDir . 'design_settings.json';

$results['main_dir'] = [
    'path' => $mainDir,
    'exists' => is_dir($mainDir),
    'readable' => is_readable($mainDir),
    'writable' => is_writable($mainDir),
    'permissions' => substr(sprintf('%o', fileperms($mainDir)), -4)
];

$results['main_file'] = [
    'path' => $mainFile,
    'exists' => file_exists($mainFile),
    'readable' => file_exists($mainFile) ? is_readable($mainFile) : false,
    'writable' => file_exists($mainFile) ? is_writable($mainFile) : false,
    'permissions' => file_exists($mainFile) ? substr(sprintf('%o', fileperms($mainFile)), -4) : 'N/A'
];

// Проверяем альтернативную директорию
$altDir = __DIR__ . '/';
$altFile = $altDir . 'design_settings.json';

$results['alt_dir'] = [
    'path' => $altDir,
    'exists' => is_dir($altDir),
    'readable' => is_readable($altDir),
    'writable' => is_writable($altDir),
    'permissions' => substr(sprintf('%o', fileperms($altDir)), -4)
];

$results['alt_file'] = [
    'path' => $altFile,
    'exists' => file_exists($altFile),
    'readable' => file_exists($altFile) ? is_readable($altFile) : false,
    'writable' => file_exists($altFile) ? is_writable($altFile) : false,
    'permissions' => file_exists($altFile) ? substr(sprintf('%o', fileperms($altFile)), -4) : 'N/A'
];

// Тестируем запись
$testData = ['test' => 'data', 'timestamp' => time()];
$testJson = json_encode($testData);

$results['write_test'] = [];

// Тест записи в основной файл
try {
    $writeResult = file_put_contents($mainFile, $testJson);
    $results['write_test']['main'] = [
        'success' => $writeResult !== false,
        'bytes' => $writeResult,
        'error' => $writeResult === false ? error_get_last() : null
    ];
} catch (Exception $e) {
    $results['write_test']['main'] = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

// Тест записи в альтернативный файл
try {
    $writeResult = file_put_contents($altFile, $testJson);
    $results['write_test']['alt'] = [
        'success' => $writeResult !== false,
        'bytes' => $writeResult,
        'error' => $writeResult === false ? error_get_last() : null
    ];
} catch (Exception $e) {
    $results['write_test']['alt'] = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

// Информация о сервере
$results['server_info'] = [
    'php_version' => PHP_VERSION,
    'user' => get_current_user(),
    'uid' => function_exists('posix_getuid') ? posix_getuid() : 'N/A',
    'gid' => function_exists('posix_getgid') ? posix_getgid() : 'N/A'
];

echo json_encode($results, JSON_PRETTY_PRINT);
?>
