-- Таблица для настроек уведомлений пользователей
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица для логов отправки уведомлений
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    telegram_id BIGINT NOT NULL,
    username VA<PERSON>HA<PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    message_text TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('sent', 'failed', 'blocked') DEFAULT 'sent',
    error_message TEXT,
    last_activity TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_telegram_id (telegram_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status)
);

-- Вставляем настройки по умолчанию
INSERT INTO notification_settings (setting_name, setting_value, is_enabled) VALUES 
('notifications_enabled', '1', TRUE),
('message_template', 'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!', TRUE),
('bot_username', 'uniqpaid_paid_bot', TRUE),
('inactive_hours', '24', TRUE)
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;
