# Структура файла advanced_fingerprints.json

## Описание
Файл содержит отпечатки устройств пользователей в формате JSON для антифрод системы.

## Структура данных

```json
{
  "fingerprint_hash": {
    "fingerprint": "unique_device_fingerprint_hash",
    "user_id": "telegram_user_id",
    "ip_address": "user_ip_address",
    "timestamp": "2025-01-01 12:00:00",
    "created_at": 1704110400,
    "risk_score": 0,
    "components": {
      "screen": {
        "width": 1920,
        "height": 1080,
        "devicePixelRatio": 1
      },
      "system": {
        "platform": "Win32",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "language": "ru-RU"
      },
      "hardware": {
        "hardwareConcurrency": 8,
        "maxTouchPoints": 0
      },
      "performance": {
        "memory": 8192,
        "connection": "4g"
      },
      "behavioral": {
        "timezone": "Europe/Moscow",
        "cookiesEnabled": true
      }
    }
  }
}
```

## Поля данных

### Основные поля
- **fingerprint** - Уникальный хеш отпечатка устройства
- **user_id** - ID пользователя Telegram
- **ip_address** - IP адрес пользователя
- **timestamp** - Дата и время создания (строка)
- **created_at** - Unix timestamp создания
- **risk_score** - Оценка риска (0-100)

### Компоненты отпечатка (components)

#### screen
- **width** - Ширина экрана в пикселях
- **height** - Высота экрана в пикселях
- **devicePixelRatio** - Плотность пикселей

#### system
- **platform** - Платформа (Win32, MacIntel, Linux, etc.)
- **userAgent** - User Agent браузера
- **language** - Язык браузера

#### hardware
- **hardwareConcurrency** - Количество ядер процессора
- **maxTouchPoints** - Максимальное количество точек касания

#### performance
- **memory** - Объем памяти устройства (MB)
- **connection** - Тип соединения

#### behavioral
- **timezone** - Часовой пояс
- **cookiesEnabled** - Включены ли cookies

## Определение типа устройства

### Desktop
- platform: Win32, MacIntel, Linux x86_64
- maxTouchPoints: 0
- userAgent: не содержит Mobile, Android, iPhone, iPad

### Mobile
- userAgent: содержит Mobile, Android, iPhone (но не iPad)
- maxTouchPoints: > 0
- screen.width: < 768

### Tablet
- userAgent: содержит iPad или Android с большим экраном
- maxTouchPoints: > 0
- screen.width: >= 768

## Уровни риска

- **0-19**: Минимальный риск (зеленый)
- **20-39**: Низкий риск (синий)
- **40-59**: Средний риск (желтый)
- **60-79**: Высокий риск (красный)
- **80-100**: Критический риск (темный)

## Примечания

- Файл автоматически ограничивается до 5000 последних записей
- Ключи объекта - это хеши отпечатков устройств
- Все временные метки в UTC
- IP адреса могут быть замаскированы для конфиденциальности
