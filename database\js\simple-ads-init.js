/**
 * === simple-ads-init.js ===
 * Простая инициализация рекламы как в оригинале
 * Без сложных модулей - прямая инициализация
 */



// Глобальные переменные как в оригинале
let isAdShowing = false;
let lastAdShownTime = 0;
const adCooldownTime = 20000; // 20 секунд

// Настройки наград - загружаются с сервера
let adRewards = {
    native_banner: 10, // fallback значения
    rewarded_video: 1,
    interstitial: 10
};

// Загружаем актуальные награды с сервера
async function loadAdRewards() {
    try {
        const response = await fetch('api/get_ad_rewards.php');
        const data = await response.json();
        if (data.success) {
            adRewards = data.rewards;
            console.log('[SimpleAds] Награды загружены с сервера:', adRewards);
        } else {
            console.warn('[SimpleAds] Ошибка загрузки наград, используем fallback значения');
        }
    } catch (error) {
        console.warn('[SimpleAds] Ошибка загрузки наград:', error);
    }
}

// Контроллер рекламы
let adsController = null;

/**
 * Простая функция записи просмотра рекламы (как в оригинале)
 */
async function recordAdView(adType = 'default') {
    const tg = window.Telegram?.WebApp || {};
    
    // Используем тестовые данные если нет реальных
    let initData = tg.initData;
    if (!initData) {
        initData = 'test_init_data_for_user_12345';
    }
    
    // Показываем статус
    const statusEl = document.getElementById('status-message');
    if (statusEl) statusEl.textContent = 'Запись просмотра...';

    try {
        const reward = adRewards[adType] || 1;

        const response = await fetch('api/recordAdView.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                initData: initData,
                adType: adType,
                reward: reward
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }

        // Обновляем баланс
        const balanceElements = [
            document.getElementById('balance-amount'),
            document.getElementById('earn-balance-amount')
        ];
        
        balanceElements.forEach(el => {
            if (el) el.textContent = data.newBalance;
        });

        // 🎵 Воспроизводим звук монеток!
        if (window.playCoinsSound) {
            window.playCoinsSound(reward);
        }

        // Показываем успех
        if (statusEl) {
            statusEl.textContent = `Награда зачислена! Баланс: ${data.newBalance}`;
            statusEl.style.color = '#0f0';
        }

        // Вибрация
        if (tg.HapticFeedback) {
            tg.HapticFeedback.notificationOccurred('success');
        }

        // Очищаем статус через 3 секунды
        setTimeout(() => {
            if (statusEl && statusEl.textContent.includes('Награда зачислена')) {
                statusEl.textContent = '';
            }
        }, 3000);

        return true;

    } catch (error) {
        
        if (statusEl) {
            statusEl.textContent = `Ошибка: ${error.message}`;
            statusEl.style.color = '#f00';
        }

        if (tg.HapticFeedback) {
            tg.HapticFeedback.notificationOccurred('error');
        }

        return false;
    }
}

// Переменная для отслеживания состояния кнопок
let isButtonPressed = false;
let countdownTimer = null;

/**
 * Функция обратного отсчета для кнопки (точно как в оригинале)
 */
function startCountdown(button, seconds = 20) {
    if (!button || isButtonPressed) return;

    isButtonPressed = true;

    // Добавляем класс нажатой кнопки
    button.classList.add('pressed');

    // Блокируем конкретную кнопку с таймером
    button.disabled = true;
    button.style.pointerEvents = 'none'; // Дополнительная защита от кликов

    // Блокируем все кнопки рекламы
    disableAllAdButtons();

    // Создаем элемент счетчика
    const countdownOverlay = document.createElement('div');
    countdownOverlay.className = 'countdown-overlay';

    // Создаем элемент для цифр с улучшенной контрастностью
    const countdownTime = document.createElement('span');
    countdownTime.className = 'countdown-time';
    countdownTime.textContent = seconds;
    countdownOverlay.appendChild(countdownTime);

    button.appendChild(countdownOverlay);

    let remainingTime = seconds;

    countdownTimer = setInterval(() => {
        remainingTime--;
        countdownTime.textContent = remainingTime;

        if (remainingTime <= 0) {
            clearInterval(countdownTimer);
            countdownTimer = null;

            // Убираем счетчик и возвращаем кнопку в исходное состояние
            button.removeChild(countdownOverlay);
            button.classList.remove('pressed');

            // Разблокируем конкретную кнопку с таймером
            button.disabled = false;
            button.style.pointerEvents = 'auto'; // Восстанавливаем возможность кликов

            // Разблокируем все кнопки рекламы
            enableAllAdButtons();

            isButtonPressed = false;
        }
    }, 1000);
}

/**
 * Блокирует все кнопки рекламы
 */
function disableAllAdButtons() {
    const watchAdButton = document.getElementById('openLinkButton');
    const watchVideoButton = document.getElementById('watchVideoButton');
    const openLinkButton = document.getElementById('openAdButton');

    if (watchAdButton) {
        watchAdButton.disabled = true;
        watchAdButton.style.pointerEvents = 'none';
    }
    if (watchVideoButton) {
        watchVideoButton.disabled = true;
        watchVideoButton.style.pointerEvents = 'none';
    }
    if (openLinkButton) {
        openLinkButton.disabled = true;
        openLinkButton.style.pointerEvents = 'none';
    }
}

/**
 * Разблокирует все кнопки рекламы
 */
function enableAllAdButtons() {
    const watchAdButton = document.getElementById('openLinkButton');
    const watchVideoButton = document.getElementById('watchVideoButton');
    const openLinkButton = document.getElementById('openAdButton');

    if (watchAdButton) {
        watchAdButton.disabled = false;
        watchAdButton.style.pointerEvents = 'auto';
    }
    if (watchVideoButton) {
        watchVideoButton.disabled = false;
        watchVideoButton.style.pointerEvents = 'auto';
    }
    if (openLinkButton) {
        openLinkButton.disabled = false;
        openLinkButton.style.pointerEvents = 'auto';
    }
}

/**
 * Проверка cooldown
 */
function checkAdCooldown() {
    const now = Date.now();
    const timeSinceLastAd = now - lastAdShownTime;

    if (timeSinceLastAd < adCooldownTime) {
        return false;
    }

    return true;
}

/**
 * Обработчик баннерной рекламы (как в оригинале)
 */
async function handleWatchAdClick() {
    
    if (isAdShowing || !checkAdCooldown()) {
        return;
    }

    isAdShowing = true;
    lastAdShownTime = Date.now();

    try {
        if (adsController && adsController.showBanner) {
            // Используем RichAds SDK
            const result = await adsController.showBanner();
            if (result.success) {
                const success = await recordAdView('native_banner');
                if (success) {
                    startCountdown(document.getElementById('openLinkButton'), 20);
                }
            }
        } else {
            // Fallback - имитация
            setTimeout(async () => {
                const success = await recordAdView('native_banner');
                if (success) {
                    startCountdown(document.getElementById('openLinkButton'), 20);
                }
            }, 1000);
        }
    } catch (error) {
        // Тихо обрабатываем ошибки
    } finally {
        isAdShowing = false;
    }
}

/**
 * Обработчик видеорекламы (как в оригинале)
 */
async function handleWatchVideoClick() {
    
    if (isAdShowing || !checkAdCooldown()) {
        return;
    }

    isAdShowing = true;
    lastAdShownTime = Date.now();

    try {
        if (adsController && adsController.showVideo) {
            // Используем RichAds SDK
            const result = await adsController.showVideo();
            if (result.success) {
                const success = await recordAdView('rewarded_video');
                if (success) {
                    startCountdown(document.getElementById('watchVideoButton'), 20);
                }
            }
        } else {
            // Fallback - имитация
            setTimeout(async () => {
                const success = await recordAdView('rewarded_video');
                if (success) {
                    startCountdown(document.getElementById('watchVideoButton'), 20);
                }
            }, 2000);
        }
    } catch (error) {
        // Тихо обрабатываем ошибки
    } finally {
        isAdShowing = false;
    }
}

/**
 * Обработчик интерстициальной рекламы (как в оригинале)
 */
async function handleOpenLinkClick() {
    
    if (isAdShowing || !checkAdCooldown()) {
        return;
    }

    isAdShowing = true;
    lastAdShownTime = Date.now();

    try {
        if (adsController && adsController.showInterstitial) {
            // Используем RichAds SDK
            const result = await adsController.showInterstitial();
            if (result.success) {
                const success = await recordAdView('interstitial');
                if (success) {
                    startCountdown(document.getElementById('openAdButton'), 20);
                }
            }
        } else {
            // Fallback - имитация
            setTimeout(async () => {
                const success = await recordAdView('interstitial');
                if (success) {
                    startCountdown(document.getElementById('openAdButton'), 20);
                }
            }, 1500);
        }
    } catch (error) {
        // Тихо обрабатываем ошибки
    } finally {
        isAdShowing = false;
    }
}

/**
 * Инициализация рекламной системы (как в оригинале)
 */
async function initSimpleAds() {
    try {
        // Пытаемся инициализировать RichAds SDK
        if (window.TelegramGameProxy && window.TelegramGameProxy.initAds) {
            const MY_PUB_ID = window.MY_PUB_ID || '944840';
            const MY_APP_ID = window.MY_APP_ID || '2122';

            adsController = await window.TelegramGameProxy.initAds({
                pubId: MY_PUB_ID,
                appId: MY_APP_ID
            });
        }

        // Находим кнопки
        const watchAdButton = document.getElementById('openLinkButton');
        const watchVideoButton = document.getElementById('watchVideoButton');
        const openLinkButton = document.getElementById('openAdButton');

        // Добавляем обработчики как в оригинале
        if (watchAdButton) {
            watchAdButton.addEventListener('click', handleWatchAdClick);
        }

        if (watchVideoButton) {
            watchVideoButton.addEventListener('click', handleWatchVideoClick);
        }

        if (openLinkButton) {
            openLinkButton.addEventListener('click', handleOpenLinkClick);
        }

    } catch (error) {
        // Тихо обрабатываем ошибки
    }
}

// Экспортируем функции глобально
window.recordAdView = recordAdView;
window.handleWatchAdClick = handleWatchAdClick;
window.handleWatchVideoClick = handleWatchVideoClick;
window.handleOpenLinkClick = handleOpenLinkClick;
window.initSimpleAds = initSimpleAds;
window.startCountdown = startCountdown;
window.loadAdRewards = loadAdRewards;

// Загружаем награды при инициализации
loadAdRewards();


