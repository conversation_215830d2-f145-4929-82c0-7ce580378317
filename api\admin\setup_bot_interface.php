<?php
/**
 * setup_bot_interface.php
 * API для настройки интерфейса Telegram бота из админки
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Подключаем только bot/config.php для функции telegramRequest
require_once __DIR__ . '/../../bot/config.php';

/**
 * Загрузка текстов бота из файла
 */
function loadBotTexts() {
    $filePath = __DIR__ . '/../../bot/bot_texts.json';
    if (!file_exists($filePath)) {
        return null;
    }

    $content = file_get_contents($filePath);
    return json_decode($content, true);
}

/**
 * Получение текста бота по ключу
 */
function getBotText($key, $language = 'ru', $params = []) {
    $texts = loadBotTexts();
    if (!$texts || !isset($texts[$language])) {
        return null;
    }

    $keys = explode('.', $key);
    $value = $texts[$language];

    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return null;
        }
        $value = $value[$k];
    }

    // Заменяем параметры
    if (!empty($params)) {
        foreach ($params as $param => $replacement) {
            $value = str_replace('{' . $param . '}', $replacement, $value);
        }
    }

    return $value;
}

/**
 * Настройка Menu Button (синяя кнопка в поле ввода)
 */
function setupMenuButton() {
    $results = [];

    // Получаем тексты из файла или используем fallback
    $textRu = getBotText('bot_interface.menu_button_text', 'ru') ?: '🚀 Запустить';
    $textEn = getBotText('bot_interface.menu_button_text', 'en') ?: '🚀 Launch';

    // Настройка Menu Button для русского языка
    $menuButtonRu = [
        'type' => 'web_app',
        'text' => $textRu,
        'web_app' => [
            'url' => WEBAPP_URL
        ]
    ];

    $resultRu = telegramRequest('setChatMenuButton', [
        'menu_button' => json_encode($menuButtonRu)
    ]);

    $results['ru'] = $resultRu !== false;

    // Настройка Menu Button для английского языка
    $menuButtonEn = [
        'type' => 'web_app',
        'text' => $textEn,
        'web_app' => [
            'url' => WEBAPP_URL
        ]
    ];

    $resultEn = telegramRequest('setChatMenuButton', [
        'menu_button' => json_encode($menuButtonEn)
    ]);

    $results['en'] = $resultEn !== false;

    return $results;
}

/**
 * Настройка Bot Commands (меню команд)
 */
function setupBotCommands() {
    $results = [];

    // Команды для русского языка
    $commandsRu = [
        [
            'command' => 'start',
            'description' => getBotText('commands.start', 'ru') ?: '🚀 Запустить приложение и начать зарабатывать'
        ],
        [
            'command' => 'balance',
            'description' => getBotText('commands.balance', 'ru') ?: '💰 Посмотреть баланс и историю выплат'
        ],
        [
            'command' => 'stats',
            'description' => getBotText('commands.stats', 'ru') ?: '📊 Статистика заработка и рефералов'
        ],
        [
            'command' => 'help',
            'description' => getBotText('commands.help', 'ru') ?: '❓ Помощь и инструкции по использованию'
        ]
    ];

    $resultRu = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsRu),
        'language_code' => 'ru'
    ]);

    $results['ru'] = $resultRu !== false;

    // Команды для английского языка
    $commandsEn = [
        [
            'command' => 'start',
            'description' => getBotText('commands.start', 'en') ?: '🚀 Launch app and start earning'
        ],
        [
            'command' => 'balance',
            'description' => getBotText('commands.balance', 'en') ?: '💰 Check balance and withdrawal history'
        ],
        [
            'command' => 'stats',
            'description' => getBotText('commands.stats', 'en') ?: '📊 Earnings and referrals statistics'
        ],
        [
            'command' => 'help',
            'description' => getBotText('commands.help', 'en') ?: '❓ Help and usage instructions'
        ]
    ];

    $resultEn = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsEn),
        'language_code' => 'en'
    ]);

    $results['en'] = $resultEn !== false;

    // Команды по умолчанию
    $resultDefault = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsEn)
    ]);

    $results['default'] = $resultDefault !== false;

    return $results;
}

/**
 * Настройка описаний бота
 */
function setupBotDescriptions() {
    $results = [];

    // Полное описание для русского языка
    $descriptionRu = getBotText('bot_interface.description', 'ru') ?:
        "💰 UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!\n\n🚀 Моментальные выплаты на кошелёк\n👥 Реферальная программа 10%\n💎 1 монета = $0.001 USD\n\n📱 Нажмите кнопку ниже для запуска приложения!";

    $resultDescRu = telegramRequest('setMyDescription', [
        'description' => $descriptionRu,
        'language_code' => 'ru'
    ]);

    $results['description_ru'] = $resultDescRu !== false;

    // Полное описание для английского языка
    $descriptionEn = getBotText('bot_interface.description', 'en') ?:
        "💰 UniQPaid - earn cryptocurrency by watching ads!\n\n🚀 Instant payouts to wallet\n👥 10% referral program\n💎 1 coin = $0.001 USD\n\n📱 Press the button below to launch the app!";

    $resultDescEn = telegramRequest('setMyDescription', [
        'description' => $descriptionEn,
        'language_code' => 'en'
    ]);

    $results['description_en'] = $resultDescEn !== false;

    // Описание по умолчанию
    $resultDescDefault = telegramRequest('setMyDescription', [
        'description' => $descriptionEn
    ]);

    $results['description_default'] = $resultDescDefault !== false;

    // Короткое описание для русского языка
    $shortDescRu = getBotText('bot_interface.description_short', 'ru') ?:
        "💰 Зарабатывайте криптовалюту за просмотр рекламы! Моментальные выплаты.";

    $resultShortRu = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescRu,
        'language_code' => 'ru'
    ]);

    $results['short_description_ru'] = $resultShortRu !== false;

    // Короткое описание для английского языка
    $shortDescEn = getBotText('bot_interface.description_short', 'en') ?:
        "💰 Earn cryptocurrency by watching ads! Instant payouts.";

    $resultShortEn = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescEn,
        'language_code' => 'en'
    ]);

    $results['short_description_en'] = $resultShortEn !== false;

    // Короткое описание по умолчанию
    $resultShortDefault = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescEn
    ]);

    $results['short_description_default'] = $resultShortDefault !== false;

    return $results;
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'setup_menu_button':
            $results = setupMenuButton();
            echo json_encode([
                'success' => $results['ru'] && $results['en'],
                'message' => 'Menu Button настроена',
                'details' => $results
            ]);
            break;
            
        case 'setup_commands':
            $results = setupBotCommands();
            echo json_encode([
                'success' => $results['ru'] && $results['en'] && $results['default'],
                'message' => 'Bot Commands настроены',
                'details' => $results
            ]);
            break;
            
        case 'setup_descriptions':
            $results = setupBotDescriptions();
            $allSuccess = true;
            foreach ($results as $result) {
                if (!$result) {
                    $allSuccess = false;
                    break;
                }
            }
            echo json_encode([
                'success' => $allSuccess,
                'message' => 'Описания бота настроены',
                'details' => $results
            ]);
            break;
            
        case 'setup_all':
            $menuResults = setupMenuButton();
            $commandsResults = setupBotCommands();
            $descriptionsResults = setupBotDescriptions();
            
            $allResults = array_merge($menuResults, $commandsResults, $descriptionsResults);
            $allSuccess = true;
            foreach ($allResults as $result) {
                if (!$result) {
                    $allSuccess = false;
                    break;
                }
            }
            
            echo json_encode([
                'success' => $allSuccess,
                'message' => 'Все настройки бота выполнены',
                'details' => [
                    'menu_button' => $menuResults,
                    'commands' => $commandsResults,
                    'descriptions' => $descriptionsResults
                ]
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'Неизвестное действие'
            ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка: ' . $e->getMessage()
    ]);
}
?>
