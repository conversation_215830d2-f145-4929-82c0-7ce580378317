<?php
/**
 * Тест исправлений системы выплат
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 Тест исправлений системы выплат</h1>\n";

// 1. Проверка файлов
echo "<h2>📁 1. Проверка файлов</h2>\n";

$requiredFiles = [
    'calculateCrypto.php',
    'requestWithdrawal.php', 
    'checkUserWithdrawals.php',
    'validate_initdata.php',
    'config.php',
    'FeeCalculator.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ {$file} - найден<br>\n";
    } else {
        echo "❌ {$file} - НЕ НАЙДЕН<br>\n";
    }
}

// 2. Проверка констант
echo "<h2>⚙️ 2. Проверка констант</h2>\n";

require_once __DIR__ . '/config.php';

$requiredConstants = [
    'TELEGRAM_BOT_TOKEN',
    'NOWPAYMENTS_API_KEY',
    'CONVERSION_RATE',
    'MIN_WITHDRAWAL_AMOUNT'
];

foreach ($requiredConstants as $constant) {
    if (defined($constant)) {
        $value = constant($constant);
        if ($constant === 'TELEGRAM_BOT_TOKEN') {
            $value = substr($value, 0, 10) . '...'; // Скрываем токен
        }
        echo "✅ {$constant} = {$value}<br>\n";
    } else {
        echo "❌ {$constant} - НЕ ОПРЕДЕЛЕНА<br>\n";
    }
}

// 3. Тест calculateCrypto.php
echo "<h2>🧮 3. Тест calculateCrypto.php</h2>\n";

try {
    $testData = [
        'coins_amount' => 1500,
        'currency' => 'ton'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost' . dirname($_SERVER['REQUEST_URI']) . '/calculateCrypto.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP код: {$httpCode}<br>\n";
    echo "Ответ: " . htmlspecialchars($response) . "<br>\n";
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "✅ calculateCrypto.php работает корректно<br>\n";
        } else {
            echo "⚠️ calculateCrypto.php возвращает неожиданный формат<br>\n";
        }
    } else {
        echo "❌ calculateCrypto.php недоступен (HTTP {$httpCode})<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка тестирования calculateCrypto.php: " . $e->getMessage() . "<br>\n";
}

// 4. Проверка функций валидации
echo "<h2>🔐 4. Проверка функций валидации</h2>\n";

require_once __DIR__ . '/validate_initdata.php';

if (function_exists('validateTelegramInitData')) {
    echo "✅ validateTelegramInitData - функция найдена<br>\n";
    
    // Тест с пустыми данными
    $result = validateTelegramInitData('');
    if ($result === false) {
        echo "✅ Валидация пустых данных работает корректно<br>\n";
    } else {
        echo "⚠️ Валидация пустых данных возвращает неожиданный результат<br>\n";
    }
} else {
    echo "❌ validateTelegramInitData - функция НЕ НАЙДЕНА<br>\n";
}

// 5. Проверка FeeCalculator
echo "<h2>💰 5. Проверка FeeCalculator</h2>\n";

try {
    require_once __DIR__ . '/FeeCalculator.php';
    
    $calculator = FeeCalculator::getInstance();
    echo "✅ FeeCalculator создан успешно<br>\n";
    
    $result = $calculator->calculateWithdrawalAmount(1500, 'ton');
    echo "Результат расчета: " . json_encode($result) . "<br>\n";
    
    if (isset($result['success'])) {
        echo "✅ FeeCalculator работает корректно<br>\n";
    } else {
        echo "⚠️ FeeCalculator возвращает неожиданный формат<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка FeeCalculator: " . $e->getMessage() . "<br>\n";
}

echo "<h2>✅ Тест завершен</h2>\n";
echo "<p><a href='../'>← Вернуться к приложению</a></p>\n";
?>
