<?php
/**
 * api/admin/settings.php
 * Страница настроек административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in admin/settings.php');
    die('Ошибка: Не удалось загрузить config.php');
}
// --- Конец проверки зависимостей ---

/**
 * Функция для получения актуальных значений из config.php
 * Читает файл напрямую, а не использует константы
 */
if (!function_exists('getActualConfigValue')) {
    function getActualConfigValue($constantName, $defaultValue = null) {
        static $configContent = null;

        // Читаем файл только один раз за запрос
        if ($configContent === null) {
            $configFile = __DIR__ . '/../config.php';
            $configContent = file_get_contents($configFile);
        }

        // Ищем определение константы в файле
        $patterns = [
            "/define\('$constantName',\s*([^)]+)\);/", // Обычное определение
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $configContent, $matches)) {
                $value = trim($matches[1], " '\"");

                // Обрабатываем разные типы значений
                if ($value === 'true') return true;
                if ($value === 'false') return false;
                if (is_numeric($value)) return floatval($value);
                return $value;
            }
        }

        return $defaultValue;
    }
}

// Обработка формы изменения учетных данных
$credentialsMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_credentials') {
    if (isset($_POST['new_username']) && isset($_POST['new_password']) && isset($_POST['confirm_password'])) {
        $newUsername = trim($_POST['new_username']);
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];

        if (empty($newUsername)) {
            $credentialsMessage = 'Ошибка: Имя пользователя не может быть пустым';
        } elseif (empty($newPassword)) {
            $credentialsMessage = 'Ошибка: Пароль не может быть пустым';
        } elseif ($newPassword !== $confirmPassword) {
            $credentialsMessage = 'Ошибка: Пароли не совпадают';
        } else {
            if (changeAdminCredentials($newUsername, $newPassword)) {
                $credentialsMessage = 'Учетные данные успешно изменены';

                // Обновляем имя пользователя в сессии
                $_SESSION['admin_username'] = $newUsername;
            } else {
                $credentialsMessage = 'Ошибка: Не удалось изменить учетные данные';
            }
        }
    } else {
        $credentialsMessage = 'Ошибка: Не все поля заполнены';
    }
}

// Обработка формы изменения настроек приложения
$settingsMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_settings') {
    // Получаем текущие настройки из config.php
    $configFile = __DIR__ . '/../config.php';
    $configContent = file_get_contents($configFile);

    if ($configContent !== false) {
        $updated = false;

        // Обновляем награды за типы рекламы
        if (isset($_POST['ad_reward_native_banner']) && is_numeric($_POST['ad_reward_native_banner'])) {
            $value = intval($_POST['ad_reward_native_banner']);
            $pattern = "/define\('AD_REWARD_NATIVE_BANNER',\s*\d+\);/";
            $replacement = "define('AD_REWARD_NATIVE_BANNER', $value);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        if (isset($_POST['ad_reward_interstitial']) && is_numeric($_POST['ad_reward_interstitial'])) {
            $value = intval($_POST['ad_reward_interstitial']);
            $pattern = "/define\('AD_REWARD_INTERSTITIAL',\s*\d+\);/";
            $replacement = "define('AD_REWARD_INTERSTITIAL', $value);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        if (isset($_POST['ad_reward_rewarded_video']) && is_numeric($_POST['ad_reward_rewarded_video'])) {
            $value = intval($_POST['ad_reward_rewarded_video']);
            $pattern = "/define\('AD_REWARD_REWARDED_VIDEO',\s*\d+\);/";
            $replacement = "define('AD_REWARD_REWARDED_VIDEO', $value);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем REFERRAL_BONUS_PERCENT
        if (isset($_POST['referral_bonus_percent']) && is_numeric($_POST['referral_bonus_percent'])) {
            $referralBonusPercent = floatval($_POST['referral_bonus_percent']) / 100; // Поле в % - конвертируем в десятичную дробь
            $pattern = "/define\('REFERRAL_BONUS_PERCENT',\s*[\d\.]+\);/";
            $replacement = "define('REFERRAL_BONUS_PERCENT', $referralBonusPercent);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем MIN_WITHDRAWAL_AMOUNT
        if (isset($_POST['min_withdrawal_amount']) && is_numeric($_POST['min_withdrawal_amount'])) {
            $minWithdrawalAmount = intval($_POST['min_withdrawal_amount']);
            $pattern = "/define\('MIN_WITHDRAWAL_AMOUNT',\s*\d+\);/";
            $replacement = "define('MIN_WITHDRAWAL_AMOUNT', $minWithdrawalAmount);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем MIN_BALANCE_FOR_WITHDRAWAL
        if (isset($_POST['min_balance_for_withdrawal']) && is_numeric($_POST['min_balance_for_withdrawal'])) {
            $minBalanceForWithdrawal = intval($_POST['min_balance_for_withdrawal']);
            $pattern = "/define\('MIN_BALANCE_FOR_WITHDRAWAL',\s*\d+\);/";
            $replacement = "define('MIN_BALANCE_FOR_WITHDRAWAL', $minBalanceForWithdrawal);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем CONVERSION_RATE
        if (isset($_POST['conversion_rate']) && is_numeric($_POST['conversion_rate'])) {
            $conversionRate = floatval($_POST['conversion_rate']);
            $pattern = "/define\('CONVERSION_RATE',\s*[\d\.]+\);/";
            $replacement = "define('CONVERSION_RATE', $conversionRate);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем SHOW_FEES_TO_USER
        $showFeesToUser = isset($_POST['show_fees_to_user']) ? 'true' : 'false';
        $pattern = "/define\('SHOW_FEES_TO_USER',\s*(true|false)\);/";
        $replacement = "define('SHOW_FEES_TO_USER', $showFeesToUser);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;

        // Обновляем NOWPAYMENTS_EMAIL
        if (isset($_POST['nowpayments_email']) && !empty($_POST['nowpayments_email'])) {
            $email = addslashes($_POST['nowpayments_email']);
            $pattern = "/define\('NOWPAYMENTS_EMAIL',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_EMAIL', '$email');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_PASSWORD
        if (isset($_POST['nowpayments_password']) && !empty($_POST['nowpayments_password'])) {
            $password = addslashes($_POST['nowpayments_password']);
            $pattern = "/define\('NOWPAYMENTS_PASSWORD',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_PASSWORD', '$password');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_API_KEY
        if (isset($_POST['nowpayments_api_key']) && !empty($_POST['nowpayments_api_key'])) {
            $apiKey = addslashes($_POST['nowpayments_api_key']);
            $pattern = "/define\('NOWPAYMENTS_API_KEY',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_API_KEY', '$apiKey');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_PUBLIC_KEY
        if (isset($_POST['nowpayments_public_key']) && !empty($_POST['nowpayments_public_key'])) {
            $publicKey = addslashes($_POST['nowpayments_public_key']);
            $pattern = "/define\('NOWPAYMENTS_PUBLIC_KEY',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_PUBLIC_KEY', '$publicKey');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_IPN_SECRET
        if (isset($_POST['nowpayments_ipn_secret']) && !empty($_POST['nowpayments_ipn_secret'])) {
            $ipnSecret = addslashes($_POST['nowpayments_ipn_secret']);
            $pattern = "/define\('NOWPAYMENTS_IPN_SECRET',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_IPN_SECRET', '$ipnSecret');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Сохраняем обновленный файл конфигурации
        if ($updated) {
            if (file_put_contents($configFile, $configContent) !== false) {
                $settingsMessage = 'Настройки успешно обновлены';

                // Перезагружаем config.php
                require_once __DIR__ . '/../config.php';
            } else {
                $settingsMessage = 'Ошибка: Не удалось сохранить настройки';
            }
        } else {
            $settingsMessage = 'Ошибка: Не удалось обновить настройки';
        }
    } else {
        $settingsMessage = 'Ошибка: Не удалось прочитать файл конфигурации';
    }
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Настройки</h1>
            </div>

            <div class="row">
                <!-- Настройки учетных данных -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Учетные данные администратора</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($credentialsMessage)): ?>
                                <div class="alert <?php echo strpos($credentialsMessage, 'Ошибка:') === 0 ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
                                    <?php echo $credentialsMessage; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <form method="post" id="credentials-form">
                                <input type="hidden" name="action" value="change_credentials">

                                <div class="mb-3">
                                    <label for="new_username" class="form-label">Новое имя пользователя</label>
                                    <input type="text" class="form-control" id="new_username" name="new_username" value="<?php echo $_SESSION['admin_username']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">Новый пароль</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Подтверждение пароля</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Настройки приложения -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Настройки приложения</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($settingsMessage)): ?>
                                <div class="alert <?php echo strpos($settingsMessage, 'Ошибка:') === 0 ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
                                    <?php echo $settingsMessage; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <form method="post" id="app-settings-form">
                                <input type="hidden" name="action" value="change_settings">

                                <div class="mb-3">
                                    <label class="form-label">Награды за типы рекламы</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="ad_reward_native_banner" class="form-label">Баннер (монет)</label>
                                            <input type="number" class="form-control" id="ad_reward_native_banner"
                                                   name="ad_reward_native_banner"
                                                   value="<?php echo getActualConfigValue('AD_REWARD_NATIVE_BANNER', 10); ?>"
                                                   min="1" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="ad_reward_interstitial" class="form-label">Полноэкранная (монет)</label>
                                            <input type="number" class="form-control" id="ad_reward_interstitial"
                                                   name="ad_reward_interstitial"
                                                   value="<?php echo getActualConfigValue('AD_REWARD_INTERSTITIAL', 10); ?>"
                                                   min="1" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="ad_reward_rewarded_video" class="form-label">Видео (монет)</label>
                                            <input type="number" class="form-control" id="ad_reward_rewarded_video"
                                                   name="ad_reward_rewarded_video"
                                                   value="<?php echo getActualConfigValue('AD_REWARD_REWARDED_VIDEO', 1); ?>"
                                                   min="1" required>
                                        </div>
                                    </div>
                                </div>



                                <div class="mb-4">
                                    <label class="form-label fw-bold">👤 Лимиты для пользователя в день</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="user_ad_limit_native_banner" class="form-label">Баннер (просмотров/день)</label>
                                            <input type="number" class="form-control" id="user_ad_limit_native_banner"
                                                   name="user_ad_limit_native_banner"
                                                   value="<?php echo getActualConfigValue('USER_AD_LIMIT_NATIVE_BANNER', 20); ?>"
                                                   min="1" max="1000" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="user_ad_limit_interstitial" class="form-label">Полноэкранная (просмотров/день)</label>
                                            <input type="number" class="form-control" id="user_ad_limit_interstitial"
                                                   name="user_ad_limit_interstitial"
                                                   value="<?php echo getActualConfigValue('USER_AD_LIMIT_INTERSTITIAL', 20); ?>"
                                                   min="1" max="1000" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="user_ad_limit_rewarded_video" class="form-label">Видео (просмотров/день)</label>
                                            <input type="number" class="form-control" id="user_ad_limit_rewarded_video"
                                                   name="user_ad_limit_rewarded_video"
                                                   value="<?php echo getActualConfigValue('USER_AD_LIMIT_REWARDED_VIDEO', 20); ?>"
                                                   min="1" max="1000" required>
                                        </div>
                                    </div>
                                    <div class="form-text">Сколько раз в день один пользователь может успешно посмотреть рекламу каждого типа и получить награду</div>
                                </div>

                                <div class="mb-3">
                                    <label for="referral_bonus_percent" class="form-label">Процент реферального бонуса (%)</label>
                                    <input type="number" class="form-control" id="referral_bonus_percent" name="referral_bonus_percent" value="<?php echo getActualConfigValue('REFERRAL_BONUS_PERCENT', 0.1) * 100; ?>" min="0" max="100" step="0.1" required>
                                </div>

                                <div class="mb-3">
                                    <label for="min_withdrawal_amount" class="form-label">Минимальная сумма для вывода (монеты)</label>
                                    <input type="number" class="form-control" id="min_withdrawal_amount" name="min_withdrawal_amount" value="<?php echo getActualConfigValue('MIN_WITHDRAWAL_AMOUNT', 0); ?>" min="0" required>
                                </div>

                                <div class="mb-3">
                                    <label for="min_balance_for_withdrawal" class="form-label">Минимальный баланс для доступа к выводу (монеты)</label>
                                    <input type="number" class="form-control" id="min_balance_for_withdrawal" name="min_balance_for_withdrawal" value="<?php echo getActualConfigValue('MIN_BALANCE_FOR_WITHDRAWAL', 100); ?>" min="1" required>
                                    <div class="form-text">Пользователь сможет выводить средства только при наличии этого минимального баланса</div>
                                </div>

                                <div class="mb-3">
                                    <label for="conversion_rate" class="form-label">Курс конвертации монет в USD</label>
                                    <input type="number" class="form-control" id="conversion_rate" name="conversion_rate" value="<?php echo getActualConfigValue('CONVERSION_RATE', 0.001); ?>" min="0.0001" step="0.0001" required>
                                    <div class="form-text">1 монета = <span id="conversion_preview"><?php echo getActualConfigValue('CONVERSION_RATE', 0.001); ?></span> USD (например: 0.001 означает 1 монета = $0.001)</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="show_fees_to_user" name="show_fees_to_user" <?php echo getActualConfigValue('SHOW_FEES_TO_USER', true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="show_fees_to_user">
                                            <strong>Показывать комиссии пользователю</strong>
                                        </label>
                                        <div class="form-text">Если включено, пользователи будут видеть детальную информацию о комиссиях в калькуляторе вывода. Если выключено, комиссии будут скрыты.</div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Настройки NOWPayments API -->
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Настройки NOWPayments API</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="nowpayments-form">
                                <input type="hidden" name="action" value="change_settings">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_email" class="form-label">Email аккаунта NOWPayments</label>
                                            <input type="email" class="form-control" id="nowpayments_email" name="nowpayments_email" value="<?php echo getActualConfigValue('NOWPAYMENTS_EMAIL', ''); ?>" required>
                                            <div class="form-text">Email для входа в панель NOWPayments</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_password" class="form-label">Пароль аккаунта NOWPayments</label>
                                            <input type="password" class="form-control" id="nowpayments_password" name="nowpayments_password" value="<?php echo getActualConfigValue('NOWPAYMENTS_PASSWORD', ''); ?>" required>
                                            <div class="form-text">Пароль для входа в панель NOWPayments</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_api_key" class="form-label">API Key (приватный)</label>
                                            <input type="text" class="form-control" id="nowpayments_api_key" name="nowpayments_api_key" value="<?php echo getActualConfigValue('NOWPAYMENTS_API_KEY', ''); ?>" required>
                                            <div class="form-text">Приватный ключ для создания выплат</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_public_key" class="form-label">Public Key</label>
                                            <input type="text" class="form-control" id="nowpayments_public_key" name="nowpayments_public_key" value="<?php echo defined('NOWPAYMENTS_PUBLIC_KEY') ? NOWPAYMENTS_PUBLIC_KEY : ''; ?>" required>
                                            <div class="form-text">Публичный ключ для получения оценок</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="nowpayments_ipn_secret" class="form-label">IPN Secret Key</label>
                                    <input type="text" class="form-control" id="nowpayments_ipn_secret" name="nowpayments_ipn_secret" value="<?php echo defined('NOWPAYMENTS_IPN_SECRET') ? NOWPAYMENTS_IPN_SECRET : ''; ?>" required>
                                    <div class="form-text">Секретный ключ для проверки подписи callback уведомлений</div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Важно:</strong> Все ключи можно найти в панели управления NOWPayments.
                                    Email и пароль используются для автоматического получения JWT токенов.
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить настройки NOWPayments</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
    // Обновление предпросмотра курса конвертации
    document.getElementById('conversion_rate').addEventListener('input', function() {
        const rate = parseFloat(this.value);
        document.getElementById('conversion_preview').textContent = rate.toFixed(4);
    });

    // Универсальная функция для AJAX обработки форм
    function setupFormAjax(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {};

            // Преобразуем FormData в объект
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Показываем индикатор загрузки
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Сохранение...';
            submitBtn.disabled = true;

            // Отправляем AJAX запрос
            fetch('save_settings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showAlert(formId, 'success', result.message || 'Настройки успешно сохранены');
                    console.log('Настройки сохранены:', result);

                    // Обновляем значения в форме если нужно
                    if (result.updated) {
                        console.log('Обновляем поля формы:', result.updated);
                        updateFormValues(form, result.updated);

                        // Дополнительно обновляем поля наград с правильными значениями
                        if (result.updated.ad_reward_native_banner !== undefined) {
                            const field = document.getElementById('ad_reward_native_banner');
                            if (field) field.value = result.updated.ad_reward_native_banner;
                        }
                        if (result.updated.ad_reward_interstitial !== undefined) {
                            const field = document.getElementById('ad_reward_interstitial');
                            if (field) field.value = result.updated.ad_reward_interstitial;
                        }
                        if (result.updated.ad_reward_rewarded_video !== undefined) {
                            const field = document.getElementById('ad_reward_rewarded_video');
                            if (field) field.value = result.updated.ad_reward_rewarded_video;
                        }

                        // Если обновились награды, уведомляем об этом
                        if (result.updated.ad_reward_native_banner !== undefined ||
                            result.updated.ad_reward_interstitial !== undefined ||
                            result.updated.ad_reward_rewarded_video !== undefined) {
                            console.log('🏆 Награды обновлены! Приложение должно обновить badges.');
                            showAlert(formId, 'info', 'Награды обновлены! Обновите приложение для отображения новых значений.');
                        }
                    }
                } else {
                    showAlert(formId, 'danger', result.error || 'Ошибка сохранения настроек');
                }
            })
            .catch(error => {
                console.error('Ошибка:', error);
                showAlert(formId, 'danger', 'Ошибка соединения с сервером');
            })
            .finally(() => {
                // Восстанавливаем кнопку
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // Функция для показа уведомлений
    function showAlert(formId, type, message) {
        const form = document.getElementById(formId);
        if (!form) return;

        // Удаляем старые уведомления в этой форме
        const oldAlerts = form.querySelectorAll('.alert-dynamic');
        oldAlerts.forEach(alert => alert.remove());

        // Создаем новое уведомление
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-dynamic`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Вставляем уведомление в начало формы
        form.insertBefore(alertDiv, form.firstChild);

        // Автоматически скрываем через 5 секунд
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Функция для обновления значений в форме
    function updateFormValues(form, values) {
        Object.entries(values).forEach(([key, value]) => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = value === 'true' || value === true;
                } else {
                    // Обновляем значение в поле
                    input.value = value;

                    // Специальная обработка для полей наград
                    if (key.startsWith('ad_reward_')) {
                        console.log(`Обновлено поле ${key}: ${value}`);
                    }

                    // Обновляем предпросмотр конверсии если это поле конверсии
                    if (key === 'conversion_rate') {
                        const preview = document.getElementById('conversion_preview');
                        if (preview) {
                            const rate = parseFloat(value);
                            preview.textContent = rate.toFixed(4);
                        }
                    }

                    // Специальная обработка для процента реферального бонуса
                    if (key === 'referral_bonus_percent') {
                        // Значение уже в процентах, просто обновляем поле
                        field.value = parseFloat(value).toFixed(1);
                        console.log(`Обновлен реферальный бонус: ${value}%`);
                        continue; // Пропускаем стандартную обработку
                    }
                }
            }
        });
    }

    // Функция для обновления всех полей из config.php
    async function refreshAllFields() {
        try {
            const response = await fetch('get_current_config.php');
            const data = await response.json();

            if (data.success) {
                // Обновляем поля наград
                if (data.config.ad_reward_native_banner !== undefined) {
                    const field = document.getElementById('ad_reward_native_banner');
                    if (field) field.value = data.config.ad_reward_native_banner;
                }
                if (data.config.ad_reward_interstitial !== undefined) {
                    const field = document.getElementById('ad_reward_interstitial');
                    if (field) field.value = data.config.ad_reward_interstitial;
                }
                if (data.config.ad_reward_rewarded_video !== undefined) {
                    const field = document.getElementById('ad_reward_rewarded_video');
                    if (field) field.value = data.config.ad_reward_rewarded_video;
                }

                // Обновляем другие поля
                if (data.config.conversion_rate !== undefined) {
                    const field = document.getElementById('conversion_rate');
                    if (field) {
                        field.value = data.config.conversion_rate;
                        const preview = document.getElementById('conversion_preview');
                        if (preview) preview.textContent = data.config.conversion_rate.toFixed(4);
                    }
                }

                // Обновляем процент реферального бонуса (значение уже в процентах)
                if (data.config.referral_bonus_percent !== undefined) {
                    const field = document.getElementById('referral_bonus_percent');
                    if (field) {
                        field.value = parseFloat(data.config.referral_bonus_percent).toFixed(1);
                    }
                }

                console.log('Все поля обновлены из config.php');
            }
        } catch (error) {
            console.error('Ошибка обновления полей:', error);
        }
    }

    // Инициализируем AJAX для всех форм
    document.addEventListener('DOMContentLoaded', function() {
        setupFormAjax('credentials-form');
        setupFormAjax('app-settings-form');
        setupFormAjax('nowpayments-form');

        console.log('AJAX обработка настроена для всех форм');
    });
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
