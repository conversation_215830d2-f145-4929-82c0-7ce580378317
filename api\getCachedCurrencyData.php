<?php
/**
 * API для получения кешированных данных о валютах
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Исправляем проблему с REQUEST_METHOD для прямых вызовов
if (!isset($_SERVER['REQUEST_METHOD'])) {
    $_SERVER['REQUEST_METHOD'] = 'GET';
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/currency_cache.php';

try {
    // Получаем все валюты из кеша
    $currencies = getAllCachedCurrencies();
    
    if (empty($currencies)) {
        echo json_encode([
            'success' => false,
            'error' => 'Кеш валют пуст',
            'currencies' => []
        ]);
        exit;
    }
    
    // Проверяем возраст кеша
    $cache = loadCurrencyCache();
    $lastUpdated = $cache['last_updated'] ?? 0;
    $cacheAge = time() - $lastUpdated;
    $isExpired = isCacheExpired();
    
    // Преобразуем данные в формат, совместимый с getCurrencyData.php
    $formattedCurrencies = [];
    
    foreach ($currencies as $code => $data) {
        $rate = $data['rate_usd'] ?? 1;
        $minAmount = $data['min_amount'] ?? 0;
        $networkFee = $data['network_fee'] ?? 0;

        // --- ИСПРАВЛЕНИЕ: ПРАВИЛЬНЫЙ РАСЧЕТ МИНИМУМА С УЧЕТОМ КОМИССИИ ---
        // 1. Конвертируем минимальную сумму в крипте в USD
        $minAmountInUsd = $minAmount * $rate;
        // 2. Складываем минимальную сумму в USD с комиссией в USD
        $totalUsdNeeded = $minAmountInUsd + $networkFee;
        // 3. Конвертируем итоговую сумму в USD в монеты
        $minCoinsWithFee = ceil($totalUsdNeeded / CONVERSION_RATE);

        // Для совместимости оставляем старые поля
        $minCoins = ceil(($minAmount * $rate) / CONVERSION_RATE); // Базовый минимум без комиссии
        $feeInCoins = $minCoinsWithFee - $minCoins; // Комиссия в монетах

        // НОВОЕ: Рассчитываем эффективность и определяем статус
        $dollarAmount = $minCoinsWithFee * CONVERSION_RATE;
        $afterFeeUsd = $dollarAmount - $networkFee;
        $efficiency = $afterFeeUsd > 0 ? (($afterFeeUsd / $dollarAmount) * 100) : 0;

        // Определяем статус на основе эффективности
        if ($efficiency >= 85) {
            $status = 'best';
        } elseif ($efficiency >= 70) {
            $status = 'good';
        } elseif ($efficiency >= 50) {
            $status = 'available';
        } else {
            $status = 'expensive';
        }

        $formattedCurrencies[$code] = [
            'name' => getCurrencyName($code),
            'minCoins' => $minCoinsWithFee, // ИСПРАВЛЕНО: Используем минимум с учетом комиссии
            'minCoinsBase' => $minCoins,    // Базовый минимум без комиссии
            'feeInCoins' => $feeInCoins,    // Комиссия в монетах
            'networkFee' => $networkFee,
            'status' => $status,            // НОВОЕ: Статус на основе эффективности
            'efficiency' => round($efficiency, 1), // НОВОЕ: Эффективность в процентах
            'rate_usd' => $rate,
            'min_amount_crypto' => $minAmount,
            'last_updated' => $data['last_updated'] ?? 0
        ];
    }
    
    echo json_encode([
        'success' => true,
        'currencies' => $formattedCurrencies,
        'cache_info' => [
            'last_updated' => $lastUpdated,
            'cache_age' => $cacheAge,
            'is_expired' => $isExpired,
            'update_interval' => $cache['update_interval'] ?? 3600,
            'next_update' => $lastUpdated + ($cache['update_interval'] ?? 3600)
        ],
        'data_source' => 'cache',
        'generated_at' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("getCachedCurrencyData ERROR: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения данных из кеша',
        'details' => $e->getMessage(),
        'currencies' => []
    ]);
}

/**
 * Получает человекочитаемое название валюты
 */
function getCurrencyName($code) {
    $names = [
        'eth' => 'Ethereum (ETH)',
        'btc' => 'Bitcoin (BTC)',
        'ton' => 'TON (Telegram)',
        'usdttrc20' => 'USDT (TRC20)',
        'ltc' => 'Litecoin (LTC)',
        'bch' => 'Bitcoin Cash (BCH)',
        'xrp' => 'Ripple (XRP)',
        'ada' => 'Cardano (ADA)',
        'dot' => 'Polkadot (DOT)'
    ];
    
    return $names[$code] ?? strtoupper($code);
}

?>
