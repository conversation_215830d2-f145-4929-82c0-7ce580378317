/* ======================================== */
/* 3D BUTTON EFFECTS - Enhanced Physical Button Interactions */
/* ======================================== */

/**
 * Инициализация 3D эффектов для кнопок
 */
function init3DButtonEffects() {
    console.log('🎯 Инициализация 3D эффектов кнопок...');
    
    // Добавляем дополнительные эффекты для всех 3D кнопок
    const buttons3D = document.querySelectorAll('.btn-3d');
    
    buttons3D.forEach(button => {
        enhance3DButton(button);
    });
    
    // Добавляем специальные эффекты для основных кнопок действий
    enhanceMainActionButtons();
    
    // Добавляем звуковые эффекты (опционально)
    addSoundEffects();
    
    console.log(`✨ 3D эффекты применены к ${buttons3D.length} кнопкам`);
}

/**
 * Улучшение отдельной 3D кнопки
 */
function enhance3DButton(button) {
    // Добавляем эффект наклона при hover
    button.addEventListener('mouseenter', (e) => {
        if (!button.disabled) {
            add3DTiltEffect(button, e);
        }
    });
    
    button.addEventListener('mousemove', (e) => {
        if (!button.disabled) {
            update3DTiltEffect(button, e);
        }
    });
    
    button.addEventListener('mouseleave', () => {
        remove3DTiltEffect(button);
    });
    
    // Добавляем эффект "взрыва" при клике
    button.addEventListener('click', (e) => {
        if (!button.disabled) {
            create3DClickExplosion(button, e);
        }
    });
    
    // Добавляем пульсацию для важных кнопок
    if (button.classList.contains('btn-pulse')) {
        add3DPulseEffect(button);
    }
}

/**
 * Эффект наклона 3D кнопки при hover
 */
function add3DTiltEffect(button, event) {
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const mouseX = event.clientX;
    const mouseY = event.clientY;
    
    const deltaX = (mouseX - centerX) / (rect.width / 2);
    const deltaY = (mouseY - centerY) / (rect.height / 2);
    
    const tiltX = deltaY * 10; // Максимальный наклон 10 градусов
    const tiltY = deltaX * -10;
    
    const front = button.querySelector('.btn-front');
    if (front) {
        front.style.transform = `translateY(-6px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;
        front.style.transformStyle = 'preserve-3d';
    }
}

/**
 * Обновление эффекта наклона при движении мыши
 */
function update3DTiltEffect(button, event) {
    add3DTiltEffect(button, event);
}

/**
 * Удаление эффекта наклона
 */
function remove3DTiltEffect(button) {
    const front = button.querySelector('.btn-front');
    if (front) {
        front.style.transform = 'translateY(-6px) rotateX(0deg) rotateY(0deg)';
    }
}

/**
 * Создание эффекта "взрыва" при клике
 */
function create3DClickExplosion(button, event) {
    const rect = button.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Создаем элемент для эффекта взрыва
    const explosion = document.createElement('div');
    explosion.className = 'btn-3d-explosion';
    explosion.style.cssText = `
        position: absolute;
        left: ${x}px;
        top: ${y}px;
        width: 4px;
        height: 4px;
        background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        animation: btn3dExplosion 0.6s ease-out forwards;
    `;
    
    button.style.position = 'relative';
    button.appendChild(explosion);
    
    // Удаляем элемент после анимации
    setTimeout(() => {
        if (explosion.parentNode) {
            explosion.parentNode.removeChild(explosion);
        }
    }, 600);
}

/**
 * Добавление CSS анимаций для 3D эффектов
 */
function add3DButtonCSS() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes btn3dExplosion {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(20);
                opacity: 0;
            }
        }
        
        @keyframes btn3dPulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);
            }
            50% {
                box-shadow: 0 0 0 15px rgba(255, 165, 0, 0);
            }
        }
        
        @keyframes btn3dShake {
            0%, 100% { transform: translateY(-4px) translateX(0); }
            25% { transform: translateY(-4px) translateX(-2px); }
            75% { transform: translateY(-4px) translateX(2px); }
        }
        
        /* Улучшенные переходы для 3D кнопок */
        .btn-3d .btn-front {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .btn-3d:hover .btn-front {
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .btn-3d:active .btn-front {
            transition: all 0.05s ease-out;
        }
        
        /* Эффект свечения для активных кнопок */
        .btn-3d.btn-glow-active .btn-front {
            box-shadow: 
                0 0 20px rgba(255, 165, 0, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        /* Эффект отключенной кнопки */
        .btn-3d:disabled .btn-front,
        .btn-3d.disabled .btn-front {
            background: #666666;
            color: #999999;
            transform: translateY(-1px);
        }
        
        .btn-3d:disabled .btn-edge,
        .btn-3d.disabled .btn-edge {
            background: #555555;
        }
        
        /* Адаптивные размеры для мобильных устройств */
        @media (max-width: 768px) {
            .btn-3d .btn-front {
                transform: translateY(-3px);
            }
            
            .btn-3d:hover .btn-front {
                transform: translateY(-4px);
            }
            
            .btn-3d:active .btn-front {
                transform: translateY(-1px);
            }
        }
    `;
    
    document.head.appendChild(style);
}

/**
 * Улучшение основных кнопок действий
 */
function enhanceMainActionButtons() {
    // Кнопки заданий - добавляем пульсацию
    const taskButtons = document.querySelectorAll('#openLinkButton, #watchVideoButton, #openAdButton');
    taskButtons.forEach(button => {
        if (!button.disabled) {
            button.classList.add('btn-glow');
        }
    });
    
    // Кнопка вывода - добавляем специальный эффект
    const withdrawButton = document.getElementById('request-withdrawal-button');
    if (withdrawButton && !withdrawButton.disabled) {
        withdrawButton.classList.add('btn-pulse');
    }
    
    // Кнопка поделиться - добавляем анимацию при успешном действии
    const shareButton = document.getElementById('share-app-button');
    if (shareButton) {
        shareButton.addEventListener('click', () => {
            shareButton.classList.add('btn-glow-active');
            setTimeout(() => {
                shareButton.classList.remove('btn-glow-active');
            }, 2000);
        });
    }
}

/**
 * Добавление звуковых эффектов (опционально)
 */
function addSoundEffects() {
    // Проверяем, поддерживает ли браузер Web Audio API
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const buttons3D = document.querySelectorAll('.btn-3d');
        
        buttons3D.forEach(button => {
            button.addEventListener('mousedown', () => {
                if (!button.disabled) {
                    playClickSound();
                }
            });
        });
    }
}

/**
 * Воспроизведение звука клика
 */
function playClickSound() {
    try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioContext = new AudioContext();
        
        // Создаем короткий звук клика
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
        // Игнорируем ошибки звука
    }
}

/**
 * Добавление специальных эффектов для состояний кнопок
 */
function addButtonStateEffects() {
    // Эффект загрузки
    window.show3DButtonLoading = function(buttonId) {
        const button = document.getElementById(buttonId);
        if (button && button.classList.contains('btn-3d')) {
            const front = button.querySelector('.btn-front');
            if (front) {
                front.style.animation = 'btn3dPulse 1s infinite';
                button.disabled = true;
            }
        }
    };
    
    // Убрать эффект загрузки
    window.hide3DButtonLoading = function(buttonId) {
        const button = document.getElementById(buttonId);
        if (button && button.classList.contains('btn-3d')) {
            const front = button.querySelector('.btn-front');
            if (front) {
                front.style.animation = '';
                button.disabled = false;
            }
        }
    };
    
    // Эффект ошибки
    window.show3DButtonError = function(buttonId) {
        const button = document.getElementById(buttonId);
        if (button && button.classList.contains('btn-3d')) {
            const front = button.querySelector('.btn-front');
            if (front) {
                front.style.animation = 'btn3dShake 0.5s ease-in-out';
                setTimeout(() => {
                    front.style.animation = '';
                }, 500);
            }
        }
    };
}

/**
 * Инициализация всех 3D эффектов
 */
document.addEventListener('DOMContentLoaded', () => {
    // Небольшая задержка для загрузки основных стилей
    setTimeout(() => {
        add3DButtonCSS();
        init3DButtonEffects();
        addButtonStateEffects();
        
        console.log('🎯 3D Button Effects активированы!');
    }, 100);
});

// Экспорт функций для использования в других скриптах
window.Button3DEffects = {
    init3DButtonEffects,
    enhance3DButton,
    create3DClickExplosion,
    show3DButtonLoading: () => window.show3DButtonLoading,
    hide3DButtonLoading: () => window.hide3DButtonLoading,
    show3DButtonError: () => window.show3DButtonError
};
