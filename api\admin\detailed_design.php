<?php
session_start();

// Отладка
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Проверка авторизации
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Путь к файлу настроек
$designSettingsFile = __DIR__ . '/../../design_settings.json';

// Функция загрузки настроек
function loadDesignSettings($file) {
    if (!file_exists($file)) {
        return getDefaultSettings();
    }
    
    $json = file_get_contents($file);
    $settings = json_decode($json, true);
    
    if (!$settings) {
        return getDefaultSettings();
    }
    
    return $settings;
}

// Функция сохранения настроек
function saveDesignSettings($file, $settings) {
    $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($file, $json) !== false;
}

// Настройки по умолчанию
function getDefaultSettings() {
    return [
        'colors' => [
            'global' => [
                'body_bg' => '#1a1a1a',
                'text_primary' => '#ffffff',
                'text_secondary' => '#cccccc',
                'border_color' => '#4a4a4a'
            ],
            'header' => [
                'bg' => '#2a2a2a',
                'text' => '#ffffff',
                'balance_bg' => '#00FF7F',
                'balance_text' => '#ffffff',
                'balance_hover' => '#00E066',
                'avatar_border' => '#ff6b35',
                'avatar_bg' => '#ff6b35'
            ],
            'navigation' => [
                'bg' => '#2a2a2a',
                'text' => '#ffffff',
                'text_inactive' => '#999999',
                'active_bg' => '#ff6b35',
                'active_text' => '#ffffff',
                'hover_bg' => '#ff8555',
                'border' => '#4a4a4a'
            ],
            'cards' => [
                'bg' => '#2a2a2a',
                'border' => '#4a4a4a',
                'title_text' => '#ffffff',
                'content_text' => '#cccccc',
                'hover_bg' => '#333333'
            ],
            'buttons' => [
                'primary_bg' => '#ff6b35',
                'primary_text' => '#ffffff',
                'primary_hover' => '#ff8555',
                'secondary_bg' => '#666666',
                'secondary_text' => '#ffffff',
                'secondary_hover' => '#777777',
                'success_bg' => '#00FF7F',
                'success_text' => '#ffffff',
                'success_hover' => '#00E066',
                'danger_bg' => '#FF4444',
                'danger_text' => '#ffffff',
                'danger_hover' => '#FF6666'
            ],
            'calculator' => [
                'tab_bg' => '#333333',
                'tab_text' => '#ffffff',
                'tab_active_bg' => '#ff6b35',
                'tab_active_text' => '#ffffff',
                'tab_hover' => '#444444',
                'input_bg' => '#2a2a2a',
                'input_text' => '#ffffff',
                'input_border' => '#4a4a4a',
                'input_focus' => '#ff6b35'
            ],
            'history' => [
                'item_bg' => '#2a2a2a',
                'item_border' => '#ff6b35',
                'item_text' => '#ffffff',
                'status_pending' => '#FFD700',
                'status_completed' => '#00FF7F',
                'status_failed' => '#FF4444',
                'amount_text' => '#ff6b35'
            ],
            'forms' => [
                'input_bg' => '#2a2a2a',
                'input_text' => '#ffffff',
                'input_border' => '#4a4a4a',
                'input_focus' => '#ff6b35',
                'label_text' => '#cccccc',
                'placeholder_text' => '#999999'
            ]
        ],
        'effects' => [
            'glitch_speed' => 3,
            'glitch_count' => 3,
            'glitch_opacity' => 0.8,
            'bg_opacity' => 0.8,
            'geometric_size' => 1,
            'enable_glitch' => true,
            'enable_geometric' => true,
            'enable_glitch_lines' => true
        ],
        'theme' => 'cyberpunk'
    ];
}

// Загружаем настройки
$designSettings = loadDesignSettings($designSettingsFile);

// Обработка формы
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'save_detailed_design':
                // Обновляем все цвета из формы
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'color_') === 0) {
                        $colorPath = str_replace('color_', '', $key);
                        $pathParts = explode('_', $colorPath);
                        
                        if (count($pathParts) >= 2) {
                            $section = $pathParts[0];
                            $property = implode('_', array_slice($pathParts, 1));
                            
                            if (!isset($designSettings['colors'][$section])) {
                                $designSettings['colors'][$section] = [];
                            }
                            
                            $designSettings['colors'][$section][$property] = $value;
                        }
                    }
                }
                
                if (saveDesignSettings($designSettingsFile, $designSettings)) {
                    $message = 'Детальные настройки дизайна успешно сохранены!';
                    $messageType = 'success';
                    
                    // Генерируем CSS файл
                    exec('php ' . __DIR__ . '/generate_detailed_css.php', $output, $returnCode);
                    if ($returnCode === 0) {
                        $message .= ' CSS файлы обновлены!';
                    } else {
                        $message .= ' Предупреждение: не удалось обновить CSS';
                    }
                } else {
                    $message = 'Ошибка при сохранении настроек дизайна';
                    $messageType = 'danger';
                }
                break;
                
            case 'reset_detailed_design':
                $designSettings = getDefaultSettings();
                if (saveDesignSettings($designSettingsFile, $designSettings)) {
                    $message = 'Настройки дизайна сброшены к умолчанию';
                    $messageType = 'info';
                    exec('php ' . __DIR__ . '/generate_detailed_css.php');
                } else {
                    $message = 'Ошибка при сбросе настроек';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🎨 Детальный дизайн приложения</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="save_detailed_design">
                        
                        <!-- Глобальные настройки -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🌍 Глобальные настройки</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php 
                                    $globalColors = [
                                        'body_bg' => 'Фон страницы',
                                        'text_primary' => 'Основной текст',
                                        'text_secondary' => 'Вторичный текст',
                                        'border_color' => 'Цвет границ'
                                    ];
                                    
                                    foreach ($globalColors as $key => $label): 
                                        $value = $designSettings['colors']['global'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_global_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_global_<?php echo $key; ?>" name="color_global_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_global_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Хедер -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">📱 Хедер приложения</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $headerColors = [
                                        'bg' => 'Фон хедера',
                                        'text' => 'Текст хедера',
                                        'balance_bg' => 'Фон кнопки баланса',
                                        'balance_text' => 'Текст кнопки баланса',
                                        'balance_hover' => 'Кнопка баланса при наведении',
                                        'avatar_border' => 'Граница аватара',
                                        'avatar_bg' => 'Фон аватара'
                                    ];

                                    foreach ($headerColors as $key => $label):
                                        $value = $designSettings['colors']['header'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_header_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_header_<?php echo $key; ?>" name="color_header_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_header_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Навигация -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🧭 Навигация</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $navColors = [
                                        'bg' => 'Фон навигации',
                                        'text' => 'Текст навигации',
                                        'text_inactive' => 'Неактивный текст',
                                        'active_bg' => 'Фон активной кнопки',
                                        'active_text' => 'Текст активной кнопки',
                                        'hover_bg' => 'Фон при наведении',
                                        'border' => 'Граница навигации'
                                    ];

                                    foreach ($navColors as $key => $label):
                                        $value = $designSettings['colors']['navigation'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_navigation_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_navigation_<?php echo $key; ?>" name="color_navigation_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_navigation_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Карточки -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🃏 Карточки и блоки</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $cardColors = [
                                        'bg' => 'Фон карточек',
                                        'border' => 'Граница карточек',
                                        'title_text' => 'Текст заголовков',
                                        'content_text' => 'Текст содержимого',
                                        'hover_bg' => 'Фон при наведении'
                                    ];

                                    foreach ($cardColors as $key => $label):
                                        $value = $designSettings['colors']['cards'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_cards_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_cards_<?php echo $key; ?>" name="color_cards_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_cards_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Кнопки -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🔘 Кнопки</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $buttonColors = [
                                        'primary_bg' => 'Основные кнопки - фон',
                                        'primary_text' => 'Основные кнопки - текст',
                                        'primary_hover' => 'Основные кнопки - наведение',
                                        'secondary_bg' => 'Вторичные кнопки - фон',
                                        'secondary_text' => 'Вторичные кнопки - текст',
                                        'secondary_hover' => 'Вторичные кнопки - наведение',
                                        'success_bg' => 'Успех кнопки - фон',
                                        'success_text' => 'Успех кнопки - текст',
                                        'success_hover' => 'Успех кнопки - наведение',
                                        'danger_bg' => 'Опасность кнопки - фон',
                                        'danger_text' => 'Опасность кнопки - текст',
                                        'danger_hover' => 'Опасность кнопки - наведение'
                                    ];

                                    foreach ($buttonColors as $key => $label):
                                        $value = $designSettings['colors']['buttons'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_buttons_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_buttons_<?php echo $key; ?>" name="color_buttons_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_buttons_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Калькулятор -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🧮 Калькулятор валют</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $calcColors = [
                                        'tab_bg' => 'Фон табов',
                                        'tab_text' => 'Текст табов',
                                        'tab_active_bg' => 'Фон активного таба',
                                        'tab_active_text' => 'Текст активного таба',
                                        'tab_hover' => 'Фон таба при наведении',
                                        'input_bg' => 'Фон полей ввода',
                                        'input_text' => 'Текст полей ввода',
                                        'input_border' => 'Граница полей ввода',
                                        'input_focus' => 'Граница при фокусе'
                                    ];

                                    foreach ($calcColors as $key => $label):
                                        $value = $designSettings['colors']['calculator'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_calculator_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_calculator_<?php echo $key; ?>" name="color_calculator_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_calculator_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- История -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">📊 История выплат</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $historyColors = [
                                        'item_bg' => 'Фон элементов истории',
                                        'item_border' => 'Граница элементов',
                                        'item_text' => 'Текст элементов',
                                        'status_pending' => 'Статус "В ожидании"',
                                        'status_completed' => 'Статус "Завершено"',
                                        'status_failed' => 'Статус "Ошибка"',
                                        'amount_text' => 'Цвет суммы'
                                    ];

                                    foreach ($historyColors as $key => $label):
                                        $value = $designSettings['colors']['history'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_history_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_history_<?php echo $key; ?>" name="color_history_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_history_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Формы -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">📝 Формы и поля ввода</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    $formColors = [
                                        'input_bg' => 'Фон полей ввода',
                                        'input_text' => 'Текст полей ввода',
                                        'input_border' => 'Граница полей ввода',
                                        'input_focus' => 'Граница при фокусе',
                                        'label_text' => 'Текст меток',
                                        'placeholder_text' => 'Текст плейсхолдеров'
                                    ];

                                    foreach ($formColors as $key => $label):
                                        $value = $designSettings['colors']['forms'][$key] ?? '#000000';
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="color_forms_<?php echo $key; ?>" class="form-label"><?php echo $label; ?></label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="color_forms_<?php echo $key; ?>" name="color_forms_<?php echo $key; ?>" value="<?php echo $value; ?>">
                                            <input type="text" class="form-control color-text-input" value="<?php echo $value; ?>" data-color-input="color_forms_<?php echo $key; ?>">
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Кнопки управления -->
                        <div class="card mb-4">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    💾 Сохранить все настройки
                                </button>
                                <button type="button" class="btn btn-warning btn-lg" onclick="resetSettings()">
                                    🔄 Сбросить к умолчанию
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Превью -->
                <div class="col-lg-4">
                    <div class="card sticky-top">
                        <div class="card-header">
                            <h5 class="mb-0">👁️ Превью изменений</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Здесь будет отображаться превью изменений в реальном времени.</p>
                            <div id="preview-area" style="min-height: 300px; border: 1px solid #ddd; border-radius: 5px; padding: 15px;">
                                <!-- Превью будет добавлено через JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Синхронизация color picker и text input
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    const textInputs = document.querySelectorAll('.color-text-input');

    // Обновление текстового поля при изменении color picker
    colorInputs.forEach(colorInput => {
        colorInput.addEventListener('input', function() {
            const textInput = document.querySelector(`[data-color-input="${this.id}"]`);
            if (textInput) {
                textInput.value = this.value.toUpperCase();
            }
        });
    });

    // Обновление color picker при изменении текстового поля
    textInputs.forEach(textInput => {
        textInput.addEventListener('input', function() {
            const colorInputId = this.getAttribute('data-color-input');
            const colorInput = document.getElementById(colorInputId);

            if (colorInput && /^#[0-9A-F]{6}$/i.test(this.value)) {
                colorInput.value = this.value;
            }
        });

        textInput.addEventListener('blur', function() {
            // Валидация и форматирование
            let value = this.value.trim();
            if (value && !value.startsWith('#')) {
                value = '#' + value;
            }
            if (/^#[0-9A-F]{6}$/i.test(value)) {
                this.value = value.toUpperCase();
                const colorInputId = this.getAttribute('data-color-input');
                const colorInput = document.getElementById(colorInputId);
                if (colorInput) {
                    colorInput.value = value;
                }
            }
        });
    });
});

function resetSettings() {
    if (confirm('Вы уверены, что хотите сбросить все настройки к умолчанию?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="reset_detailed_design">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'templates/footer.php'; ?>
