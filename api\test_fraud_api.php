<?php
/**
 * Тестовый файл для проверки работы fraud-detection API
 */

header('Content-Type: application/json');

echo json_encode([
    'test' => 'fraud-detection API test',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => [
        'php_version' => PHP_VERSION,
        'current_dir' => __DIR__,
        'files_exist' => [
            'config.php' => file_exists(__DIR__ . '/config.php'),
            'db_mock.php' => file_exists(__DIR__ . '/db_mock.php'),
            'security.php' => file_exists(__DIR__ . '/security.php'),
            'fraud-detection.php' => file_exists(__DIR__ . '/fraud-detection.php')
        ]
    ]
]);
?>
