/**
 * js/advanced-device-fingerprint.js
 * Расширенная система создания уникальных отпечатков устройств для Фазы 4
 * 
 * Новые возможности:
 * - Детекция эмуляторов и ботов
 * - Защита от spoofing атак
 * - Расширенные методы fingerprinting
 * - Поведенческий анализ
 * - Интеграция с системой блокировки
 */

class AdvancedDeviceFingerprint {
    constructor() {
        this.fingerprint = null;
        this.components = {};
        this.behavioralData = {};
        this.suspiciousIndicators = [];
        this.riskScore = 0;
        
        console.log('[AdvancedDeviceFingerprint] 🔍 Инициализация расширенной системы детекции устройств');
        
        // Начинаем сбор поведенческих данных
        this.startBehavioralTracking();
    }
    
    /**
     * Генерирует расширенный отпечаток устройства с анализом рисков
     */
    async generateAdvancedFingerprint() {
        try {
            console.log('[AdvancedDeviceFingerprint] 📊 Начинаем расширенный сбор данных устройства...');
            
            // Базовые компоненты отпечатка
            this.components = {
                // Стандартные методы
                canvas: await this.getCanvasFingerprint(),
                webgl: await this.getWebGLFingerprint(),
                audio: await this.getAudioFingerprint(),
                screen: this.getScreenFingerprint(),
                system: this.getSystemFingerprint(),
                navigator: this.getNavigatorFingerprint(),
                timezone: this.getTimezoneFingerprint(),
                fonts: await this.getFontsFingerprint(),
                
                // Новые расширенные методы
                hardware: await this.getHardwareFingerprint(),
                performance: await this.getPerformanceFingerprint(),
                webrtc: await this.getWebRTCFingerprint(),
                battery: await this.getBatteryFingerprint(),
                sensors: await this.getSensorsFingerprint(),
                media: await this.getMediaFingerprint(),
                storage: await this.getStorageFingerprint(),
                network: await this.getNetworkFingerprint(),
                
                // Детекция эмуляторов и ботов
                emulatorDetection: await this.detectEmulators(),
                botDetection: await this.detectBots(),
                spoofingDetection: await this.detectSpoofing(),
                
                // Поведенческие данные
                behavioral: this.getBehavioralFingerprint()
            };
            
            // Анализируем риски
            await this.analyzeRisks();
            
            // Создаем хеш из всех компонентов
            this.fingerprint = await this.createAdvancedFingerprintHash();
            
            console.log('[AdvancedDeviceFingerprint] ✅ Расширенный отпечаток создан:', this.fingerprint.substring(0, 16) + '...');
            console.log('[AdvancedDeviceFingerprint] 📊 Risk Score:', this.riskScore);
            
            return {
                fingerprint: this.fingerprint,
                components: this.components,
                riskScore: this.riskScore,
                suspiciousIndicators: this.suspiciousIndicators,
                timestamp: Date.now(),
                isHighRisk: this.riskScore >= 70,
                shouldBlock: this.riskScore >= 85
            };
            
        } catch (error) {
            console.error('[AdvancedDeviceFingerprint] ❌ Ошибка создания расширенного отпечатка:', error);
            return this.createFallbackFingerprint();
        }
    }
    
    /**
     * Расширенное hardware fingerprinting
     */
    async getHardwareFingerprint() {
        try {
            const hardware = {
                // CPU информация
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                deviceMemory: navigator.deviceMemory || 'unknown',
                
                // GPU информация (через WebGL)
                gpu: await this.getGPUInfo(),
                
                // Сенсоры и возможности устройства
                maxTouchPoints: navigator.maxTouchPoints || 0,
                vibration: 'vibrate' in navigator,
                gamepad: 'getGamepads' in navigator,
                
                // Медиа возможности
                mediaDevices: 'mediaDevices' in navigator,
                getUserMedia: 'getUserMedia' in navigator,
                
                // Сетевые возможности
                connection: this.getConnectionInfo(),
                
                // Хранилище
                storageQuota: await this.getStorageQuota()
            };
            
            return hardware;
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Hardware fingerprint failed:', error);
            return 'hardware_error';
        }
    }
    
    /**
     * Performance fingerprinting - анализ производительности
     */
    async getPerformanceFingerprint() {
        try {
            const startTime = performance.now();
            
            // Тест производительности JavaScript
            let iterations = 0;
            const testDuration = 10; // 10ms
            const endTime = startTime + testDuration;
            
            while (performance.now() < endTime) {
                Math.random();
                iterations++;
            }
            
            const jsPerformance = iterations / testDuration;
            
            // Тест производительности Canvas
            const canvasPerf = await this.testCanvasPerformance();
            
            // Тест производительности WebGL
            const webglPerf = await this.testWebGLPerformance();
            
            return {
                jsPerformance: Math.round(jsPerformance),
                canvasPerformance: canvasPerf,
                webglPerformance: webglPerf,
                timing: this.getTimingInfo(),
                memory: this.getMemoryInfo()
            };
            
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Performance fingerprint failed:', error);
            return 'performance_error';
        }
    }
    
    /**
     * WebRTC fingerprinting - получение локальных IP адресов
     */
    async getWebRTCFingerprint() {
        return new Promise((resolve) => {
            try {
                const rtcConfig = { iceServers: [{ urls: 'stun:stun.l.google.com:19302' }] };
                const pc = new RTCPeerConnection(rtcConfig);
                const ips = [];
                
                pc.createDataChannel('');
                
                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        const candidate = event.candidate.candidate;
                        const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
                        if (ipMatch && !ips.includes(ipMatch[1])) {
                            ips.push(ipMatch[1]);
                        }
                    }
                };
                
                pc.createOffer()
                    .then(offer => pc.setLocalDescription(offer))
                    .catch(() => {});
                
                setTimeout(() => {
                    pc.close();
                    resolve({
                        localIPs: ips,
                        candidatesCount: ips.length,
                        webrtcSupport: true
                    });
                }, 1000);
                
            } catch (error) {
                console.warn('[AdvancedDeviceFingerprint] WebRTC fingerprint failed:', error);
                resolve('webrtc_unavailable');
            }
        });
    }
    
    /**
     * Battery API fingerprinting
     */
    async getBatteryFingerprint() {
        try {
            if ('getBattery' in navigator) {
                const battery = await navigator.getBattery();
                return {
                    charging: battery.charging,
                    level: Math.round(battery.level * 100),
                    chargingTime: battery.chargingTime,
                    dischargingTime: battery.dischargingTime
                };
            }
            return 'battery_unavailable';
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Battery fingerprint failed:', error);
            return 'battery_error';
        }
    }
    
    /**
     * Sensors fingerprinting
     */
    async getSensorsFingerprint() {
        try {
            const sensors = {
                accelerometer: 'Accelerometer' in window,
                gyroscope: 'Gyroscope' in window,
                magnetometer: 'Magnetometer' in window,
                ambientLight: 'AmbientLightSensor' in window,
                proximity: 'ProximitySensor' in window,
                deviceOrientation: 'DeviceOrientationEvent' in window,
                deviceMotion: 'DeviceMotionEvent' in window
            };
            
            // Пытаемся получить данные с сенсоров (если доступны)
            if (sensors.deviceOrientation) {
                sensors.orientationData = await this.getOrientationData();
            }
            
            return sensors;
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Sensors fingerprint failed:', error);
            return 'sensors_error';
        }
    }
    
    /**
     * Media devices fingerprinting
     */
    async getMediaFingerprint() {
        try {
            if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
                return 'media_unavailable';
            }
            
            const devices = await navigator.mediaDevices.enumerateDevices();
            
            const mediaInfo = {
                audioInputs: devices.filter(d => d.kind === 'audioinput').length,
                audioOutputs: devices.filter(d => d.kind === 'audiooutput').length,
                videoInputs: devices.filter(d => d.kind === 'videoinput').length,
                deviceIds: devices.map(d => d.deviceId.substring(0, 8)).join(','),
                labels: devices.map(d => d.label || 'unknown').join(',')
            };
            
            return mediaInfo;
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Media fingerprint failed:', error);
            return 'media_error';
        }
    }
    
    /**
     * Storage fingerprinting
     */
    async getStorageFingerprint() {
        try {
            const storage = {
                localStorage: !!window.localStorage,
                sessionStorage: !!window.sessionStorage,
                indexedDB: !!window.indexedDB,
                webSQL: !!window.openDatabase,
                cookies: navigator.cookieEnabled
            };
            
            // Тестируем доступное место в хранилище
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                storage.quota = estimate.quota;
                storage.usage = estimate.usage;
            }
            
            return storage;
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Storage fingerprint failed:', error);
            return 'storage_error';
        }
    }
    
    /**
     * Network fingerprinting
     */
    async getNetworkFingerprint() {
        try {
            const network = {
                onLine: navigator.onLine,
                connection: this.getConnectionInfo(),
                effectiveType: navigator.connection?.effectiveType || 'unknown',
                downlink: navigator.connection?.downlink || 'unknown',
                rtt: navigator.connection?.rtt || 'unknown'
            };
            
            return network;
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Network fingerprint failed:', error);
            return 'network_error';
        }
    }
    
    /**
     * Детекция эмуляторов
     */
    async detectEmulators() {
        const indicators = [];
        let riskPoints = 0;
        
        try {
            // Проверка на Selenium
            if (window.navigator.webdriver) {
                indicators.push('selenium_webdriver_detected');
                riskPoints += 30;
            }
            
            // Проверка на PhantomJS
            if (window.callPhantom || window._phantom) {
                indicators.push('phantomjs_detected');
                riskPoints += 30;
            }
            
            // Проверка на Headless Chrome
            if (navigator.userAgent.includes('HeadlessChrome')) {
                indicators.push('headless_chrome_detected');
                riskPoints += 25;
            }
            
            // Проверка на отсутствие плагинов (характерно для ботов)
            if (navigator.plugins.length === 0) {
                indicators.push('no_plugins_detected');
                riskPoints += 15;
            }
            
            // Проверка на подозрительные размеры экрана
            if (screen.width === 1024 && screen.height === 768) {
                indicators.push('suspicious_screen_size');
                riskPoints += 10;
            }
            
            // Проверка на отсутствие WebGL
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl');
            if (!gl) {
                indicators.push('no_webgl_support');
                riskPoints += 15;
            }
            
            return {
                indicators,
                riskPoints,
                isEmulator: riskPoints >= 25
            };
            
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Emulator detection failed:', error);
            return { indicators: ['detection_error'], riskPoints: 0, isEmulator: false };
        }
    }
    
    /**
     * Детекция ботов
     */
    async detectBots() {
        const indicators = [];
        let riskPoints = 0;
        
        try {
            // Проверка времени загрузки (боты загружаются слишком быстро)
            if (performance.timing.loadEventEnd - performance.timing.navigationStart < 100) {
                indicators.push('too_fast_loading');
                riskPoints += 20;
            }
            
            // Проверка на отсутствие событий мыши
            if (!this.behavioralData.mouseEvents || this.behavioralData.mouseEvents.length === 0) {
                indicators.push('no_mouse_events');
                riskPoints += 15;
            }
            
            // Проверка на подозрительные User-Agent
            const suspiciousUA = ['bot', 'crawler', 'spider', 'scraper'];
            if (suspiciousUA.some(term => navigator.userAgent.toLowerCase().includes(term))) {
                indicators.push('suspicious_user_agent');
                riskPoints += 25;
            }
            
            // Проверка на отсутствие языков
            if (!navigator.languages || navigator.languages.length === 0) {
                indicators.push('no_languages');
                riskPoints += 10;
            }
            
            return {
                indicators,
                riskPoints,
                isBot: riskPoints >= 20
            };
            
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Bot detection failed:', error);
            return { indicators: ['detection_error'], riskPoints: 0, isBot: false };
        }
    }
    
    /**
     * Детекция spoofing атак
     */
    async detectSpoofing() {
        const indicators = [];
        let riskPoints = 0;
        
        try {
            // Проверка консистентности данных экрана
            if (screen.width !== window.screen.width || screen.height !== window.screen.height) {
                indicators.push('screen_inconsistency');
                riskPoints += 15;
            }
            
            // Проверка консистентности timezone
            const jsTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const offsetTimezone = new Date().getTimezoneOffset();
            // Дополнительные проверки timezone consistency можно добавить
            
            // Проверка на подделку Canvas
            const canvas1 = await this.getCanvasFingerprint();
            const canvas2 = await this.getCanvasFingerprint();
            if (canvas1 !== canvas2) {
                indicators.push('canvas_spoofing_detected');
                riskPoints += 25;
            }
            
            return {
                indicators,
                riskPoints,
                isSpoofed: riskPoints >= 20
            };
            
        } catch (error) {
            console.warn('[AdvancedDeviceFingerprint] Spoofing detection failed:', error);
            return { indicators: ['detection_error'], riskPoints: 0, isSpoofed: false };
        }
    }
    
    /**
     * Начинает отслеживание поведенческих данных
     */
    startBehavioralTracking() {
        this.behavioralData = {
            mouseEvents: [],
            keyboardEvents: [],
            touchEvents: [],
            scrollEvents: [],
            startTime: Date.now()
        };
        
        // Отслеживаем события мыши
        document.addEventListener('mousemove', (e) => {
            this.behavioralData.mouseEvents.push({
                x: e.clientX,
                y: e.clientY,
                timestamp: Date.now()
            });
            
            // Ограничиваем количество событий
            if (this.behavioralData.mouseEvents.length > 100) {
                this.behavioralData.mouseEvents.shift();
            }
        });
        
        // Отслеживаем события клавиатуры
        document.addEventListener('keydown', () => {
            this.behavioralData.keyboardEvents.push(Date.now());
            
            if (this.behavioralData.keyboardEvents.length > 50) {
                this.behavioralData.keyboardEvents.shift();
            }
        });
        
        // Отслеживаем события прокрутки
        window.addEventListener('scroll', () => {
            this.behavioralData.scrollEvents.push({
                scrollY: window.scrollY,
                timestamp: Date.now()
            });
            
            if (this.behavioralData.scrollEvents.length > 50) {
                this.behavioralData.scrollEvents.shift();
            }
        });
    }
    
    /**
     * Получает поведенческий отпечаток
     */
    getBehavioralFingerprint() {
        const sessionDuration = Date.now() - this.behavioralData.startTime;
        
        return {
            sessionDuration,
            mouseEventsCount: this.behavioralData.mouseEvents.length,
            keyboardEventsCount: this.behavioralData.keyboardEvents.length,
            scrollEventsCount: this.behavioralData.scrollEvents.length,
            mousePattern: this.analyzeMousePattern(),
            typingPattern: this.analyzeTypingPattern()
        };
    }
    
    /**
     * Анализирует паттерн движения мыши
     */
    analyzeMousePattern() {
        if (this.behavioralData.mouseEvents.length < 2) {
            return 'insufficient_data';
        }

        const events = this.behavioralData.mouseEvents;
        let totalDistance = 0;
        let totalTime = 0;

        for (let i = 1; i < events.length; i++) {
            const prev = events[i - 1];
            const curr = events[i];

            const distance = Math.sqrt(
                Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
            );

            totalDistance += distance;
            totalTime += curr.timestamp - prev.timestamp;
        }

        return {
            averageSpeed: totalTime > 0 ? totalDistance / totalTime : 0,
            totalDistance,
            totalTime,
            eventsCount: events.length
        };
    }

    /**
     * Анализирует паттерн печати
     */
    analyzeTypingPattern() {
        if (this.behavioralData.keyboardEvents.length < 2) {
            return 'insufficient_data';
        }

        const events = this.behavioralData.keyboardEvents;
        const intervals = [];

        for (let i = 1; i < events.length; i++) {
            intervals.push(events[i] - events[i - 1]);
        }

        const averageInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;

        return {
            averageInterval,
            eventsCount: events.length,
            intervals: intervals.slice(0, 10) // Первые 10 интервалов
        };
    }

    /**
     * Получает информацию о GPU
     */
    async getGPUInfo() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) return 'gpu_unavailable';

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');

            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown',
                unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown'
            };
        } catch (error) {
            return 'gpu_error';
        }
    }

    /**
     * Получает информацию о соединении
     */
    getConnectionInfo() {
        if (!navigator.connection) return 'connection_unavailable';

        return {
            effectiveType: navigator.connection.effectiveType || 'unknown',
            downlink: navigator.connection.downlink || 'unknown',
            rtt: navigator.connection.rtt || 'unknown',
            saveData: navigator.connection.saveData || false
        };
    }

    /**
     * Получает квоту хранилища
     */
    async getStorageQuota() {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                return {
                    quota: estimate.quota,
                    usage: estimate.usage,
                    usagePercentage: estimate.quota ? (estimate.usage / estimate.quota * 100).toFixed(2) : 'unknown'
                };
            }
            return 'storage_quota_unavailable';
        } catch (error) {
            return 'storage_quota_error';
        }
    }

    /**
     * Тестирует производительность Canvas
     */
    async testCanvasPerformance() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 100;
            canvas.height = 100;

            const startTime = performance.now();

            // Рендерим много прямоугольников
            for (let i = 0; i < 1000; i++) {
                ctx.fillStyle = `rgb(${i % 255}, ${(i * 2) % 255}, ${(i * 3) % 255})`;
                ctx.fillRect(i % 100, (i * 2) % 100, 10, 10);
            }

            const endTime = performance.now();
            return Math.round(endTime - startTime);
        } catch (error) {
            return 'canvas_perf_error';
        }
    }

    /**
     * Тестирует производительность WebGL
     */
    async testWebGLPerformance() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl');

            if (!gl) return 'webgl_unavailable';

            const startTime = performance.now();

            // Простой тест производительности WebGL
            const buffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, buffer);

            const vertices = new Float32Array(3000); // 1000 треугольников
            for (let i = 0; i < vertices.length; i++) {
                vertices[i] = Math.random() * 2 - 1;
            }

            gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

            const endTime = performance.now();
            return Math.round(endTime - startTime);
        } catch (error) {
            return 'webgl_perf_error';
        }
    }

    /**
     * Получает информацию о timing
     */
    getTimingInfo() {
        if (!performance.timing) return 'timing_unavailable';

        const timing = performance.timing;
        return {
            navigationStart: timing.navigationStart,
            loadEventEnd: timing.loadEventEnd,
            domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
            loadComplete: timing.loadEventEnd - timing.navigationStart
        };
    }

    /**
     * Получает информацию о памяти
     */
    getMemoryInfo() {
        if (!performance.memory) return 'memory_unavailable';

        return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        };
    }

    /**
     * Получает данные ориентации устройства
     */
    async getOrientationData() {
        return new Promise((resolve) => {
            if (!window.DeviceOrientationEvent) {
                resolve('orientation_unavailable');
                return;
            }

            let orientationData = null;

            const handler = (event) => {
                orientationData = {
                    alpha: event.alpha,
                    beta: event.beta,
                    gamma: event.gamma,
                    absolute: event.absolute
                };
            };

            window.addEventListener('deviceorientation', handler);

            setTimeout(() => {
                window.removeEventListener('deviceorientation', handler);
                resolve(orientationData || 'orientation_timeout');
            }, 1000);
        });
    }

    /**
     * Анализирует риски на основе всех собранных данных
     */
    async analyzeRisks() {
        this.riskScore = 0;
        this.suspiciousIndicators = [];

        // Анализируем результаты детекции эмуляторов
        if (this.components.emulatorDetection?.isEmulator) {
            this.riskScore += this.components.emulatorDetection.riskPoints;
            this.suspiciousIndicators.push(...this.components.emulatorDetection.indicators);
        }

        // Анализируем результаты детекции ботов
        if (this.components.botDetection?.isBot) {
            this.riskScore += this.components.botDetection.riskPoints;
            this.suspiciousIndicators.push(...this.components.botDetection.indicators);
        }

        // Анализируем результаты детекции spoofing
        if (this.components.spoofingDetection?.isSpoofed) {
            this.riskScore += this.components.spoofingDetection.riskPoints;
            this.suspiciousIndicators.push(...this.components.spoofingDetection.indicators);
        }

        // Дополнительные проверки
        await this.performAdditionalRiskChecks();

        console.log(`[AdvancedDeviceFingerprint] 📊 Risk analysis complete. Score: ${this.riskScore}`);
        if (this.suspiciousIndicators.length > 0) {
            console.log(`[AdvancedDeviceFingerprint] ⚠️ Suspicious indicators:`, this.suspiciousIndicators);
        }
    }

    /**
     * Выполняет дополнительные проверки рисков
     */
    async performAdditionalRiskChecks() {
        // Проверка на отсутствие поведенческих данных
        const behavioral = this.getBehavioralFingerprint();
        if (behavioral.mouseEventsCount === 0 && behavioral.sessionDuration > 5000) {
            this.riskScore += 20;
            this.suspiciousIndicators.push('no_mouse_activity');
        }

        // Проверка на подозрительную производительность
        if (this.components.performance?.jsPerformance > 10000) {
            this.riskScore += 15;
            this.suspiciousIndicators.push('suspicious_performance');
        }

        // Проверка на отсутствие WebRTC
        if (this.components.webrtc === 'webrtc_unavailable') {
            this.riskScore += 10;
            this.suspiciousIndicators.push('no_webrtc_support');
        }
    }

    /**
     * Создает расширенный хеш отпечатка
     */
    async createAdvancedFingerprintHash() {
        // Исключаем поведенческие данные из хеша (они изменяются)
        const hashData = { ...this.components };
        delete hashData.behavioral;

        const dataString = JSON.stringify(hashData);

        if (window.crypto && window.crypto.subtle) {
            try {
                const encoder = new TextEncoder();
                const data = encoder.encode(dataString);
                const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            } catch (error) {
                console.warn('[AdvancedDeviceFingerprint] Crypto API failed, using fallback hash');
            }
        }

        return this.simpleHash(dataString);
    }

    /**
     * Простая hash функция для fallback
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }

    /**
     * Базовые методы fingerprinting (наследуем от оригинального класса)
     */
    async getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = 280;
            canvas.height = 60;

            ctx.textBaseline = 'top';
            ctx.font = '14px Arial, sans-serif';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);

            ctx.fillStyle = '#069';
            ctx.fillText('UniQPaid Security 🔒 Advanced', 2, 15);

            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Advanced fingerprint test 456', 4, 35);

            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();

            return canvas.toDataURL();
        } catch (error) {
            return 'canvas_unavailable';
        }
    }

    async getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            if (!gl) return 'webgl_unavailable';

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');

            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown',
                unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown',
                extensions: gl.getSupportedExtensions()?.join(',') || '',
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)?.join(',') || ''
            };
        } catch (error) {
            return 'webgl_error';
        }
    }

    async getAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();
                const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

                oscillator.type = 'triangle';
                oscillator.frequency.value = 1000;
                gainNode.gain.value = 0;

                oscillator.connect(analyser);
                analyser.connect(scriptProcessor);
                scriptProcessor.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.start();

                let audioData = [];

                scriptProcessor.onaudioprocess = function(event) {
                    const inputBuffer = event.inputBuffer.getChannelData(0);

                    for (let i = 0; i < inputBuffer.length; i++) {
                        audioData.push(inputBuffer[i]);
                    }

                    if (audioData.length >= 1000) {
                        oscillator.stop();
                        audioContext.close();
                        resolve(audioData.slice(0, 100).join(','));
                    }
                };

                setTimeout(() => {
                    try {
                        oscillator.stop();
                        audioContext.close();
                    } catch (e) {}
                    resolve('audio_timeout');
                }, 500);

            } catch (error) {
                resolve('audio_unavailable');
            }
        });
    }

    getScreenFingerprint() {
        return {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            orientation: screen.orientation?.type || 'unknown',
            devicePixelRatio: window.devicePixelRatio || 1,
            innerWidth: window.innerWidth,
            innerHeight: window.innerHeight,
            outerWidth: window.outerWidth,
            outerHeight: window.outerHeight
        };
    }

    getSystemFingerprint() {
        return {
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages?.join(',') || '',
            hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
            deviceMemory: navigator.deviceMemory || 'unknown',
            maxTouchPoints: navigator.maxTouchPoints || 0,
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack || 'unknown',
            onLine: navigator.onLine
        };
    }

    getNavigatorFingerprint() {
        const plugins = [];
        for (let i = 0; i < navigator.plugins.length; i++) {
            plugins.push(navigator.plugins[i].name);
        }

        return {
            plugins: plugins.join(','),
            mimeTypes: Array.from(navigator.mimeTypes).map(mt => mt.type).join(','),
            vendor: navigator.vendor || '',
            product: navigator.product || '',
            productSub: navigator.productSub || '',
            appName: navigator.appName || '',
            appVersion: navigator.appVersion || ''
        };
    }

    getTimezoneFingerprint() {
        return {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset(),
            locale: Intl.DateTimeFormat().resolvedOptions().locale,
            calendar: Intl.DateTimeFormat().resolvedOptions().calendar || 'unknown',
            numberingSystem: Intl.DateTimeFormat().resolvedOptions().numberingSystem || 'unknown'
        };
    }

    async getFontsFingerprint() {
        const testFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Tahoma', 'Calibri'
        ];

        const availableFonts = [];

        for (const font of testFonts) {
            if (await this.isFontAvailable(font)) {
                availableFonts.push(font);
            }
        }

        return availableFonts.join(',');
    }

    async isFontAvailable(fontName) {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            const testText = 'mmmmmmmmmmlli';
            const testSize = '72px';
            const fallbackFont = 'monospace';

            ctx.font = `${testSize} ${fallbackFont}`;
            const fallbackWidth = ctx.measureText(testText).width;

            ctx.font = `${testSize} ${fontName}, ${fallbackFont}`;
            const testWidth = ctx.measureText(testText).width;

            return testWidth !== fallbackWidth;
        } catch (error) {
            return false;
        }
    }

    /**
     * Fallback отпечаток для случаев ошибок
     */
    createFallbackFingerprint() {
        const basicData = {
            userAgent: navigator.userAgent,
            screen: `${screen.width}x${screen.height}`,
            timezone: new Date().getTimezoneOffset(),
            language: navigator.language,
            platform: navigator.platform,
            timestamp: Date.now()
        };

        return {
            fingerprint: this.simpleHash(JSON.stringify(basicData)),
            components: basicData,
            riskScore: 50, // Средний риск для fallback
            suspiciousIndicators: ['fallback_fingerprint'],
            timestamp: Date.now(),
            fallback: true,
            isHighRisk: true,
            shouldBlock: false
        };
    }
}

// Экспортируем класс
window.AdvancedDeviceFingerprint = AdvancedDeviceFingerprint;

console.log('✅ [AdvancedDeviceFingerprint] Расширенный модуль детекции устройств загружен');
