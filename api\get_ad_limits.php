<?php
/**
 * api/get_ad_limits.php
 * API для получения лимитов показов рекламы
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

/**
 * Функция для получения актуальных значений из config.php
 */
if (!function_exists('getActualConfigValue')) {
    function getActualConfigValue($constantName, $defaultValue = null) {
        static $configContent = null;
        
        // Читаем файл только один раз за запрос
        if ($configContent === null) {
            $configFile = __DIR__ . '/config.php';
            $configContent = file_get_contents($configFile);
        }
        
        // Ищем определение константы в файле
        $patterns = [
            "/define\('$constantName',\s*([^)]+)\);/", // Обычное определение
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $configContent, $matches)) {
                $value = trim($matches[1], " '\"");
                
                // Обрабатываем разные типы значений
                if ($value === 'true') return true;
                if ($value === 'false') return false;
                if (is_numeric($value)) return floatval($value);
                return $value;
            }
        }
        
        return $defaultValue;
    }
}

try {
    // Получаем пользовательские лимиты из актуального файла config.php
    $nativeBannerLimit = getActualConfigValue('USER_AD_LIMIT_NATIVE_BANNER', 20);
    $interstitialLimit = getActualConfigValue('USER_AD_LIMIT_INTERSTITIAL', 20);
    $rewardedVideoLimit = getActualConfigValue('USER_AD_LIMIT_REWARDED_VIDEO', 20);

    $limits = [
        'native_banner' => $nativeBannerLimit,
        'rewarded_video' => $rewardedVideoLimit,
        'interstitial' => $interstitialLimit,
        'daily_limit_per_type' => $nativeBannerLimit + $interstitialLimit + $rewardedVideoLimit // Сумма всех лимитов для пользователя
    ];
    
    // Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'limits' => $limits,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log('Ошибка в get_ad_limits.php: ' . $e->getMessage());
    
    // Возвращаем ошибку с fallback значениями из config.php
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения лимитов показов',
        'limits' => [
            'native_banner' => getActualConfigValue('AD_LIMIT_NATIVE_BANNER', 20),
            'rewarded_video' => getActualConfigValue('AD_LIMIT_REWARDED_VIDEO', 20),
            'interstitial' => getActualConfigValue('AD_LIMIT_INTERSTITIAL', 20),
            'daily_limit_per_type' => 20
        ]
    ]);
}
?>
