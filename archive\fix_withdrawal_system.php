<?php
/**
 * fix_withdrawal_system.php
 * Комплексное исправление системы выплат
 */

require_once __DIR__ . '/api/config.php';
require_once __DIR__ . '/api/db_mock.php';
require_once __DIR__ . '/api/NOWPaymentsAPI.php';

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Загружаем данные пользователей
$userData = loadUserData();

$fixedCount = 0;
$balanceFixed = 0;

foreach ($userData as $userId => &$user) {
    if (empty($user['withdrawals']) || !is_array($user['withdrawals'])) continue;

    foreach ($user['withdrawals'] as &$withdrawal) {
        // Пропускаем уже подтвержденные выплаты
        if (($withdrawal['status'] ?? '') === 'confirmed') continue;

        $payoutId = $withdrawal['payout_id'] ?? null;
        if (!$payoutId) continue;

        try {
            // Получаем актуальный статус выплаты
            $payoutStatus = $api->getPayoutStatus($payoutId);
            
            if (!isset($payoutStatus['payout_status'])) continue;
            
            $status = strtolower($payoutStatus['payout_status']);
            $normalizedStatus = [
                'waiting' => 'pending',
                'confirming' => 'confirming',
                'sending' => 'sending',
                'finished' => 'confirmed',
                'failed' => 'failed',
                'refunded' => 'refunded',
                'expired' => 'expired'
            ][$status] ?? $status;

            // Если статус изменился
            if ($normalizedStatus !== ($withdrawal['status'] ?? '')) {
                $oldStatus = $withdrawal['status'] ?? '';
                $withdrawal['status'] = $normalizedStatus;
                $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                
                // Сохраняем дополнительные данные
                if (isset($payoutStatus['txid'])) $withdrawal['txid'] = $payoutStatus['txid'];
                if (isset($payoutStatus['payout_amount'])) $withdrawal['crypto_amount'] = $payoutStatus['payout_amount'];
                
                // Корректируем баланс
                $amount = $withdrawal['coins_amount'] ?? 0;
                
                if ($normalizedStatus === 'confirmed' && $oldStatus !== 'confirmed') {
                    // Списание баланса
                    $user['balance'] = ($user['balance'] ?? 0) - $amount;
                    $balanceFixed -= $amount;
                } elseif (in_array($normalizedStatus, ['failed', 'refunded', 'expired']) && 
                         !in_array($oldStatus, ['failed', 'refunded', 'expired'])) {
                    // Возврат средств
                    $user['balance'] = ($user['balance'] ?? 0) + $amount;
                    $balanceFixed += $amount;
                }
                
                $fixedCount++;
                echo "Исправлена выплата $payoutId: $oldStatus => $normalizedStatus\n";
            }
        } catch (Exception $e) {
            echo "Ошибка при обработке выплаты $payoutId: " . $e->getMessage() . "\n";
        }
    }
}

// Сохраняем исправленные данные
if (saveUserData($userData)) {
    echo "✅ Система выплат исправлена!\n";
    echo "Исправлено выплат: $fixedCount\n";
    echo "Скорректировано балансов: $balanceFixed монет\n";
} else {
    echo "❌ Ошибка сохранения данных\n";
}
?>