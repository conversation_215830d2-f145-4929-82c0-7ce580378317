<?php
/**
 * update_perfect_banner.php
 * Обновление идеального баннера бота
 */

require_once __DIR__ . '/config.php';

// Получаем chat_id из параметра URL
$chatId = $_GET['chat_id'] ?? null;

if (!$chatId) {
    echo "❌ Ошибка: Не указан chat_id\n";
    echo "Использование: update_perfect_banner.php?chat_id=YOUR_CHAT_ID\n";
    exit;
}

// URL нового идеального баннера с timestamp для обхода кэша
$logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_perfect_banner.png?' . time();

// Сообщение для теста
$message = "🌈 <b>ИДЕАЛЬНЫЙ БАННЕР ГОТОВ!</b>\n\n";
$message .= "🌞 Яркий жёлтый фон\n";
$message .= "💛 Жёлтые криптовалюты: TON, USDT, BTC, ETH\n";
$message .= "🌸 Розовые дизайнерские элементы\n";
$message .= "🍃 Салатовые декоративные акценты\n";
$message .= "🏆 Название UniQPaid крупно\n";
$message .= "🚫 Никаких размытий - только четкие контуры\n";
$message .= "⏰ Время обновления: " . date('Y-m-d H:i:s');

// Клавиатура
$keyboard = [
    'inline_keyboard' => [
        [
            [
                'text' => '🌈 Идеальный баннер!',
                'callback_data' => 'perfect_banner_ready'
            ]
        ],
        [
            [
                'text' => '🚀 Запустить приложение',
                'web_app' => ['url' => WEBAPP_URL]
            ]
        ],
        [
            [
                'text' => '💰 Мой баланс',
                'callback_data' => 'my_balance'
            ],
            [
                'text' => '👥 Друзья',
                'callback_data' => 'invite_friends'
            ]
        ]
    ]
];

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Идеальный баннер</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:linear-gradient(135deg,#FFF59D,#FFEB3B);color:#1a1a1a;} 
.success{color:#1a1a1a;background:#CCFF90;padding:15px;border-radius:8px;border:3px solid #8BC34A;} 
.error{color:#fff;background:#E91E63;padding:15px;border-radius:8px;border:3px solid #AD1457;} 
.info{color:#1a1a1a;font-weight:bold;} 
.banner{background:linear-gradient(135deg,#FF80AB,#E91E63);color:#fff;padding:20px;border-radius:12px;margin:20px 0;text-align:center;font-weight:bold;}
code{background:#fff;padding:2px 6px;border-radius:4px;color:#1a1a1a;border:1px solid #ccc;}
a{color:#1a1a1a;font-weight:bold;}
h1{color:#1a1a1a;text-shadow:2px 2px 0px #fff;}
</style>";
echo "</head><body>\n";

echo "<div class='banner'>\n";
echo "<h1>🌈 Идеальный баннер Telegram бота UniQPaid</h1>\n";
echo "</div>\n";

echo "<p>📤 Отправляем новый идеальный баннер...</p>\n";
echo "<p><strong>URL:</strong> <code>{$logoUrl}</code></p>\n";
echo "<p><strong>Chat ID:</strong> <code>{$chatId}</code></p>\n";

// Логируем попытку
botLog("INFO: Попытка обновления идеального баннера для chat_id: {$chatId}");
botLog("INFO: URL картинки: {$logoUrl}");

// Отправляем картинку
$result = sendPhoto($chatId, $logoUrl, $message, $keyboard);

if ($result) {
    echo "<div class='success'>\n";
    echo "<p>🎉 <strong>Успех!</strong> Идеальный баннер успешно отправлен!</p>\n";
    echo "</div>\n";
    
    // Получаем file_id для будущего использования
    if (isset($result['photo']) && is_array($result['photo'])) {
        $fileId = end($result['photo'])['file_id']; // Берем самое большое разрешение
        echo "<p class='info'>🆔 File ID: <code>{$fileId}</code></p>\n";
        echo "<p class='info'>💡 Этот file_id можно использовать для быстрой отправки без URL</p>\n";
        botLog("SUCCESS: Идеальный баннер отправлен. File ID: {$fileId}");
    }
    
    echo "<p class='success'>✅ Теперь можете протестировать бота командой /start</p>\n";
    echo "<p class='success'>✅ Новый идеальный баннер должен появиться в приветственном сообщении!</p>\n";
    
} else {
    echo "<div class='error'>\n";
    echo "<p>❌ <strong>Ошибка!</strong> Не удалось отправить идеальный баннер.</p>\n";
    echo "</div>\n";
    
    echo "<p class='info'>💡 Возможные причины:</p>\n";
    echo "<ul>\n";
    echo "<li>Неверный chat_id</li>\n";
    echo "<li>Картинка недоступна по URL</li>\n";
    echo "<li>Проблемы с Telegram API</li>\n";
    echo "<li>Бот заблокирован пользователем</li>\n";
    echo "</ul>\n";
    
    botLog("ERROR: Не удалось отправить идеальный баннер для chat_id: {$chatId}");
}

// Дополнительная информация
echo "<hr style='border-color:#8BC34A;'>\n";
echo "<h3 class='info'>📋 Информация об идеальном баннере</h3>\n";

echo "<p><strong>Файл:</strong> bot_welcome_perfect_banner.png</p>\n";
echo "<p><strong>SVG исходник:</strong> bot_welcome_perfect_banner.svg</p>\n";
echo "<p><strong>Расположение:</strong> images/bot_welcome_perfect_banner.png</p>\n";
echo "<p><strong>Прямая ссылка:</strong> <a href='https://app.uniqpaid.com/test3/images/bot_welcome_perfect_banner.png' target='_blank'>Открыть картинку</a></p>\n";

echo "<h3 class='info'>🎨 Особенности идеального дизайна</h3>\n";
echo "<ul>\n";
echo "<li>🌞 Яркий жёлтый фон - никакой мрачности!</li>\n";
echo "<li>💛 Жёлтые криптовалюты с иконками</li>\n";
echo "<li>🌸 Розовые дизайнерские элементы</li>\n";
echo "<li>🍃 Салатовые декоративные акценты</li>\n";
echo "<li>🏆 UniQPaid - крупное название</li>\n";
echo "<li>🚫 Никаких размытий - четкие контуры</li>\n";
echo "<li>✨ Идеальная читаемость текста</li>\n";
echo "</ul>\n";

echo "<h3 class='info'>🔧 Следующие шаги</h3>\n";
echo "<ol>\n";
echo "<li>Конвертируйте SVG в PNG (размер 800x400 или больше)</li>\n";
echo "<li>Загрузите PNG файл как <code>bot_welcome_perfect_banner.png</code></li>\n";
echo "<li>Протестируйте бота командой /start</li>\n";
echo "<li>Наслаждайтесь идеальным дизайном! 🌈</li>\n";
echo "</ol>\n";

echo "</body></html>\n";
?>
