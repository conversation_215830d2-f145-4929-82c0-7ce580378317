<?php
/**
 * api/ads-config.php
 * Централизованная конфигурация рекламной системы для PHP
 * Единый источник истины для всех настроек рекламы на сервере
 */

/**
 * Функция для получения актуальных значений из config.php
 */
if (!function_exists('getActualConfigValue')) {
    function getActualConfigValue($constantName, $defaultValue = null) {
        static $configContent = null;

        // Читаем файл только один раз за запрос
        if ($configContent === null) {
            $configFile = __DIR__ . '/config.php';
            $configContent = file_get_contents($configFile);
        }

        // Ищем определение константы в файле
        $patterns = [
            "/define\('$constantName',\s*([^)]+)\);/", // Обычное определение
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $configContent, $matches)) {
                $value = trim($matches[1], " '\"");

                // Обрабатываем разные типы значений
                if ($value === 'true') return true;
                if ($value === 'false') return false;
                if (is_numeric($value)) return floatval($value);
                return $value;
            }
        }

        return $defaultValue;
    }
}

class AdsConfig {
    // ===== ОСНОВНЫЕ НАСТРОЙКИ RICHADS =====
    const RICHADS_PUB_ID = "944840";
    const RICHADS_APP_ID = "2122";
    const RICHADS_DEBUG_MODE = false;
    
    // ===== ТИПЫ РЕКЛАМЫ И НАГРАДЫ =====
    // Награды теперь загружаются динамически из config.php
    public static function getAdTypes() {
        return [
            'native_banner' => [
                'id' => 'native_banner',
                'reward' => getActualConfigValue('AD_REWARD_NATIVE_BANNER', 10),
                'button_id' => 'openLinkButton',
                'counter_id' => 'native-banner-counter',
                'method' => 'triggerInterstitialBanner',
                'params' => true, // С автопереходом (прямой клик)
                'has_fallback' => false, // БЕЗ fallback
                'title' => [
                    'ru' => 'Открыть ссылку',
                    'en' => 'Open Link'
                ]
            ],
            'rewarded_video' => [
                'id' => 'rewarded_video',
                'reward' => getActualConfigValue('AD_REWARD_REWARDED_VIDEO', 1),
                'button_id' => 'watchVideoButton',
                'counter_id' => 'rewarded-video-counter',
                'method' => 'triggerInterstitialBanner',
                'params' => null, // обычный полноэкранный баннер (БЕЗ параметров)
                'has_fallback' => false, // БЕЗ fallback
                'title' => [
                    'ru' => 'Смотреть видео',
                    'en' => 'Watch Video'
                ]
            ],
            'interstitial' => [
                'id' => 'interstitial',
                'reward' => getActualConfigValue('AD_REWARD_INTERSTITIAL', 10),
                'button_id' => 'openAdButton',
                'counter_id' => 'interstitial-counter',
                'method' => 'triggerInterstitialBanner',
                'params' => false, // БЕЗ автоперехода (обычный баннер)
                'has_fallback' => false, // БЕЗ fallback
                'title' => [
                    'ru' => 'Открыть рекламу',
                    'en' => 'Open Ad'
                ]
            ]
        ];
    }
    
    // ===== ПОЛЬЗОВАТЕЛЬСКИЕ ЛИМИТЫ =====
    // Лимиты для отдельного пользователя в день (загружаются динамически из config.php)
    public static function getUserDailyLimitPerType() {
        // Общий лимит = сумма всех типов рекламы для одного пользователя
        $nativeBanner = getActualConfigValue('USER_AD_LIMIT_NATIVE_BANNER', 20);
        $interstitial = getActualConfigValue('USER_AD_LIMIT_INTERSTITIAL', 20);
        $rewardedVideo = getActualConfigValue('USER_AD_LIMIT_REWARDED_VIDEO', 20);
        return $nativeBanner + $interstitial + $rewardedVideo;
    }

    public static function getUserDailyLimitForAdType($adType) {
        switch ($adType) {
            case 'native_banner':
                return getActualConfigValue('USER_AD_LIMIT_NATIVE_BANNER', 20);
            case 'interstitial':
                return getActualConfigValue('USER_AD_LIMIT_INTERSTITIAL', 20);
            case 'rewarded_video':
                return getActualConfigValue('USER_AD_LIMIT_REWARDED_VIDEO', 20);
            default:
                return getActualConfigValue('USER_AD_LIMIT_NATIVE_BANNER', 20);
        }
    }

    const COOLDOWN_TIME = 20000; // 20 секунд в миллисекундах
    const MAX_RETRIES = 3;
    const TIMEOUT = 30000; // 30 секунд
    
    // ===== СТАТУСЫ ЛОГИРОВАНИЯ =====
    const LOG_STATUS = [
        'BUTTON_CLICK' => 'button_click',
        'AD_REQUEST' => 'ad_request',
        'AD_SHOWN' => 'ad_shown', 
        'AD_COMPLETED' => 'ad_completed',
        'AD_ERROR' => 'ad_error',
        'LIMIT_EXCEEDED' => 'limit_exceeded',
        'COOLDOWN_ACTIVE' => 'cooldown_active',
        'SUCCESS' => 'success',
        'ERROR' => 'error'
    ];
    
    // ===== ФАЙЛЫ ДАННЫХ =====
    const DATA_FILES = [
        'AD_LIMITS' => __DIR__ . '/../database/ad_limits.json',
        'USER_DATA' => __DIR__ . '/user_data.json',
        'AD_REQUESTS_LOG' => __DIR__ . '/ad_requests.log'
    ];
    
    // ===== МЕТОДЫ ПОЛУЧЕНИЯ КОНФИГУРАЦИИ =====
    
    /**
     * Получить конфигурацию типа рекламы
     */
    public static function getAdType($adTypeId) {
        $adTypes = self::getAdTypes();
        return $adTypes[$adTypeId] ?? null;
    }
    
    /**
     * Получить все типы рекламы
     */
    public static function getAllAdTypes() {
        return self::getAdTypes();
    }
    
    /**
     * Получить награду за тип рекламы
     */
    public static function getReward($adTypeId) {
        $adType = self::getAdType($adTypeId);
        return $adType ? $adType['reward'] : 0;
    }
    
    /**
     * Получить дневной лимит для пользователя
     */
    public static function getDailyLimit() {
        return self::getUserDailyLimitPerType();
    }
    
    /**
     * Получить время cooldown в секундах
     */
    public static function getCooldownTime() {
        return self::COOLDOWN_TIME / 1000; // конвертируем в секунды
    }
    
    /**
     * Проверить, достигнут ли лимит для пользователя
     */
    public static function isUserLimitReached($currentCount, $adType = null) {
        $limit = $adType ? self::getUserDailyLimitForAdType($adType) : self::getUserDailyLimitPerType();
        return $currentCount >= $limit;
    }
    
    /**
     * Получить путь к файлу данных
     */
    public static function getDataFile($fileKey) {
        return self::DATA_FILES[$fileKey] ?? null;
    }
    
    /**
     * Получить статус логирования
     */
    public static function getLogStatus($statusKey) {
        return self::LOG_STATUS[$statusKey] ?? 'unknown';
    }
    
    /**
     * Валидация типа рекламы
     */
    public static function isValidAdType($adTypeId) {
        return isset(self::AD_TYPES[$adTypeId]);
    }
    
    /**
     * Получить конфигурацию для JavaScript (JSON)
     */
    public static function getJsConfig() {
        return [
            'richads' => [
                'pub_id' => self::RICHADS_PUB_ID,
                'app_id' => self::RICHADS_APP_ID,
                'debug_mode' => self::RICHADS_DEBUG_MODE
            ],
            'ad_types' => self::AD_TYPES,
            'user_limits' => [
                'daily_limit_per_type' => self::getUserDailyLimitPerType(), // Сумма всех лимитов
                'native_banner' => self::getUserDailyLimitForAdType('native_banner'),
                'interstitial' => self::getUserDailyLimitForAdType('interstitial'),
                'rewarded_video' => self::getUserDailyLimitForAdType('rewarded_video'),
                'total_per_user_per_day' => self::getUserDailyLimitPerType(), // Дублируем для ясности
                'cooldown_time' => self::COOLDOWN_TIME,
                'max_retries' => self::MAX_RETRIES,
                'timeout' => self::TIMEOUT
            ],
            'log_status' => self::LOG_STATUS
        ];
    }
    
    /**
     * Экспорт констант для обратной совместимости
     */
    public static function exportLegacyConstants() {
        // Определяем константы для совместимости со старым кодом
        if (!defined('AD_REWARD_NATIVE_BANNER')) {
            define('AD_REWARD_NATIVE_BANNER', self::getReward('native_banner'));
        }
        if (!defined('AD_REWARD_INTERSTITIAL')) {
            define('AD_REWARD_INTERSTITIAL', self::getReward('interstitial'));
        }
        if (!defined('AD_REWARD_REWARDED_VIDEO')) {
            define('AD_REWARD_REWARDED_VIDEO', self::getReward('rewarded_video'));
        }
    }
    
    /**
     * Получить текст счетчика для языка
     */
    public static function getCounterText($language, $count) {
        if ($language === 'ru') {
            if ($count === 0) return 'лимит исчерпан';
            if ($count === 1) return 'остался 1 показ';
            if ($count >= 2 && $count <= 4) return "осталось {$count} показа";
            return "осталось {$count} показов";
        } else {
            return $count === 0 ? 'limit reached' : "{$count} ad views left";
        }
    }
    
    /**
     * Валидация всей конфигурации
     */
    public static function validate() {
        $errors = [];
        
        // Проверяем RichAds настройки
        if (empty(self::RICHADS_PUB_ID) || empty(self::RICHADS_APP_ID)) {
            $errors[] = 'RichAds PUB_ID и APP_ID обязательны';
        }
        
        // Проверяем типы рекламы
        $adTypes = self::getAdTypes();
        foreach ($adTypes as $typeId => $config) {
            if (empty($config['id']) || empty($config['method'])) {
                $errors[] = "Неполная конфигурация для типа рекламы: {$typeId}";
            }
            if (!is_numeric($config['reward']) || $config['reward'] < 0) {
                $errors[] = "Некорректная награда для типа: {$typeId}";
            }
        }
        
        // Проверяем пользовательские лимиты
        if (self::getUserDailyLimitPerType() <= 0) {
            $errors[] = 'Дневной лимит для пользователя должен быть больше 0';
        }

        // Проверяем индивидуальные пользовательские лимиты
        $adTypes = ['native_banner', 'interstitial', 'rewarded_video'];
        foreach ($adTypes as $adType) {
            if (self::getUserDailyLimitForAdType($adType) <= 0) {
                $errors[] = "Пользовательский лимит для типа $adType должен быть больше 0";
            }
        }
        
        if (self::COOLDOWN_TIME < 0) {
            $errors[] = 'Время cooldown не может быть отрицательным';
        }
        
        // Проверяем файлы данных
        foreach (self::DATA_FILES as $key => $path) {
            $dir = dirname($path);
            if (!is_dir($dir)) {
                $errors[] = "Директория не существует: {$dir} для {$key}";
            }
        }
        
        return [
            'is_valid' => empty($errors),
            'errors' => $errors
        ];
    }
}

// Экспорт констант для обратной совместимости
AdsConfig::exportLegacyConstants();

// Валидация при загрузке
$validation = AdsConfig::validate();
if (!$validation['is_valid']) {
    error_log('[AdsConfig] Ошибки конфигурации: ' . implode(', ', $validation['errors']));
} else {
    error_log('[AdsConfig] ✅ Конфигурация рекламы загружена и валидна');
}
?>
