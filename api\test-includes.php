<?php
/**
 * api/test-includes.php
 * Тест подключения всех зависимостей
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

$result = [
    'test' => 'includes_test',
    'timestamp' => time(),
    'files' => [],
    'functions' => [],
    'errors' => []
];

// Тест 1: Проверяем существование файлов
$files = [
    'config.php' => __DIR__ . '/config.php',
    'validate_initdata.php' => __DIR__ . '/validate_initdata.php',
    'db_mock.php' => __DIR__ . '/db_mock.php'
];

foreach ($files as $name => $path) {
    $result['files'][$name] = [
        'exists' => file_exists($path),
        'readable' => file_exists($path) ? is_readable($path) : false,
        'size' => file_exists($path) ? filesize($path) : 0
    ];
}

// Тест 2: Пытаемся подключить файлы
try {
    if (file_exists(__DIR__ . '/config.php')) {
        require_once __DIR__ . '/config.php';
        $result['config_loaded'] = true;
    } else {
        $result['errors'][] = 'config.php не найден';
    }
} catch (Exception $e) {
    $result['errors'][] = 'Ошибка загрузки config.php: ' . $e->getMessage();
}

try {
    if (file_exists(__DIR__ . '/validate_initdata.php')) {
        require_once __DIR__ . '/validate_initdata.php';
        $result['validate_loaded'] = true;
        
        if (function_exists('validateTelegramInitData')) {
            $result['functions']['validateTelegramInitData'] = true;
        } else {
            $result['errors'][] = 'Функция validateTelegramInitData не найдена';
        }
    } else {
        $result['errors'][] = 'validate_initdata.php не найден';
    }
} catch (Exception $e) {
    $result['errors'][] = 'Ошибка загрузки validate_initdata.php: ' . $e->getMessage();
}

try {
    if (file_exists(__DIR__ . '/db_mock.php')) {
        require_once __DIR__ . '/db_mock.php';
        $result['db_mock_loaded'] = true;
        
        $functions = ['loadUserData', 'saveUserData', 'getUserDetails'];
        foreach ($functions as $func) {
            if (function_exists($func)) {
                $result['functions'][$func] = true;
            } else {
                $result['errors'][] = "Функция $func не найдена";
            }
        }
    } else {
        $result['errors'][] = 'db_mock.php не найден';
    }
} catch (Exception $e) {
    $result['errors'][] = 'Ошибка загрузки db_mock.php: ' . $e->getMessage();
}

// Тест 3: Проверяем константы
if (defined('USER_DATA_FILE')) {
    $result['constants']['USER_DATA_FILE'] = USER_DATA_FILE;
    $result['constants']['USER_DATA_FILE_exists'] = file_exists(USER_DATA_FILE);
} else {
    $result['errors'][] = 'Константа USER_DATA_FILE не определена';
}

if (defined('TELEGRAM_BOT_TOKEN')) {
    $result['constants']['TELEGRAM_BOT_TOKEN'] = 'определен (' . strlen(TELEGRAM_BOT_TOKEN) . ' символов)';
} else {
    $result['errors'][] = 'Константа TELEGRAM_BOT_TOKEN не определена';
}

// Тест 4: Пытаемся вызвать loadUserData
if (function_exists('loadUserData')) {
    try {
        $userData = loadUserData();
        $result['loadUserData_test'] = [
            'success' => true,
            'type' => gettype($userData),
            'count' => is_array($userData) ? count($userData) : 'не массив'
        ];
    } catch (Exception $e) {
        $result['loadUserData_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

echo json_encode($result, JSON_PRETTY_PRINT);
?>
