<?php
/**
 * api/forceUpdateWithdrawalStatuses.php
 * Принудительное обновление статусов выплат для конкретного пользователя
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/bot_config_loader.php';
defineBotConstants();
require_once __DIR__ . '/admin/auth.php';

session_start(); // Начало сессии для auth.php

// ... (остальной код без изменений)

try {
    $isAdmin = isAuthenticated();
    $userId = null;

    if ($isAdmin) {
        // Администратор может указать ID пользователя
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);
        $userId = $input['user_id'] ?? null;

        if (!$userId) {
            http_response_code(400);
            echo json_encode(['error' => 'Для администратора требуется user_id']);
            exit;
        }
        $userId = intval($userId);

    } else {
        // Стандартная логика для пользователей
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);

        if (!isset($input['initData']) || empty($input['initData'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Отсутствуют данные аутентификации']);
            exit;
        }

        $validatedData = validateTelegramInitData($input['initData']);
        if ($validatedData === false) {
            http_response_code(401);
            echo json_encode(['error' => 'Ошибка аутентификации']);
            exit;
        }
        $userId = intval($validatedData['user']['id']);
    }
    error_log("forceUpdateWithdrawalStatuses INFO: Принудительное обновление статусов для пользователя {$userId}");

    // 3. Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        http_response_code(404);
        echo json_encode(['error' => 'Пользователь не найден']);
        exit;
    }

    $user = $userData[$userId];
    $withdrawals = $user['withdrawals'] ?? [];

    if (empty($withdrawals)) {
        echo json_encode([
            'success' => true,
            'user_id' => $userId,
            'checked' => 0,
            'updated' => 0,
            'message' => 'Нет выплат для проверки'
        ]);
        exit;
    }

    // 4. Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

    $checkedCount = 0;
    $updatedCount = 0;
    $statusChanges = [];

    // 5. Проверяем каждую выплату
    foreach ($withdrawals as $index => &$withdrawal) {
        $checkedCount++;
        $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
        
        if (!$payoutId) {
            continue;
        }

        $oldStatus = $withdrawal['status'] ?? 'unknown';
        
        // Пропускаем уже завершенные выплаты
        if (in_array($oldStatus, ['finished', 'completed', 'failed', 'cancelled', 'expired'])) {
            continue;
        }

        try {
            // Получаем актуальный статус из NOWPayments
            $payoutStatus = $api->getPayoutStatus($payoutId);
            
            if ($payoutStatus && isset($payoutStatus['status'])) {
                $newStatus = $payoutStatus['status'];
                
                if ($newStatus !== $oldStatus) {
                    $withdrawal['status'] = $newStatus;
                    $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                    
                    // Добавляем дополнительную информацию если есть
                    if (isset($payoutStatus['txid'])) {
                        $withdrawal['txid'] = $payoutStatus['txid'];
                    }
                    if (isset($payoutStatus['hash'])) {
                        $withdrawal['hash'] = $payoutStatus['hash'];
                    }
                    
                    $updatedCount++;
                    $statusChanges[] = [
                        'payout_id' => $payoutId,
                        'old_status' => $oldStatus,
                        'new_status' => $newStatus,
                        'updated_at' => $withdrawal['updated_at']
                    ];
                    
                    error_log("forceUpdateWithdrawalStatuses INFO: Обновлен статус выплаты {$payoutId}: {$oldStatus} -> {$newStatus}");
                }
            }
            
            // Небольшая пауза между запросами
            usleep(200000); // 200ms
            
        } catch (Exception $e) {
            error_log("forceUpdateWithdrawalStatuses ERROR: Ошибка проверки статуса выплаты {$payoutId}: " . $e->getMessage());
        }
    }

    // 6. Сохраняем обновленные данные если были изменения
    if ($updatedCount > 0) {
        $userData[$userId]['withdrawals'] = $withdrawals;
        if (saveUserData($userData)) {
            error_log("forceUpdateWithdrawalStatuses INFO: Сохранены обновления для пользователя {$userId}");
        } else {
            error_log("forceUpdateWithdrawalStatuses ERROR: Не удалось сохранить обновления для пользователя {$userId}");
        }
    }

    // 7. Возвращаем результат
    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'checked' => $checkedCount,
        'updated' => $updatedCount,
        'status_changes' => $statusChanges,
        'message' => $updatedCount > 0 ? 
            "Обновлено статусов: {$updatedCount} из {$checkedCount}" : 
            "Все статусы актуальны ({$checkedCount} проверено)",
        'updated_at' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("forceUpdateWithdrawalStatuses CRITICAL ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при принудительном обновлении статусов',
        'details' => $e->getMessage()
    ]);
}
?>
