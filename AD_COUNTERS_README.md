# 📊 Система счетчиков рекламы

## 🎯 Описание

Добавлена система счетчиков успешных показов рекламы для каждого типа рекламы с поддержкой локализации на русский и английский языки.

## ✨ Функциональность

### 📱 Отображение в интерфейсе

Каждая кнопка рекламы теперь показывает:
- **Основной текст** кнопки (например, "Открыть ссылку")
- **Счетчик показов** под основным текстом (например, "осталось 15 показов")

### 🌐 Локализация

**Русский язык:**
- `осталось 20 показов` (для 0, 5-20 показов)
- `осталось 2 показа` (для 2-4 показов)
- `остался 1 показ` (для 1 показа)

**Английский язык:**
- `20 ad views left` (универсальная форма)

### 🎨 Визуальное оформление

- Счетчики отображаются под основным текстом кнопки
- Увеличенный размер шрифта (14px) для лучшей читаемости
- Темный цвет текста для контраста с желтыми кнопками
- Декоративная тонкая линия между основным текстом и счетчиком
- Аккуратные отступы и улучшенная типографика
- При достижении лимита (0 показов) меняют цвет на темно-красный
- Текстовые тени для улучшения читаемости

## 🔧 Техническая реализация

### 📁 Новые файлы

1. **`js/ad-counters.js`** - Основной модуль управления счетчиками
2. **`test_ad_counters.html`** - Тестовая страница для проверки функциональности
3. **`test_daily_reset.html`** - Специальная страница для тестирования ежедневного сброса

### 📝 Изменения в существующих файлах

1. **`index.html`** - Обновлена структура кнопок рекламы
2. **`locales/ru.json`** - Добавлены переводы для счетчиков
3. **`locales/en.json`** - Добавлены переводы для счетчиков
4. **`css/cyberpunk-styles.css`** - Добавлены стили для счетчиков
5. **`js/modules-loader.js`** - Добавлен новый модуль в порядок загрузки
6. **`js/ads-manager-full.js`** - Интеграция с системой счетчиков
7. **`js/localization.js`** - Обновление счетчиков при смене языка
8. **`js/main.js`** - Инициализация счетчиков в основном приложении

### 🏗️ Архитектура

```
AdCountersManager
├── Управление лимитами (20 показов каждого типа в день)
├── Локализация текстов счетчиков
├── Обновление отображения в реальном времени
├── Интеграция с localStorage для хранения данных
└── Автоматический сброс счетчиков в новый день (UTC)
```

### 🔗 Интеграция

Модуль автоматически интегрируется с:
- **Системой локализации** - обновляет тексты при смене языка
- **Менеджером рекламы** - увеличивает счетчики при успешных просмотрах
- **Основным приложением** - инициализируется при запуске

## 🧪 Тестирование

### Локальное тестирование

1. Откройте `test_ad_counters.html` в браузере
2. Используйте кнопки для симуляции просмотров рекламы
3. Переключайте языки для проверки локализации
4. Сбрасывайте счетчики для повторного тестирования

### Функции тестирования

- **Симуляция просмотров** - увеличение счетчиков без реальной рекламы
- **Переключение языков** - проверка корректности переводов
- **Сброс счетчиков** - возврат к начальному состоянию
- **Просмотр лимитов** - отображение детальной информации

## 📊 API модуля

### Основные методы

```javascript
// Инициализация
await window.adCountersManager.init();

// Увеличение счетчика
window.adCountersManager.incrementCounter('native_banner');

// Получение оставшихся показов
const remaining = window.adCountersManager.getRemainingCount('rewarded_video');

// Проверка достижения лимита
const isLimited = window.adCountersManager.isLimitReached('interstitial');

// Обновление языка
window.adCountersManager.updateLanguage('en');

// Сброс всех счетчиков
window.adCountersManager.resetAllCounters();
```

### Типы рекламы

- `native_banner` - Баннерная реклама (кнопка "Открыть ссылку")
- `rewarded_video` - Видеореклама (кнопка "Смотреть видео")
- `interstitial` - Интерстициальная реклама (кнопка "Кликнуть по баннеру")

## 🎯 Результат

Пользователи теперь видят:
- Сколько показов рекламы у них осталось на сегодня
- Информация обновляется в реальном времени
- Поддержка русского и английского языков
- Элегантное визуальное оформление в стиле приложения

## 🔄 Автоматические функции

- **Ежедневный сброс UTC** - счетчики автоматически сбрасываются в 00:00 UTC
- **Автоочистка старых данных** - старые счетчики удаляются при каждом обновлении
- **Синхронизация с рекламой** - счетчики увеличиваются только при успешных просмотрах
- **Языковая адаптация** - автоматическое обновление при смене языка пользователем
- **Сохранение состояния** - данные сохраняются в localStorage браузера
- **Проверка смены дня** - система автоматически определяет переход на новый день UTC
