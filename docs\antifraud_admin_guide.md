# Руководство по антифрод системе в админке

## Обзор

Новая вкладка "🛡️ Антифрод система" в разделе "Безопасность" админки предоставляет полный контроль над системой предотвращения фрода.

## Функциональность

### 1. Статистика антифрод системы
- **Всего отпечатков**: Общее количество зарегистрированных отпечатков устройств
- **Заблокировано устройств**: Количество устройств, заблокированных за подозрительную активность
- **Дубликаты отпечатков**: Количество отпечатков, используемых несколькими пользователями
- **Попытки фрода**: Общее количество зарегистрированных попыток мошенничества

### 2. Настройки антифрод системы
- **Включить антифрод систему**: Глобальное включение/выключение системы
- **Порог фрода (баллы)**: Количество баллов риска для автоматической блокировки (10-100)
- **Блокировать VPN/Proxy**: Автоматическая блокировка VPN и прокси соединений
- **Блокировать дубликаты отпечатков**: Блокировка устройств с повторяющимися отпечатками
- **Блокировать самореферралы**: Предотвращение самостоятельных рефералов

### 3. Журнал фрода
Отображает последние 50 записей о подозрительной активности:
- **Время**: Дата и время события
- **Пользователь**: ID пользователя (если доступен)
- **Тип нарушения**: Категория подозрительной активности
- **Риск**: Уровень риска (LOW, MEDIUM, HIGH, CRITICAL)
- **Баллы**: Количество баллов риска
- **Действие**: Предпринятое действие системой
- **Детали**: Дополнительная информация о событии

**Действия:**
- **Обновить**: Перезагрузить журнал
- **Очистить**: Удалить все записи журнала

### 4. Заблокированные устройства
Список всех заблокированных устройств:
- **Отпечаток устройства**: Сокращенный хеш отпечатка
- **Пользователи**: Количество пользователей, использующих это устройство
- **Дата блокировки**: Когда устройство было заблокировано
- **Причина**: Причина блокировки
- **Действия**: Кнопка разблокировки

## Технические детали

### Файлы конфигурации
- `database/antifraud_settings.json` - Настройки системы
- `database/device_fingerprints.json` - Отпечатки устройств
- `database/fraud_log.json` - Журнал фрода
- `database/blocked_devices.json` - Заблокированные устройства

### API endpoints
Все запросы отправляются на `/api/fraud-detection.php` с различными действиями:
- `get_admin_stats` - Получение статистики
- `save_admin_settings` - Сохранение настроек
- `clear_fraud_log` - Очистка журнала
- `unblock_device` - Разблокировка устройства
- `log_suspicious_activity` - Логирование активности

### Автообновление
Данные автоматически загружаются при переключении на вкладку антифрод.

## Безопасность

- Все настройки валидируются на сервере
- Журнал ограничен 1000 последними записями
- Отпечатки устройств хешируются для конфиденциальности
- Блокировки применяются в реальном времени

## Интеграция

Антифрод система интегрирована с:
- Системой рекламы (блокировка просмотров)
- Системой регистрации пользователей
- Системой рефералов
- Основным приложением через fraud-manager.js
