<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../db_mock.php';

$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$period = isset($_GET['period']) ? $_GET['period'] : 'last_7_days';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;

if ($userId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid user ID']);
    exit;
}

function getUserActivityStats($userId, $period, $dateFrom, $dateTo) {
    $allUserData = loadUserData();
    if (!isset($allUserData[$userId]) || !isset($allUserData[$userId]['ad_views_log'])) {
        return []; // У пользователя нет данных о просмотрах
    }

    $adViews = $allUserData[$userId]['ad_views_log'];
    $stats = [];

    if ($period === 'last_24_hours') {
        for ($i = 0; $i < 24; $i++) {
            $stats[date('H:00', strtotime("-$i hours"))] = 0;
        }
        $startTime = strtotime('-24 hours');
        foreach ($adViews as $timestamp) {
            if ($timestamp >= $startTime) {
                $hourKey = date('H:00', $timestamp);
                if (isset($stats[$hourKey])) {
                    $stats[$hourKey]++;
                }
            }
        }
        krsort($stats);
    } else { // Дневная статистика
        $startDate = null;
        $endDate = null;

        if ($period === 'last_7_days') {
            $startDate = new DateTime('-6 days');
            $endDate = new DateTime('today');
        } elseif ($period === 'custom_date' && !empty($dateFrom) && !empty($dateTo)) {
            $startDate = new DateTime($dateFrom);
            $endDate = new DateTime($dateTo);
        }

        if ($startDate && $endDate) {
            $currentDate = clone $startDate;
            while ($currentDate <= $endDate) {
                $stats[$currentDate->format('Y-m-d')] = 0;
                $currentDate->modify('+1 day');
            }

            foreach ($adViews as $timestamp) {
                $clickDate = date('Y-m-d', $timestamp);
                if (isset($stats[$clickDate])) {
                    $stats[$clickDate]++;
                }
            }
        }
    }

    return $stats;
}

$data = getUserActivityStats($userId, $period, $dateFrom, $dateTo);

echo json_encode(['success' => true, 'data' => $data]);
