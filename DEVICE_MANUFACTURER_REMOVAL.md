# Удаление колонки "Device Manufacturer" из RichAds Success логов

## Обзор изменений

По требованию пользователя исключена колонка "Device Manufacturer" из отображения в таблице и экспорта данных RichAds Success логов.

## Внесенные изменения

### 1. Обновление HTML таблицы
**Файл**: `api/admin/security.php`

#### Заголовок таблицы:
```html
<!-- БЫЛО: 7 колонок -->
<thead class="table-dark">
    <tr>
        <th>Telegram User ID</th>
        <th>IP Address</th>
        <th>Device Type</th>
        <th>Platform</th>
        <th>Screen Resolution</th>
        <th>Device Manufacturer</th>  <!-- УДАЛЕНО -->
        <th>Time</th>
    </tr>
</thead>

<!-- СТАЛО: 6 колонок -->
<thead class="table-dark">
    <tr>
        <th>Telegram User ID</th>
        <th>IP Address</th>
        <th>Device Type</th>
        <th>Platform</th>
        <th>Screen Resolution</th>
        <th>Time</th>
    </tr>
</thead>
```

### 2. Обновление JavaScript функции отображения
**Файл**: `api/admin/security.php`

#### Функция `updateRichaddsLogTable()`:
```javascript
// БЫЛО: colspan="7"
if (data.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Нет данных для отображения</td></tr>';
}

// СТАЛО: colspan="6"
if (data.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Нет данных для отображения</td></tr>';
}

// БЫЛО: 7 колонок включая Device Manufacturer
return `
    <tr>
        <td>${row[0] || '-'}</td>  <!-- Telegram User ID -->
        <td>${row[1] || '-'}</td>  <!-- IP Address -->
        <td>${row[2] || '-'}</td>  <!-- Device Type -->
        <td>${row[3] || '-'}</td>  <!-- Platform -->
        <td>${row[4] || '-'}</td>  <!-- Screen Resolution -->
        <td>${row[5] || '-'}</td>  <!-- Device Manufacturer -->
        <td>${row[6] || '-'}</td>  <!-- Time -->
    </tr>
`;

// СТАЛО: 6 колонок без Device Manufacturer
return `
    <tr>
        <td>${row[0] || '-'}</td>  <!-- Telegram User ID -->
        <td>${row[1] || '-'}</td>  <!-- IP Address -->
        <td>${row[2] || '-'}</td>  <!-- Device Type -->
        <td>${row[3] || '-'}</td>  <!-- Platform -->
        <td>${row[4] || '-'}</td>  <!-- Screen Resolution -->
        <td>${row[6] || '-'}</td>  <!-- Time (пропускаем индекс 5) -->
    </tr>
`;
```

### 3. Обновление экспорта текущей страницы
**Файл**: `api/admin/security.php`

#### Функция `exportCurrentPage()`:
```javascript
// БЫЛО: 7 колонок в CSV
const csvData = [
    ['Telegram User ID', 'IP Address', 'Device Type', 'Platform', 'Screen Resolution', 'Device Manufacturer', 'Time'],
    ...currentPageData.map(row => [row[0], row[1], row[2], row[3], row[4], row[5], row[6]])
];

// СТАЛО: 6 колонок в CSV
const csvData = [
    ['Telegram User ID', 'IP Address', 'Device Type', 'Platform', 'Screen Resolution', 'Time'],
    ...currentPageData.map(row => [row[0], row[1], row[2], row[3], row[4], row[6]])
];
```

### 4. Обновление серверного экспорта
**Файл**: `api/admin/export_richadds_log.php`

#### Заголовки CSV:
```php
// БЫЛО: 7 колонок
$exportHeader = [
    'Telegram User ID',
    'IP Address',
    'Device Type',
    'Platform',
    'Screen Resolution',
    'Device Manufacturer',  // УДАЛЕНО
    'Time'
];

// СТАЛО: 6 колонок
$exportHeader = [
    'Telegram User ID',
    'IP Address',
    'Device Type',
    'Platform',
    'Screen Resolution',
    'Time'
];
```

#### Данные для экспорта:
```php
// БЫЛО: 7 полей включая Device Manufacturer
$exportRow = [
    $row[0], // Telegram User ID
    $row[1], // IP Address
    $row[2], // Device Type
    formatPlatform($row[3]), // Platform
    $row[4], // Screen Resolution
    $row[5], // Device Manufacturer
    $row[6]  // Time
];

// СТАЛО: 6 полей без Device Manufacturer
$exportRow = [
    $row[0], // Telegram User ID
    $row[1], // IP Address
    $row[2], // Device Type
    formatPlatform($row[3]), // Platform
    $row[4], // Screen Resolution
    $row[6]  // Time (пропускаем индекс 5)
];
```

## Структура данных

### Исходные данные (CSV файл):
```
Индекс | Поле                  | Статус
-------|----------------------|----------
[0]    | Telegram User ID     | ✅ Отображается
[1]    | IP Address           | ✅ Отображается
[2]    | Device Type          | ✅ Отображается
[3]    | Platform             | ✅ Отображается
[4]    | Screen Resolution    | ✅ Отображается
[5]    | Device Manufacturer  | ❌ ИСКЛЮЧЕНО
[6]    | Time                 | ✅ Отображается
```

### Отображение в таблице:
```
Колонка | Поле                 | Исходный индекс
--------|---------------------|----------------
1       | Telegram User ID    | [0]
2       | IP Address          | [1]
3       | Device Type         | [2]
4       | Platform            | [3]
5       | Screen Resolution   | [4]
6       | Time                | [6] (пропускаем [5])
```

### Экспорт в CSV:
```
Колонка | Поле                 | Исходный индекс
--------|---------------------|----------------
1       | Telegram User ID    | [0]
2       | IP Address          | [1]
3       | Device Type         | [2]
4       | Platform            | [3]
5       | Screen Resolution   | [4]
6       | Time                | [6] (пропускаем [5])
```

## Результаты тестирования

### ✅ Таблица:
- Отображается 6 колонок вместо 7
- Device Manufacturer не показывается
- Данные корректно маппятся (пропускается индекс 5)
- Пагинация работает правильно

### ✅ Экспорт текущей страницы:
- CSV содержит 6 колонок
- Device Manufacturer отсутствует в заголовках и данных
- Маппинг данных: `[row[0], row[1], row[2], row[3], row[4], row[6]]`

### ✅ Серверный экспорт:
- Все типы экспорта (текущая страница, отфильтрованные, все данные)
- CSV заголовки содержат 6 полей
- Device Manufacturer исключен из экспорта
- Сортировка работает корректно

## Примеры файлов

### До изменений:
```csv
"Telegram User ID","IP Address","Device Type","Platform","Screen Resolution","Device Manufacturer","Time"
"7971051670","***************","Linux armv7l","Android (ARM 32-bit)","360x800","Google Inc.","12:21:43"
```

### После изменений:
```csv
"Telegram User ID","IP Address","Device Type","Platform","Screen Resolution","Time"
"7971051670","***************","Linux armv7l","Android (ARM 32-bit)","360x800","12:21:43"
```

## Обратная совместимость

- ✅ Исходный CSV файл остается неизменным
- ✅ API endpoints работают как прежде
- ✅ Фильтрация и сортировка не затронуты
- ✅ Все типы экспорта поддерживаются
- ✅ Только изменено отображение и экспорт

## Файлы изменены

1. `api/admin/security.php` - HTML таблица и JavaScript функции
2. `api/admin/export_richadds_log.php` - серверный экспорт
3. `docs/richadds_success_log_admin_guide.md` - документация
4. `RICHADDS_TESTING_INSTRUCTIONS.md` - инструкции по тестированию

Изменения минимальны и затрагивают только отображение и экспорт данных, не влияя на функциональность системы.
