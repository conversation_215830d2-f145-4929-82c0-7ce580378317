/**
 * === debug-navigation.js ===
 * Отладочные функции для тестирования навигации
 */

// Функция для тестирования навигации
function testNavigation() {
  console.log('🧪 Тестирование навигации...');
  
  // Проверяем наличие элементов
  const pages = ['main-content', 'earn-section', 'friends-section'];
  const buttons = ['nav-home', 'nav-earn', 'nav-friends'];
  
  console.log('📄 Проверка страниц:');
  pages.forEach(id => {
    const element = document.getElementById(id);
    console.log(`  ${id}: ${element ? '✅ найдена' : '❌ не найдена'}`);
    if (element) {
      console.log(`    Классы: ${element.className}`);
      console.log(`    Видимость: ${getComputedStyle(element).display}`);
    }
  });
  
  console.log('🔘 Проверка кнопок:');
  buttons.forEach(id => {
    const element = document.getElementById(id);
    console.log(`  ${id}: ${element ? '✅ найдена' : '❌ не найдена'}`);
    if (element) {
      console.log(`    Классы: ${element.className}`);
    }
  });
  
  // Проверяем NavigationManager
  if (window.navigationManager) {
    console.log('🎯 NavigationManager доступен');
    console.log('  Текущая страница:', navigationManager.currentPageId);
    console.log('  Кешированные страницы:', navigationManager.pages.size);
    console.log('  Кешированные кнопки:', navigationManager.navButtons.size);
  } else {
    console.log('❌ NavigationManager не найден');
  }
}

// Функция для принудительного переключения страниц
function forceShowPage(pageId) {
  console.log(`🔧 Принудительное переключение на: ${pageId}`);
  
  const page = document.getElementById(pageId);
  if (!page) {
    console.error(`❌ Страница ${pageId} не найдена`);
    return;
  }
  
  // Скрываем все страницы
  const allPages = document.querySelectorAll('.app-section, .app-main');
  allPages.forEach(p => {
    p.classList.remove('active-section');
    p.classList.add('page-hidden');
    p.style.display = 'none';
  });
  
  // Показываем целевую страницу
  page.classList.remove('page-hidden');
  page.classList.add('active-section');
  page.style.display = 'block';
  
  // Обновляем кнопки
  const allButtons = document.querySelectorAll('.nav-button');
  allButtons.forEach(btn => btn.classList.remove('active'));
  
  const buttonMapping = {
    'main-content': 'nav-home',
    'earn-section': 'nav-earn',
    'friends-section': 'nav-friends'
  };
  
  const buttonId = buttonMapping[pageId];
  const button = document.getElementById(buttonId);
  if (button) {
    button.classList.add('active');
  }
  
  console.log(`✅ Страница ${pageId} показана принудительно`);
}

// Функция для добавления обработчиков кликов
function addClickHandlers() {
  console.log('🖱️ Добавление обработчиков кликов...');
  
  const handlers = {
    'nav-home': () => forceShowPage('main-content'),
    'nav-earn': () => forceShowPage('earn-section'),
    'nav-friends': () => forceShowPage('friends-section')
  };
  
  Object.entries(handlers).forEach(([buttonId, handler]) => {
    const button = document.getElementById(buttonId);
    if (button) {
      // Удаляем старые обработчики
      button.replaceWith(button.cloneNode(true));
      const newButton = document.getElementById(buttonId);
      
      // Добавляем новый обработчик
      newButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log(`🖱️ Клик по ${buttonId}`);
        handler();
      });
      
      console.log(`✅ Обработчик добавлен для ${buttonId}`);
    } else {
      console.log(`❌ Кнопка ${buttonId} не найдена`);
    }
  });
}

// Функция для полной диагностики
function fullDiagnostic() {
  console.log('🔍 ПОЛНАЯ ДИАГНОСТИКА НАВИГАЦИИ');
  console.log('================================');
  
  testNavigation();
  console.log('');
  addClickHandlers();
  console.log('');
  
  console.log('🎮 Доступные команды:');
  console.log('  testNavigation() - проверить элементы');
  console.log('  forceShowPage("main-content") - показать главную');
  console.log('  forceShowPage("earn-section") - показать заработок');
  console.log('  forceShowPage("friends-section") - показать друзей');
  console.log('  addClickHandlers() - добавить обработчики');
  console.log('  fullDiagnostic() - полная диагностика');
}

// Экспорт функций
window.testNavigation = testNavigation;
window.forceShowPage = forceShowPage;
window.addClickHandlers = addClickHandlers;
window.fullDiagnostic = fullDiagnostic;

// Автоматический запуск диагностики
setTimeout(() => {
  console.log('🚀 Автоматическая диагностика навигации...');
  fullDiagnostic();
}, 2000);

console.log('🔧 [DebugNavigation] Отладочные функции загружены');
