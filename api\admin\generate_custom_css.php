<?php
/**
 * generate_custom_css.php
 * Генерирует custom.css на основе настроек дизайна из админки
 */

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

/**
 * Генерирует CSS файл на основе настроек дизайна
 */
function generateCustomCSS($settings) {
    // Отладочная информация
    error_log('generateCustomCSS вызван с настройками: ' . print_r($settings, true));

    $colors = $settings['colors'] ?? [];
    $effects = $settings['effects'] ?? [];
    $layout = $settings['layout'] ?? [];
    $theme = $settings['theme'] ?? 'cyberpunk';

    error_log('Цвета для генерации: ' . print_r($colors, true));
    
    $css = "/* \n";
    $css .= " * CUSTOM.CSS - Автоматически сгенерированный файл\n";
    $css .= " * Создан: " . date('Y-m-d H:i:s') . "\n";
    $css .= " * Тема: {$theme}\n";
    $css .= " * Акцентный цвет: " . ($colors['accent_orange'] ?? 'не установлен') . "\n";
    $css .= " */\n\n";
    
    // CSS переменные с !important
    $css .= ":root {\n";

    // Цвета
    if (!empty($colors)) {
        $css .= "  /* Цвета */\n";
        foreach ($colors as $key => $value) {
            $cssVar = str_replace('_', '-', $key);
            $css .= "  --{$cssVar}: {$value} !important;\n";
        }
        $css .= "\n";
    }

    // Эффекты
    if (!empty($effects)) {
        $css .= "  /* Эффекты */\n";
        foreach ($effects as $key => $value) {
            if (is_numeric($value)) {
                $cssVar = str_replace('_', '-', $key);
                $css .= "  --{$cssVar}: {$value} !important;\n";
            }
        }
        $css .= "\n";
    }

    // Макет
    if (!empty($layout)) {
        $css .= "  /* Макет */\n";
        foreach ($layout as $key => $value) {
            $cssVar = str_replace('_', '-', $key);
            $unit = is_numeric($value) ? 'px' : '';
            $css .= "  --{$cssVar}: {$value}{$unit} !important;\n";
        }
        $css .= "\n";
    }

    $css .= "}\n\n";
    
    // Основные стили body
    $css .= "/* Основные стили */\n";
    $css .= "body {\n";
    if (isset($colors['primary_dark'])) {
        $css .= "  background: var(--primary-dark) !important;\n";
    }
    if (isset($colors['text_primary'])) {
        $css .= "  color: var(--text-primary) !important;\n";
    }
    $css .= "}\n\n";
    
    // Стили карточек
    if (isset($colors['bg_card']) || isset($colors['border_color'])) {
        $css .= "/* Карточки */\n";
        $css .= ".card, .earn-block, .friends-block {\n";
        if (isset($colors['bg_card'])) {
            $css .= "  background: var(--bg-card) !important;\n";
        }
        if (isset($colors['border_color'])) {
            $css .= "  border-color: var(--border-color) !important;\n";
        }
        if (isset($layout['border_radius'])) {
            $css .= "  border-radius: var(--border-radius) !important;\n";
        }
        $css .= "}\n\n";
    }
    
    // Стили кнопок - максимально агрессивные селекторы
    if (isset($colors['accent_orange'])) {
        $css .= "/* Кнопки - все возможные селекторы */\n";
        $css .= ".action-button, .btn-primary, button, .btn, input[type='submit'], input[type='button'] {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  background-image: linear-gradient(145deg, var(--accent-orange), var(--accent-orange)) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  border: 1px solid var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        if (isset($layout['button_radius'])) {
            $css .= "  border-radius: var(--button-radius) !important;\n";
        }
        $css .= "}\n\n";

        // Специфичные селекторы для кнопок в разных секциях
        $css .= "/* Кнопки в секциях заработка и друзей */\n";
        $css .= ".earn-section button, .friends-section button, .earn-block button, .friends-block button {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Hover эффекты - максимально агрессивные
        $css .= ".action-button:hover, .btn-primary:hover, button:hover, .btn:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        // Hover для всех специфичных кнопок
        $css .= ".purple-button:hover, .blue-button:hover, .orange-button:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= ".primary-action:hover, .secondary-action:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= ".copy-button:hover, .refresh-history-btn:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        // Принудительное переопределение для всех кнопок
        $css .= "/* Принудительное переопределение всех кнопок */\n";
        $css .= "*[class*='button'], *[class*='btn'] {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";
    }

    // Стили навигации
    if (isset($colors['accent_orange'])) {
        $css .= "/* Навигация - исправленная */\n";
        $css .= ".nav-button.active {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // .balance-info будет переопределен ниже в специфичных стилях

        // Дополнительные стили с !important
        $css .= "/* Заголовки и иконки */\n";
        $css .= "h1, h2, h3, h4, h5, h6 {\n";
        $css .= "  color: var(--text-primary) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Иконки заголовков - максимально агрессивные */\n";
        $css .= "h2 svg, h3 svg, h4 svg, h2 > svg, h3 > svg, h4 > svg {\n";
        $css .= "  color: var(--accent-orange) !important;\n";
        $css .= "  fill: var(--accent-orange) !important;\n";
        $css .= "  stroke: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Иконки в заголовках с style атрибутами */\n";
        $css .= "h2 svg[style], h3 svg[style], h4 svg[style] {\n";
        $css .= "  color: var(--accent-orange) !important;\n";
        $css .= "  fill: var(--accent-orange) !important;\n";
        $css .= "  stroke: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Переопределение всех SVG с старым оранжевым цветом */\n";
        $css .= "[style*='color: #ff6b35'], [style*='stroke: #ff6b35'], [style*='fill: #ff6b35'] {\n";
        $css .= "  color: var(--accent-orange) !important;\n";
        $css .= "  fill: var(--accent-orange) !important;\n";
        $css .= "  stroke: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        // Стили для истории выплат
        $css .= "/* История выплат */\n";
        $css .= ".history-item, .withdrawal-history .history-item {\n";
        $css .= "  border-left: 3px solid var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= ".history-item svg, .withdrawal-history svg {\n";
        $css .= "  color: var(--accent-orange) !important;\n";
        $css .= "  fill: var(--accent-orange) !important;\n";
        $css .= "  stroke: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= ".history-status.pending, .status-pending {\n";
        $css .= "  color: var(--accent-orange) !important;\n";
        $css .= "  background: rgba(255, 107, 53, 0.1) !important;\n";
        $css .= "}\n\n";

        $css .= ".history-status.completed, .status-completed {\n";
        $css .= "  color: var(--juicy-green) !important;\n";
        $css .= "  background: rgba(0, 255, 127, 0.1) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Навигация */\n";
        $css .= ".app-nav {\n";
        $css .= "  background: var(--bg-card) !important;\n";
        $css .= "  border-top: 1px solid var(--border-color) !important;\n";
        $css .= "}\n\n";

        $css .= ".nav-button {\n";
        $css .= "  color: var(--text-primary) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Хедер приложения */\n";
        $css .= ".app-header {\n";
        $css .= "  background: var(--bg-card) !important;\n";
        $css .= "  border-bottom: 1px solid var(--border-color) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Аватар пользователя */\n";
        $css .= ".user-avatar {\n";
        $css .= "  border: 2px solid var(--accent-orange) !important;\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= ".user-avatar-icon {\n";
        $css .= "  color: var(--primary-dark) !important;\n";
        $css .= "}\n\n";

        // Специфичные стили для элементов из скриншота
        $css .= "/* Специфичные кнопки и элементы */\n";
        $css .= "#shareButton, #copyButton, .copy-button, .share-button {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Кнопки в нижней навигации
        $css .= ".nav-button, .navigation-button, .bottom-nav button {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Активные состояния навигации
        $css .= ".nav-button.active, .navigation-button.active {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";
    }
    
    // Эффекты включения/выключения
    if (isset($effects['enable_crypto_icons']) && !$effects['enable_crypto_icons']) {
        $css .= "/* Отключение криптоиконок */\n";
        $css .= ".crypto-background, .floating-crypto {\n";
        $css .= "  display: none !important;\n";
        $css .= "}\n\n";
    }

    if (isset($effects['enable_glitch_effects']) && !$effects['enable_glitch_effects']) {
        $css .= "/* Отключение глитч эффектов */\n";
        $css .= ".glitch-line {\n";
        $css .= "  display: none !important;\n";
        $css .= "}\n\n";
    }

    // Специфичные исправления для элементов из скриншота
    if (isset($colors['accent_orange'])) {
        $css .= "/* ИСПРАВЛЕНИЕ КОНКРЕТНЫХ ЭЛЕМЕНТОВ ИЗ СКРИНШОТА */\n";

        // Кнопка баланса в хедере (зеленая кнопка с монетами) - должна остаться зеленой
        $css .= ".balance-info, .clickable-balance {\n";
        $css .= "  background: var(--juicy-green) !important;\n";
        $css .= "  background-color: var(--juicy-green) !important;\n";
        $css .= "  border-color: var(--juicy-green) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Отображение баланса в секции заработка
        $css .= ".current-balance-display, .balance-display {\n";
        $css .= "  background: rgba(0, 255, 255, 0.05) !important;\n";
        $css .= "  border: 1px solid var(--border-color) !important;\n";
        $css .= "}\n\n";

        // Кнопки калькулятора валют (неактивные - серые, активные - оранжевые)
        $css .= ".currency-tab:not(.active) {\n";
        $css .= "  background: var(--bg-secondary) !important;\n";
        $css .= "  background-color: var(--bg-secondary) !important;\n";
        $css .= "  border-color: var(--border-color) !important;\n";
        $css .= "  color: var(--text-primary) !important;\n";
        $css .= "}\n\n";

        $css .= ".currency-tab.active {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Переопределение только для кнопок действий (не всех элементов)
        $css .= "/* МАКСИМАЛЬНО АГРЕССИВНЫЕ СТИЛИ ТОЛЬКО ДЛЯ КНОПОК ДЕЙСТВИЙ */\n";
        // Переопределение только кнопок действий (исключая баланс и валютные табы)
        $css .= "button[style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='button'][style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='submit'][style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab) {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Переопределение inline стилей только для кнопок */\n";
        $css .= "button[style*='background-color']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='button'][style*='background-color']:not(.balance-info):not(.clickable-balance):not(.currency-tab) {\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "/* Кнопки с классами button/btn (исключая специальные) */\n";
        $css .= "[class*='button']:not(.nav-button):not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), [class*='btn']:not(.nav-button):not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // ЯДЕРНЫЙ ВАРИАНТ - переопределение кликабельных элементов (исключая специальные)
        $css .= "/* ЯДЕРНЫЙ ВАРИАНТ - максимальная специфичность для кликабельных элементов */\n";
        $css .= "html body *[onclick]:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), html body *[role='button']:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), html body *.clickable:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Специфичные ID и классы из приложения
        $css .= "/* Специфичные элементы приложения */\n";
        $css .= "#earnButton, #friendsButton, #mainButton {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Конкретные кнопки из HTML
        $css .= "/* Конкретные кнопки из приложения */\n";
        $css .= ".purple-button, .blue-button, .orange-button, .primary-action, .secondary-action {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        $css .= "#openLinkButton, #watchVideoButton, #openAdButton, #request-withdrawal-button {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        $css .= "#share-app-button, #copy-referral-button, #refresh-stats-button {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        $css .= ".copy-button, .refresh-history-btn {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "  color: #ffffff !important;\n";
        $css .= "}\n\n";

        // Дополнительные hover эффекты для всех ID кнопок
        $css .= "/* Hover эффекты для всех ID кнопок */\n";
        $css .= "#openLinkButton:hover, #watchVideoButton:hover, #openAdButton:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "#request-withdrawal-button:hover, #share-app-button:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        $css .= "#copy-referral-button:hover, #refresh-stats-button:hover {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";

        // Универсальный hover для кликабельных элементов (исключая специальные)
        $css .= "/* Универсальный hover для кликабельных элементов */\n";
        $css .= "*[onclick]:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), *[role='button']:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), *.clickable:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {\n";
        $css .= "  background: var(--accent-orange) !important;\n";
        $css .= "  background-color: var(--accent-orange) !important;\n";
        $css .= "  opacity: 0.9 !important;\n";
        $css .= "  border-color: var(--accent-orange) !important;\n";
        $css .= "}\n\n";
    }
    
    return $css;
}

/**
 * Сохраняет CSS в файл custom.css
 */
function saveCustomCSS($css) {
    $customCssPath = __DIR__ . '/../../css/custom.css';
    
    // Создаем директорию если не существует
    $dir = dirname($customCssPath);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    // Записываем CSS
    $result = file_put_contents($customCssPath, $css);
    
    if ($result === false) {
        throw new Exception('Не удалось записать файл custom.css');
    }
    
    return true;
}

/**
 * Загружает настройки дизайна
 */
function loadDesignSettings() {
    // Используем тот же путь, что и в design.php
    $settingsFile = __DIR__ . '/../../design_settings.json';
    
    if (!file_exists($settingsFile)) {
        return [];
    }
    
    $json = file_get_contents($settingsFile);
    $settings = json_decode($json, true);
    
    return $settings ?: [];
}

// Если файл вызывается напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $settings = loadDesignSettings();
        $css = generateCustomCSS($settings);
        saveCustomCSS($css);
        
        echo json_encode([
            'success' => true,
            'message' => 'Файл custom.css успешно обновлен'
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Ошибка: ' . $e->getMessage()
        ]);
    }
}
?>
