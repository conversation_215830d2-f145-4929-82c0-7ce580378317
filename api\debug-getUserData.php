<?php
/**
 * api/debug-getUserData.php
 * Диагностика проблем с getUserData.php
 */

// Включаем отображение всех ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html><html><head><meta charset='utf-8'><title>🔧 Диагностика getUserData</title>";
echo "<style>body{font-family:monospace;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";
echo "</head><body>";

echo "<h1>🔧 Диагностика getUserData.php</h1>";
echo "<p><strong>Время:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Шаг 1: Проверяем PHP
echo "<h2>1. Проверка PHP</h2>";
echo "<div class='ok'>✅ PHP версия: " . phpversion() . "</div>";
echo "<div class='ok'>✅ Память: " . ini_get('memory_limit') . "</div>";
echo "<div class='ok'>✅ Время выполнения: " . ini_get('max_execution_time') . "s</div>";

// Шаг 2: Проверяем файлы
echo "<h2>2. Проверка файлов</h2>";

$files = [
    'getUserData.php' => __DIR__ . '/getUserData.php',
    'config.php' => __DIR__ . '/config.php',
    'validate_initdata.php' => __DIR__ . '/validate_initdata.php',
    'db_mock.php' => __DIR__ . '/db_mock.php'
];

foreach ($files as $name => $path) {
    if (file_exists($path)) {
        $size = filesize($path);
        $readable = is_readable($path) ? 'читаемый' : 'НЕ читаемый';
        echo "<div class='ok'>✅ $name - существует ($size байт, $readable)</div>";
        
        // Проверяем синтаксис
        $output = [];
        $return_var = 0;
        exec("php -l \"$path\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='ok'>   ✅ Синтаксис корректен</div>";
        } else {
            echo "<div class='error'>   ❌ Синтаксическая ошибка:</div>";
            echo "<pre>" . implode("\n", $output) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ $name - НЕ НАЙДЕН</div>";
    }
}

// Шаг 3: Проверяем папки
echo "<h2>3. Проверка папок</h2>";

$dirs = [
    '../database' => __DIR__ . '/../database',
    'database' => __DIR__ . '/database'
];

foreach ($dirs as $name => $path) {
    if (is_dir($path)) {
        $writable = is_writable($path) ? 'записываемая' : 'НЕ записываемая';
        echo "<div class='ok'>✅ $name - существует ($writable)</div>";
    } else {
        if (@mkdir($path, 0755, true)) {
            echo "<div class='warning'>⚠️ $name - создана</div>";
        } else {
            echo "<div class='error'>❌ $name - не удалось создать</div>";
        }
    }
}

// Шаг 4: Тестируем подключение файлов
echo "<h2>4. Тест подключения файлов</h2>";

try {
    if (file_exists(__DIR__ . '/config.php')) {
        require_once __DIR__ . '/config.php';
        echo "<div class='ok'>✅ config.php подключен</div>";
    } else {
        echo "<div class='error'>❌ config.php не найден</div>";
    }
    
    if (file_exists(__DIR__ . '/validate_initdata.php')) {
        require_once __DIR__ . '/validate_initdata.php';
        echo "<div class='ok'>✅ validate_initdata.php подключен</div>";
        
        if (function_exists('validateTelegramInitData')) {
            echo "<div class='ok'>   ✅ функция validateTelegramInitData найдена</div>";
        } else {
            echo "<div class='error'>   ❌ функция validateTelegramInitData НЕ найдена</div>";
        }
    } else {
        echo "<div class='error'>❌ validate_initdata.php не найден</div>";
    }
    
    if (file_exists(__DIR__ . '/db_mock.php')) {
        require_once __DIR__ . '/db_mock.php';
        echo "<div class='ok'>✅ db_mock.php подключен</div>";
        
        $functions = ['loadUserData', 'saveUserData'];
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "<div class='ok'>   ✅ функция $func найдена</div>";
            } else {
                echo "<div class='error'>   ❌ функция $func НЕ найдена</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ db_mock.php не найден</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка подключения: " . $e->getMessage() . "</div>";
}

// Шаг 5: Тестируем функции
echo "<h2>5. Тест функций</h2>";

try {
    if (function_exists('loadUserData')) {
        $userData = loadUserData();
        if (is_array($userData)) {
            echo "<div class='ok'>✅ loadUserData работает (загружено " . count($userData) . " пользователей)</div>";
        } else {
            echo "<div class='error'>❌ loadUserData вернул не массив: " . gettype($userData) . "</div>";
        }
    }
    
    if (function_exists('validateTelegramInitData')) {
        // Тестируем с пустыми данными
        $result = validateTelegramInitData('test');
        echo "<div class='warning'>⚠️ validateTelegramInitData с тестовыми данными: " . ($result ? 'прошла' : 'не прошла') . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка тестирования функций: " . $e->getMessage() . "</div>";
}

// Шаг 6: Прямой тест getUserData.php
echo "<h2>6. Прямой тест getUserData.php</h2>";

try {
    // Эмулируем POST запрос
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST = [];
    
    // Создаем тестовые данные
    $testData = json_encode(['initData' => 'test_data_for_debug']);
    
    // Буферизуем вывод
    ob_start();
    
    // Временно перенаправляем php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'debug_input');
    file_put_contents($tempFile, $testData);
    
    echo "<div class='warning'>⚠️ Попытка выполнения getUserData.php...</div>";
    
    // Включаем файл (это может вызвать ошибку)
    include __DIR__ . '/getUserData.php';
    
    $output = ob_get_clean();
    
    echo "<div class='ok'>✅ getUserData.php выполнен без фатальных ошибок</div>";
    echo "<div><strong>Вывод:</strong></div>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    // Проверяем JSON
    $json = json_decode($output, true);
    if ($json !== null) {
        echo "<div class='ok'>✅ Возвращает валидный JSON</div>";
        if (isset($json['error'])) {
            echo "<div class='warning'>⚠️ Ошибка в ответе: " . $json['error'] . "</div>";
        }
    } else {
        echo "<div class='error'>❌ НЕ возвращает валидный JSON</div>";
    }
    
    unlink($tempFile);
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<div class='error'>❌ Исключение при выполнении: " . $e->getMessage() . "</div>";
    echo "<div class='error'>   Файл: " . $e->getFile() . "</div>";
    echo "<div class='error'>   Строка: " . $e->getLine() . "</div>";
} catch (Error $e) {
    ob_end_clean();
    echo "<div class='error'>❌ Фатальная ошибка: " . $e->getMessage() . "</div>";
    echo "<div class='error'>   Файл: " . $e->getFile() . "</div>";
    echo "<div class='error'>   Строка: " . $e->getLine() . "</div>";
}

// Шаг 7: Проверяем логи
echo "<h2>7. Проверка логов</h2>";

$logFiles = [
    __DIR__ . '/getUserData.log',
    __DIR__ . '/error.log',
    __DIR__ . '/php-error.log'
];

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        echo "<div class='ok'>✅ Лог найден: " . basename($logFile) . "</div>";
        $lines = file($logFile);
        $lastLines = array_slice($lines, -5);
        echo "<div><strong>Последние 5 строк:</strong></div>";
        echo "<pre>" . htmlspecialchars(implode('', $lastLines)) . "</pre>";
        break;
    }
}

echo "<h2>📋 Рекомендации</h2>";
echo "<ol>";
echo "<li>Если есть синтаксические ошибки - исправьте их</li>";
echo "<li>Если отсутствуют файлы - загрузите их на сервер</li>";
echo "<li>Если функции не найдены - проверьте содержимое файлов</li>";
echo "<li>Если папки не создаются - проверьте права доступа</li>";
echo "<li>Если есть фатальные ошибки - смотрите детали выше</li>";
echo "</ol>";

echo "</body></html>";
?>
