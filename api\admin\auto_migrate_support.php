<?php
/**
 * api/admin/auto_migrate_support.php
 * Автоматическая миграция чатов поддержки при загрузке страницы
 */

require_once __DIR__ . '/support_data.php';

function autoMigrateSupportChats() {
    try {
        // Получаем все чаты
        $chats = getSupportChats();
        $needsMigration = false;
        
        // Проверяем, нужна ли миграция
        foreach ($chats as $chatId => $chat) {
            if (!isset($chat['status'])) {
                $needsMigration = true;
                break;
            }
        }
        
        // Если миграция не нужна, выходим
        if (!$needsMigration) {
            return true;
        }
        
        // Выполняем миграцию
        foreach ($chats as $chatId => &$chat) {
            if (!isset($chat['status'])) {
                $chat['status'] = 'open'; // По умолчанию все чаты открыты
            }
        }
        
        // Сохраняем обновленные данные
        return saveSupportChats($chats);
        
    } catch (Exception $e) {
        error_log("Auto migration error: " . $e->getMessage());
        return false;
    }
}

// Выполняем автомиграцию
autoMigrateSupportChats();
?>
