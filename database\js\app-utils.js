// === app-utils.js ===
// Файл: js/app-utils.js
// Описание: Утилитарные функции для приложения UniQPaid (DRY).

class AppUtils {
  constructor() {
    this.tg = window.Telegram?.WebApp;
  }

  showStatus(message, type = "info", duration = 3000) {
    const statusMessageEl = document.getElementById('status-message');
    if (!statusMessageEl) return;

    // Пытаемся перевести сообщение, если доступна локализация
    let translatedMessage = message;
    if (window.translateStatusMessage) {
      translatedMessage = window.translateStatusMessage(message);
    }

    statusMessageEl.textContent = translatedMessage;
    statusMessageEl.className = "status-message";
    if (type === "success") statusMessageEl.classList.add("success");
    if (type === "error") statusMessageEl.classList.add("error");
    if (type === "warning") statusMessageEl.classList.add("warning");
    if (type === "info") statusMessageEl.classList.add("info");
    console.log(`[Status] ${type}: ${translatedMessage}`);
    if (translatedMessage && duration > 0) {
      setTimeout(() => {
        if (statusMessageEl.textContent === translatedMessage) statusMessageEl.textContent = '';
      }, duration);
    }
  }

  /**
   * 🔔 Показывает уведомление о блокировке формы
   */
  showBlockedFormNotification(reason, activeWithdrawals = []) {
    let message = `🔒 Форма выплат заблокирована: ${reason}`;

    if (activeWithdrawals.length > 0) {
      const withdrawal = activeWithdrawals[0];
      message += ` (Статус: ${withdrawal.status_text})`;
    }

    this.showStatus(message, "warning", 5000); // Показываем 5 секунд

    // Переводим тексты для Telegram уведомления
    const titleText = window.appLocalization ? window.appLocalization.get('common.important') : 'Форма заблокирована';
    const buttonText = window.appLocalization ? window.appLocalization.get('common.ok') : 'Понятно';

    // Дополнительно показываем Telegram уведомление
    if (this.tg?.showPopup) {
      this.tg.showPopup({
        title: titleText,
        message: reason,
        buttons: [{ type: "ok", text: buttonText }]
      });
    }
  }

  /**
   * 🎉 Показывает уведомление об успешной отправке заявки
   */
  showWithdrawalSuccessNotification(withdrawalData) {
    const statusText = withdrawalData.status || (window.appLocalization ? window.appLocalization.get('status.processing') : 'В обработке');
    const message = `✅ ${window.appLocalization ? window.appLocalization.get('earnings.withdrawal_success') : 'Заявка на вывод успешно создана!'} ${window.appLocalization ? window.appLocalization.get('common.status') : 'Статус'}: ${statusText}`;

    this.showStatus(message, "success", 4000);

    // Вибрация успеха
    this.vibrate('success');

    // Переводим тексты для Telegram уведомления
    const titleText = window.appLocalization ? window.appLocalization.get('earnings.withdrawal_created') : 'Заявка создана!';
    const messageText = window.appLocalization ?
      window.appLocalization.get('earnings.withdrawal_success_message', {
        amount: withdrawalData.coins_amount || 0
      }) :
      `Ваша заявка на вывод ${withdrawalData.coins_amount || 0} монет успешно отправлена. Следите за статусом в разделе "История выплат".`;
    const buttonText = window.appLocalization ? window.appLocalization.get('common.excellent') : 'Отлично!';

    // Telegram уведомление
    if (this.tg?.showPopup) {
      this.tg.showPopup({
        title: titleText,
        message: messageText,
        buttons: [{ type: "ok", text: buttonText }]
      });
    }
  }

  getInitData() {
    if (this.tg?.initData) return this.tg.initData;
    const user = this.tg?.initDataUnsafe?.user;
    if (user?.id) {
      return `user=${encodeURIComponent(JSON.stringify(user))}&auth_date=${Math.floor(Date.now()/1000)}&hash=dev_fallback_hash`;
    }
    return null;
  }

  vibrate(type) {
    if (!this.tg?.HapticFeedback) return;
    try {
      const notificationTypes = ['success', 'warning', 'error'];
      if (notificationTypes.includes(type)) {
        this.tg.HapticFeedback.notificationOccurred(type);
      } else {
        this.tg.HapticFeedback.impactOccurred(type);
      }
    } catch (e) {
      console.warn('HapticFeedback не поддерживается.');
    }
  }

  showAlert(message) {
    try {
      if (this.tg && this.tg.showAlert) {
        this.tg.showAlert(message);
      } else {
        alert(message);
      }
    } catch (error) {
      console.warn('[AppUtils] Ошибка showAlert, используем обычный alert:', error);
      alert(message);
    }
  }

  playCoinsSound(reward = 10) {
    if (window.audioManager?.playCoinsSound) {
      window.audioManager.playCoinsSound(reward);
    } else {
      console.warn('[Audio] AudioManager не найден.');
    }
  }

  /**
   * Форматирует число как валюту с двумя знаками после запятой.
   * @param {number | string} value - Значение для форматирования.
   */
  static formatCurrency(value) {
    return parseFloat(value).toFixed(2);
  }
}

window.appUtils = new AppUtils();
console.log('🔧 [AppUtils] Модуль утилит загружен.');