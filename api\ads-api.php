<?php
/**
 * api/ads-api.php
 * Единый API endpoint для всех операций с рекламной системой
 * Централизованная обработка всех запросов, связанных с рекламой
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/ads-config.php';
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/validate_initdata.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/security.php';

/**
 * Главный класс API для рекламы
 */
class AdsAPI {
    private $clientIp;
    private $userAgent;
    private $userId = null;
    private $validatedData = null;
    
    public function __construct() {
        $this->clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $this->userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
    
    /**
     * Главный метод обработки запросов
     */
    public function handleRequest() {
        try {
            $action = $_REQUEST['action'] ?? '';

            switch ($action) {
                case 'record_view':
                    $this->recordAdView();
                    break;
                case 'get_limits':
                    $this->getLimits();
                    break;
                case 'log_click':
                    $this->logClick();
                    break;
                case 'get_stats':
                    $this->getStats();
                    break;
                case 'get_config':
                    $this->getConfig();
                    break;
                default:
                    throw new Exception('Неизвестное действие: ' . $action);
            }
        } catch (Exception $e) {
            $this->errorResponse($e->getMessage());
        }
    }
    
    /**
     * Валидация пользователя из initData
     */
    private function validateUser($initData) {
        if ($this->validatedData !== null) {
            return true; // Уже валидирован
        }

        // Для тестирования разрешаем тестовые данные
        if (strpos($initData, 'test_init_data') === 0) {
            $this->validatedData = [
                'user' => [
                    'id' => 12345,
                    'first_name' => 'Test',
                    'username' => 'testuser'
                ]
            ];
            $this->userId = 12345;
            return true;
        }

        $this->validatedData = validateTelegramInitData($initData);
        if ($this->validatedData === false) {
            throw new Exception('Неверные данные пользователя');
        }

        $this->userId = intval($this->validatedData['user']['id']);
        return true;
    }
    
    /**
     * Запись просмотра рекламы и начисление награды
     */
    private function recordAdView() {
        $input = $this->getJsonInput();
        
        if (!isset($input['initData']) || !isset($input['adType'])) {
            throw new Exception('Отсутствуют обязательные параметры');
        }
        
        $this->validateUser($input['initData']);
        $adType = $input['adType'];
        
        // Проверяем валидность типа рекламы
        if (!AdsConfig::isValidAdType($adType)) {
            throw new Exception('Неверный тип рекламы: ' . $adType);
        }
        
        // Логируем запрос
        $this->logAdRequest($this->userId, $adType, AdsConfig::getLogStatus('AD_REQUEST'));
        
        // Загружаем данные пользователей
        $userData = loadUserData();
        if (!is_array($userData)) {
            throw new Exception('Ошибка загрузки данных пользователей');
        }
        
        // Проверяем блокировку пользователя
        if (isset($userData[$this->userId]['blocked']) && $userData[$this->userId]['blocked']) {
            $this->logAdRequest($this->userId, $adType, 'blocked_user');
            throw new Exception('Ваш аккаунт заблокирован');
        }
        
        // Проверяем лимиты
        if (!$this->checkAdViewLimit($this->userId, $adType, $userData)) {
            $this->logAdRequest($this->userId, $adType, AdsConfig::getLogStatus('LIMIT_EXCEEDED'));
            throw new Exception('Превышен лимит просмотров рекламы');
        }
        
        // Получаем награду
        $reward = AdsConfig::getReward($adType);
        
        // Начисляем награду
        $newBalance = increaseUserBalance($this->userId, $reward, $userData);
        if ($newBalance === false) {
            throw new Exception('Ошибка начисления награды');
        }
        
        // Обновляем статистику
        $this->updateUserStats($userData, $this->userId, $reward);
        
        // Начисляем реферальный бонус
        $this->processReferralBonus($userData, $this->userId, $reward);
        
        // Сохраняем данные
        if (!saveUserData($userData)) {
            throw new Exception('Ошибка сохранения данных');
        }
        
        // Обновляем лимиты
        $this->incrementAdLimit($this->userId, $adType);
        
        // Логируем успех
        $this->logAdRequest($this->userId, $adType, AdsConfig::getLogStatus('SUCCESS'));
        
        return $this->successResponse([
            'newBalance' => $newBalance,
            'reward' => $reward,
            'adType' => $adType,
            'timestamp' => time(),
            'message' => 'Награда успешно начислена'
        ]);
    }
    
    /**
     * Получение лимитов пользователя
     */
    private function getLimits() {
        $input = $this->getJsonInput();
        
        if (!isset($input['initData'])) {
            throw new Exception('Отсутствуют данные пользователя');
        }
        
        $this->validateUser($input['initData']);

        // Проверяем блокировку пользователя
        $userData = loadUserData();
        if (is_array($userData) && isset($userData[$this->userId]['blocked']) && $userData[$this->userId]['blocked']) {
            throw new Exception('Ваш аккаунт заблокирован');
        }

        $limitsManager = new AdLimitsManager();
        $limitsInfo = $limitsManager->getUserLimitsInfo($this->userId);
        
        return $this->successResponse([
            'limits' => $limitsInfo,
            'userId' => $this->userId
        ]);
    }
    
    /**
     * Логирование клика по кнопке
     */
    private function logClick() {
        $input = $this->getJsonInput();
        
        if (!isset($input['initData']) || !isset($input['adType']) || !isset($input['clickType'])) {
            throw new Exception('Отсутствуют обязательные параметры');
        }
        
        $this->validateUser($input['initData']);

        // Проверяем блокировку пользователя
        $userData = loadUserData();
        if (is_array($userData) && isset($userData[$this->userId]['blocked']) && $userData[$this->userId]['blocked']) {
            throw new Exception('Ваш аккаунт заблокирован');
        }

        $additionalData = [
            'click_type' => $input['clickType'],
            'timestamp_ms' => $input['timestamp'] ?? time() * 1000,
            'session_id' => $input['sessionId'] ?? 'unknown'
        ];
        
        if (isset($input['reason'])) {
            $additionalData['reason'] = $input['reason'];
        }
        
        $this->logAdRequest($this->userId, $input['adType'], $input['clickType'], $additionalData);
        
        return $this->successResponse([
            'message' => 'Клик зарегистрирован',
            'userId' => $this->userId,
            'adType' => $input['adType'],
            'clickType' => $input['clickType']
        ]);
    }
    
    /**
     * Получение статистики
     */
    private function getStats() {
        // Проверяем права доступа (можно добавить проверку админа)
        $limitsManager = new AdLimitsManager();
        $stats = $limitsManager->getGlobalStats();
        
        return $this->successResponse([
            'stats' => $stats,
            'config' => AdsConfig::getJsConfig()
        ]);
    }
    
    /**
     * Получение конфигурации для фронтенда
     */
    private function getConfig() {
        return $this->successResponse([
            'config' => AdsConfig::getJsConfig()
        ]);
    }
    
    /**
     * Проверка лимитов просмотра рекламы
     */
    private function checkAdViewLimit($userId, $adType, $userData) {
        $limitsManager = new AdLimitsManager();
        return !$limitsManager->isLimitReached($userId, $adType);
    }
    
    /**
     * Увеличение счетчика лимитов
     */
    private function incrementAdLimit($userId, $adType) {
        $limitsManager = new AdLimitsManager();
        return $limitsManager->incrementUserAdCount($userId, $adType);
    }
    
    /**
     * Обновление статистики пользователя
     */
    private function updateUserStats(&$userData, $userId, $reward) {
        // Обновляем общую сумму заработанных монет
        if (!isset($userData[$userId]['total_earned'])) {
            $userData[$userId]['total_earned'] = $reward;
        } else {
            $userData[$userId]['total_earned'] += $reward;
        }
        
        // Обновляем лог просмотров рекламы
        if (!isset($userData[$userId]['ad_views_log'])) {
            $userData[$userId]['ad_views_log'] = [];
        }
        $userData[$userId]['ad_views_log'][] = time();
    }
    
    /**
     * Обработка реферального бонуса
     */
    private function processReferralBonus(&$userData, $userId, $reward) {
        $referrerId = getUserReferrerId($userId, $userData);
        if ($referrerId !== null && isset($userData[$referrerId])) {
            $bonusPercent = defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT : 0.10;
            $bonusAmount = floor($reward * $bonusPercent);
            
            if ($bonusAmount > 0) {
                $newBalanceReferrer = increaseUserBalance($referrerId, $bonusAmount, $userData);
                if ($newBalanceReferrer !== false) {
                    if (!isset($userData[$referrerId]['referral_earnings'])) {
                        $userData[$referrerId]['referral_earnings'] = 0;
                    }
                    $userData[$referrerId]['referral_earnings'] += $bonusAmount;
                }
            }
        }
    }
    
    /**
     * Логирование рекламных запросов
     */
    private function logAdRequest($userId, $adType, $status, $additionalData = []) {
        $logFile = AdsConfig::getDataFile('AD_REQUESTS_LOG');
        $timestamp = time();
        $date = gmdate('Y-m-d H:i:s', $timestamp);
        
        $logEntry = [
            'timestamp' => $timestamp,
            'date' => $date,
            'user_id' => $userId,
            'ad_type' => $adType,
            'status' => $status,
            'ip' => $this->clientIp,
            'user_agent' => $this->userAgent,
            'country' => $this->getCountryByIP($this->clientIp)
        ];
        
        if (!empty($additionalData)) {
            $logEntry = array_merge($logEntry, $additionalData);
        }
        
        $logLine = json_encode($logEntry) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Определение страны по IP (упрощенная версия)
     */
    private function getCountryByIP($ip) {
        // Используем существующую функцию из ad_functions.php если доступна
        if (function_exists('getCountryByIP')) {
            return getCountryByIP($ip);
        }
        
        // Простая проверка для локальных IP
        if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0) {
            return 'Local';
        }
        
        return 'Unknown';
    }
    
    /**
     * Получение JSON данных из запроса
     */
    private function getJsonInput() {
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, true);
        
        if ($input === null) {
            throw new Exception('Неверный JSON в запросе');
        }
        
        return $input;
    }
    
    /**
     * Успешный ответ
     */
    private function successResponse($data) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => time()
        ]);
        exit;
    }
    
    /**
     * Ответ с ошибкой
     */
    private function errorResponse($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => time()
        ]);
        exit;
    }
}

// Подключаем класс лимитов если не подключен
if (!class_exists('AdLimitsManager')) {
    require_once __DIR__ . '/ad_limits_manager.php';
}

// Обработка запроса только если файл вызван напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $api = new AdsAPI();
    $api->handleRequest();
}
?>
