# .htaccess для папки js
# Настройки для корректной работы JavaScript файлов в Telegram мини-приложении

<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"
    
    # Разрешаем CORS для JS файлов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# Разрешаем доступ ко всем JS файлам
<Files "*.js">
    Order Allow,Deny
    Allow from all
</Files>

# Кэширование JS файлов
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
</IfModule>

# Сжатие JS файлов
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Запрещаем просмотр директорий
Options -Indexes
