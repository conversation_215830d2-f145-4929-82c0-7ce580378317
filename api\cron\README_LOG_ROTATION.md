# Система ротации логов рекламной статистики

## Описание
Автоматическая система ротации логов для предотвращения переполнения файла `ad_requests.log` и обеспечения стабильной работы системы статистики.

## Компоненты системы

### 1. `log_rotation.php`
Основной скрипт ротации логов с функциями:
- **Ротация по размеру**: Архивирует лог при достижении 10 МБ
- **Ротация по дате**: Архивирует логи старше 30 дней
- **Сжатие архивов**: Использует gzip для экономии места
- **Очистка**: Удаляет архивы старше 90 дней
- **Логирование**: Ведет собственный лог действий

### 2. `log_archives/` директория
Хранилище архивных файлов с защитой:
- Автоматически создается при первом запуске
- Защищена `.htaccess` от прямого доступа
- Содержит сжатые архивы в формате `.gz`

### 3. Интеграция с cron уведомлений
Ротация автоматически выполняется в составе `send_notifications.php` (ежедневно в 10:00 UTC)

## Настройки ротации

```php
$config = [
    'max_file_size' => 10 * 1024 * 1024, // 10 МБ - максимальный размер лога
    'archive_days' => 30,                 // Архивировать логи старше 30 дней
    'delete_days' => 90,                  // Удалять архивы старше 90 дней
    'log_file' => '../ad_requests.log',   // Путь к основному логу
    'archive_dir' => './log_archives',    // Директория архивов
    'rotation_log' => './log_rotation.log' // Лог ротации
];
```

## Логика работы

### Ротация по размеру
1. Проверяется размер `ad_requests.log`
2. Если размер ≥ 10 МБ:
   - Файл копируется в архив с timestamp
   - Основной лог очищается
   - Архив сжимается в `.gz`

### Ротация по дате
1. Проверяется дата последнего изменения лога
2. Если прошло ≥ 30 дней:
   - Файл перемещается в архив с датой
   - Создается новый пустой лог
   - Архив сжимается

### Очистка старых архивов
1. Сканируются все файлы в `log_archives/`
2. Файлы старше 90 дней удаляются
3. Логируется количество удаленных файлов

## Форматы архивных файлов

```
log_archives/
├── ad_requests_2025-01-15.log.gz      # Архив по дате
├── ad_requests_2025-01-20_14-30-45.log.gz # Архив по размеру
└── ad_requests_2025-02-01.log.gz      # Архив по дате
```

## Мониторинг и логи

### Лог ротации: `log_rotation.log`
```
[2025-01-20 14:30:45] === НАЧАЛО РОТАЦИИ ЛОГОВ ===
[2025-01-20 14:30:45] Создана директория архивов: /path/to/log_archives
[2025-01-20 14:30:45] Ротация по размеру: 10485760 байт -> ad_requests_2025-01-20_14-30-45.log
[2025-01-20 14:30:46] Сжатие: ad_requests_2025-01-20_14-30-45.log -> ad_requests_2025-01-20_14-30-45.log.gz (экономия 85.2%)
[2025-01-20 14:30:46] Ротация завершена. Архивов: 5, размер: 2.3 МБ, удалено: 0
[2025-01-20 14:30:46] === КОНЕЦ РОТАЦИИ ЛОГОВ ===
```

### Лог уведомлений: `notifications.log`
```
[2025-01-20 10:00:15] === НАЧАЛО РОТАЦИИ ЛОГОВ ===
[2025-01-20 10:00:15] Ротация логов выполнена успешно:
[2025-01-20 10:00:15] - Ротация по размеру: Нет
[2025-01-20 10:00:15] - Ротация по дате: Нет
[2025-01-20 10:00:15] - Удалено архивов: 0
[2025-01-20 10:00:15] - Всего архивов: 5
[2025-01-20 10:00:15] - Размер архивов: 2.30 МБ
[2025-01-20 10:00:15] === ЗАВЕРШЕНИЕ РОТАЦИИ ЛОГОВ ===
```

## Ручной запуск

### Тестирование ротации
```bash
php /path/to/api/cron/log_rotation.php
```

### Проверка результата
```bash
# Просмотр лога ротации
tail -f /path/to/api/cron/log_rotation.log

# Просмотр архивов
ls -la /path/to/api/cron/log_archives/

# Проверка размера основного лога
ls -lh /path/to/api/ad_requests.log
```

## Настройка cron (уже интегрировано)

Ротация автоматически выполняется в составе существующего cron-задания:

```bash
# Timeweb hosting cron (уже настроен)
0 10 * * * /usr/bin/php /home/<USER>/public_html/test3/api/cron/send_notifications.php
```

## Преимущества системы

### Производительность
- **Ограниченный размер**: Основной лог никогда не превышает 10 МБ
- **Быстрое чтение**: Статистика загружается мгновенно
- **Сжатие**: Архивы занимают ~15% от исходного размера

### Надежность
- **Автоматическая очистка**: Предотвращает переполнение диска
- **Сохранение истории**: Данные хранятся 90 дней
- **Логирование ошибок**: Все проблемы фиксируются

### Безопасность
- **Защищенные архивы**: Нет прямого доступа через веб
- **Контролируемый доступ**: Только через админ-панель
- **Целостность данных**: Архивы создаются атомарно

## Мониторинг дискового пространства

### Примерные размеры
- **Активный лог**: до 10 МБ
- **Архив за месяц**: ~1-3 МБ (сжатый)
- **Годовой архив**: ~12-36 МБ
- **Максимальное использование**: ~50-100 МБ

### Предупреждения
Система автоматически предотвращает:
- ❌ Переполнение основного лога
- ❌ Накопление старых архивов
- ❌ Потерю производительности
- ❌ Проблемы с чтением статистики

## Восстановление данных

### Из архивов
```bash
# Распаковка архива
gunzip /path/to/log_archives/ad_requests_2025-01-15.log.gz

# Объединение с текущим логом (если нужно)
cat ad_requests_2025-01-15.log >> /path/to/api/ad_requests.log
```

### Анализ архивных данных
Архивы можно анализировать отдельно или временно восстанавливать для получения исторической статистики.

## Устранение неполадок

### Проблема: Ротация не работает
1. Проверьте права доступа к файлам
2. Убедитесь, что директория `log_archives` создана
3. Проверьте лог ротации на ошибки

### Проблема: Архивы не сжимаются
1. Убедитесь, что функция `gzopen` доступна в PHP
2. Проверьте права записи в директорию архивов

### Проблема: Старые архивы не удаляются
1. Проверьте настройку `delete_days` в конфигурации
2. Убедитесь, что cron выполняется регулярно

## Заключение

Система ротации логов обеспечивает:
- ✅ Стабильную работу статистики
- ✅ Контролируемое использование диска
- ✅ Сохранение исторических данных
- ✅ Автоматическое обслуживание

Никаких дополнительных действий от администратора не требуется - система работает полностью автоматически.
