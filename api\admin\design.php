<?php
/**
 * api/admin/design.php
 * Страница управления дизайном административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

// Настройки дизайна по умолчанию
$defaultDesignSettings = [
    'colors' => [
        'primary_dark' => '#1a1a1a',
        'accent_orange' => '#ff6b35',
        'bg_card' => '#2a2a2a',
        'text_primary' => '#ffffff',
        'bg_secondary' => '#333333',
        'border_color' => '#4a4a4a'
    ],
    'effects' => [
        'glitch_speed' => 3,
        'glitch_count' => 3,
        'glitch_opacity' => 0.8,
        'bg_opacity' => 0.8,
        'geometric_size' => 1,
        'enable_glitch' => false,
        'enable_geometric' => true,
        'enable_glitch_lines' => false
    ],
    'theme' => 'cyberpunk' // geometric, cyberpunk, minimal
];

// Файл для хранения настроек дизайна
$designSettingsFile = __DIR__ . '/../../design_settings.json';

// Проверяем, что файл доступен
if (!file_exists($designSettingsFile)) {
    // Создаем файл, если его нет
    $defaultJson = json_encode($defaultDesignSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($designSettingsFile, $defaultJson);
}

// Загрузка текущих настроек
function loadDesignSettings($file, $default) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $settings = json_decode($content, true);
        if ($settings !== null && is_array($settings)) {
            // Правильное слияние массивов
            $result = $default;
            foreach ($settings as $key => $value) {
                if (is_array($value) && isset($result[$key]) && is_array($result[$key])) {
                    $result[$key] = array_merge($result[$key], $value);
                } else {
                    $result[$key] = $value;
                }
            }
            return $result;
        }
    }
    return $default;
}

// Сохранение настроек
function saveDesignSettings($file, $settings) {
    $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($file, $json, LOCK_EX) !== false;
}

// Загружаем текущие настройки
$designSettings = loadDesignSettings($designSettingsFile, $defaultDesignSettings);

// Проверяем, что настройки загружены правильно
if (!isset($designSettings['colors']) || !is_array($designSettings['colors'])) {
    $designSettings = $defaultDesignSettings;
    saveDesignSettings($designSettingsFile, $designSettings);
}

// Отладочная информация
error_log("Design settings loaded: " . print_r($designSettings, true));

// Обработка формы
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'save_design':
                // Обновляем настройки из формы
                $designSettings['colors']['primary_dark'] = $_POST['primary_dark'] ?? $designSettings['colors']['primary_dark'];
                $designSettings['colors']['accent_orange'] = $_POST['accent_orange'] ?? $designSettings['colors']['accent_orange'];
                $designSettings['colors']['bg_card'] = $_POST['bg_card'] ?? $designSettings['colors']['bg_card'];
                $designSettings['colors']['text_primary'] = $_POST['text_primary'] ?? $designSettings['colors']['text_primary'];
                $designSettings['colors']['bg_secondary'] = $_POST['bg_secondary'] ?? $designSettings['colors']['bg_secondary'];
                $designSettings['colors']['border_color'] = $_POST['border_color'] ?? $designSettings['colors']['border_color'];
                $designSettings['colors']['border_color'] = $_POST['border_color'] ?? $designSettings['colors']['border_color'];
                
                $designSettings['effects']['glitch_speed'] = floatval($_POST['glitch_speed'] ?? $designSettings['effects']['glitch_speed']);
                $designSettings['effects']['glitch_count'] = intval($_POST['glitch_count'] ?? $designSettings['effects']['glitch_count']);
                $designSettings['effects']['glitch_opacity'] = floatval($_POST['glitch_opacity'] ?? $designSettings['effects']['glitch_opacity']);
                $designSettings['effects']['bg_opacity'] = floatval($_POST['bg_opacity'] ?? $designSettings['effects']['bg_opacity']);
                $designSettings['effects']['geometric_size'] = floatval($_POST['geometric_size'] ?? $designSettings['effects']['geometric_size']);
                $designSettings['effects']['enable_glitch'] = isset($_POST['enable_glitch']);
                $designSettings['effects']['enable_geometric'] = isset($_POST['enable_geometric']);
                $designSettings['effects']['enable_glitch_lines'] = isset($_POST['enable_glitch_lines']);
                
                $designSettings['theme'] = $_POST['theme'] ?? $designSettings['theme'];
                
                if (saveDesignSettings($designSettingsFile, $designSettings)) {
                    $message = 'Настройки дизайна успешно сохранены!';
                    $messageType = 'success';
                    
                    // Генерируем CSS файл
                    generateDesignCSS($designSettings);
                } else {
                    $message = 'Ошибка при сохранении настроек дизайна';
                    $messageType = 'danger';
                }
                break;
                
            case 'reset_design':
                $designSettings = $defaultDesignSettings;
                if (saveDesignSettings($designSettingsFile, $designSettings)) {
                    $message = 'Настройки дизайна сброшены к умолчанию';
                    $messageType = 'info';
                    generateDesignCSS($designSettings);
                } else {
                    $message = 'Ошибка при сбросе настроек';
                    $messageType = 'danger';
                }
                break;

            case 'force_defaults':
                // Принудительно устанавливаем дефолтные настройки
                $designSettings = $defaultDesignSettings;
                file_put_contents($designSettingsFile, json_encode($designSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                generateDesignCSS($designSettings);
                $message = 'Принудительно установлены настройки по умолчанию';
                $messageType = 'success';
                break;
        }
    }
}

// Вспомогательные функции для работы с цветами
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    return implode(', ', array_map('hexdec', str_split($hex, 2)));
}

function lightenColor($hex, $percent) {
    $hex = ltrim($hex, '#');
    $rgb = array_map('hexdec', str_split($hex, 2));
    foreach ($rgb as &$color) {
        $color = min(255, $color + ($percent * 255 / 100));
    }
    return '#' . implode('', array_map(function($c) { return str_pad(dechex(round($c)), 2, '0', STR_PAD_LEFT); }, $rgb));
}

function darkenColor($hex, $percent) {
    $hex = ltrim($hex, '#');
    $rgb = array_map('hexdec', str_split($hex, 2));
    foreach ($rgb as &$color) {
        $color = max(0, $color - ($percent * 255 / 100));
    }
    return '#' . implode('', array_map(function($c) { return str_pad(dechex(round($c)), 2, '0', STR_PAD_LEFT); }, $rgb));
}

// Функция генерации CSS файла
function generateDesignCSS($settings) {
    $cssFile = __DIR__ . '/../../dynamic-design.css';

    $colors = $settings['colors'];
    $effects = $settings['effects'];

    // Вычисляем дополнительные цвета
    $accentLight = lightenColor($colors['accent_orange'], 20);
    $accentDark = darkenColor($colors['accent_orange'], 20);
    
    $css = "/* Автоматически сгенерированный файл дизайна */\n";
    $css .= ":root {\n";
    $css .= "  --primary-dark: {$colors['primary_dark']};\n";
    $css .= "  --accent-orange: {$colors['accent_orange']};\n";
    $css .= "  --accent-orange-light: {$accentLight};\n";
    $css .= "  --accent-orange-dark: {$accentDark};\n";
    $css .= "  --bg-card: {$colors['bg_card']};\n";
    $css .= "  --text-primary: {$colors['text_primary']};\n";
    $css .= "  --bg-secondary: {$colors['bg_secondary']};\n";
    $css .= "  --border-color: {$colors['border_color']};\n";
    $css .= "  --gradient-orange: linear-gradient(135deg, {$colors['accent_orange']} 0%, {$accentLight} 100%);\n";
    $css .= "}\n\n";

    // Переопределение основных стилей для применения темной темы
    $css .= "/* Переопределение основных стилей */\n";
    $css .= "body {\n";
    $css .= "  background: {$colors['primary_dark']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".app-header {\n";
    $css .= "  background: {$colors['bg_card']} !important;\n";
    $css .= "  border-bottom: 1px solid {$colors['border_color']} !important;\n";
    $css .= "}\n\n";

    $css .= ".app-section, .app-main {\n";
    $css .= "  background-color: transparent !important;\n";
    $css .= "}\n\n";

    $css .= ".status-message {\n";
    $css .= "  background: {$colors['bg_card']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "}\n\n";

    $css .= ".balance-info {\n";
    $css .= "  background: {$colors['accent_orange']} !important;\n";
    $css .= "  color: {$colors['primary_dark']} !important;\n";
    $css .= "}\n\n";

    $css .= ".user-avatar {\n";
    $css .= "  border: 2px solid {$colors['accent_orange']} !important;\n";
    $css .= "  background: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    $css .= ".user-avatar-icon {\n";
    $css .= "  color: {$colors['primary_dark']} !important;\n";
    $css .= "}\n\n";

    $css .= ".app-nav {\n";
    $css .= "  background: {$colors['bg_card']} !important;\n";
    $css .= "  border-top: 1px solid {$colors['border_color']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button {\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button.active {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  background: rgba(" . hexToRgb($colors['accent_orange']) . ", 0.1) !important;\n";
    $css .= "}\n\n";

    // Исправляем иконки заголовков - более специфичные селекторы
    $css .= "h2 svg, h3 svg, h2 > svg, h3 > svg {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "  filter: drop-shadow(0 0 8px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4)) !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  width: 22px !important;\n";
    $css .= "  height: 22px !important;\n";
    $css .= "}\n\n";

    // Переопределяем CSS переменные для иконок
    $css .= ":root {\n";
    $css .= "  --accent-orange: {$colors['accent_orange']} !important;\n";
    $css .= "  --accent-primary: {$colors['accent_orange']} !important;\n";
    $css .= "  --accent-neon: {$colors['accent_orange']} !important;\n";
    $css .= "  --shadow-orange: rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4) !important;\n";
    $css .= "  --shadow-neon: rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4) !important;\n";
    $css .= "}\n\n";

    // Дополнительные стили для иконок с переменными
    $css .= "svg[style*='var(--accent-orange)'], svg[style*='var(--accent-primary)'], svg[style*='var(--accent-neon)'] {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    // Исправляем иконки в табах валют - белые без фона
    $css .= ".currency-tab .tab-icon {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  width: 24px !important;\n";
    $css .= "  height: 24px !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  background: transparent !important;\n";
    $css .= "  filter: none !important;\n";
    $css .= "}\n\n";

    // Ethereum (заполненные пути)
    $css .= ".currency-tab[data-currency='eth'] .tab-icon path {\n";
    $css .= "  fill: #ffffff !important;\n";
    $css .= "  stroke: none !important;\n";
    $css .= "}\n\n";

    // Bitcoin (контурные элементы)
    $css .= ".currency-tab[data-currency='btc'] .tab-icon circle,\n";
    $css .= ".currency-tab[data-currency='btc'] .tab-icon path {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 1.5 !important;\n";
    $css .= "}\n\n";

    // USDT (смешанные элементы)
    $css .= ".currency-tab[data-currency='usdttrc20'] .tab-icon circle {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 1.5 !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab[data-currency='usdttrc20'] .tab-icon path[fill='currentColor'] {\n";
    $css .= "  fill: #ffffff !important;\n";
    $css .= "  stroke: none !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab[data-currency='usdttrc20'] .tab-icon path:not([fill='currentColor']) {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 1.5 !important;\n";
    $css .= "}\n\n";

    // TON (смешанные элементы)
    $css .= ".currency-tab[data-currency='ton'] .tab-icon circle:not([fill='currentColor']) {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 1.5 !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab[data-currency='ton'] .tab-icon circle[fill='currentColor'] {\n";
    $css .= "  fill: rgba(255, 255, 255, 0.3) !important;\n";
    $css .= "  stroke: none !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab[data-currency='ton'] .tab-icon path {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 1.5 !important;\n";
    $css .= "}\n\n";

    // Исправляем иконки в кнопках рекламы
    $css .= ".action-button .button-icon {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "  width: 20px !important;\n";
    $css .= "  height: 20px !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  margin-right: 8px !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button .button-icon path,\n";
    $css .= ".action-button .button-icon polygon,\n";
    $css .= ".action-button .button-icon rect,\n";
    $css .= ".action-button .button-icon line {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "}\n\n";

    // Дополнительная фиксация для табов валют - убираем фон
    $css .= ".currency-tab {\n";
    $css .= "  background: transparent !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab.active {\n";
    $css .= "  background: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    // Принудительно убираем фон у всех SVG в табах
    $css .= ".currency-tab .tab-icon,\n";
    $css .= ".currency-tab .tab-icon *,\n";
    $css .= ".currency-tab svg,\n";
    $css .= ".currency-tab svg * {\n";
    $css .= "  background: transparent !important;\n";
    $css .= "  background-color: transparent !important;\n";
    $css .= "}\n\n";

    // Защита хардкодных иконок от JS
    $css .= ".hardcore-icon {\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "  user-select: none !important;\n";
    $css .= "  -webkit-user-select: none !important;\n";
    $css .= "  -moz-user-select: none !important;\n";
    $css .= "  -ms-user-select: none !important;\n";
    $css .= "}\n\n";

    $css .= ".hardcore-icon,\n";
    $css .= ".hardcore-icon *,\n";
    $css .= ".hardcore-icon path,\n";
    $css .= ".hardcore-icon polygon,\n";
    $css .= ".hardcore-icon rect,\n";
    $css .= ".hardcore-icon line {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "}\n\n";

    // Дополнительная защита для кнопок с хардкодными иконками
    $css .= ".action-button .hardcore-icon {\n";
    $css .= "  width: 20px !important;\n";
    $css .= "  height: 20px !important;\n";
    $css .= "  margin-right: 8px !important;\n";
    $css .= "}\n\n";

    $css .= ".button-text {\n";
    $css .= "  color: inherit !important;\n";
    $css .= "  font-size: inherit !important;\n";
    $css .= "}\n\n";

    // === ЖИВОЙ АНИМИРОВАННЫЙ ФОН ===
    $css .= "body {\n";
    $css .= "  position: relative !important;\n";
    $css .= "  overflow-x: hidden !important;\n";
    $css .= "}\n\n";

    $css .= "body::before {\n";
    $css .= "  content: '' !important;\n";
    $css .= "  position: fixed !important;\n";
    $css .= "  top: 0 !important;\n";
    $css .= "  left: 0 !important;\n";
    $css .= "  width: 100% !important;\n";
    $css .= "  height: 100% !important;\n";
    $css .= "  background: \n";
    $css .= "    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.12) 0%, transparent 50%),\n";
    $css .= "    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.08) 0%, transparent 50%),\n";
    $css .= "    radial-gradient(circle at 40% 40%, rgba(255, 107, 53, 0.06) 0%, transparent 50%),\n";
    $css .= "    radial-gradient(circle at 60% 70%, rgba(255, 107, 53, 0.04) 0%, transparent 50%) !important;\n";
    $css .= "  z-index: -2 !important;\n";
    $css .= "  animation: backgroundPulse 12s ease-in-out infinite !important;\n";
    $css .= "}\n\n";

    $css .= "body::after {\n";
    $css .= "  content: '' !important;\n";
    $css .= "  position: fixed !important;\n";
    $css .= "  top: -50% !important;\n";
    $css .= "  left: -50% !important;\n";
    $css .= "  width: 200% !important;\n";
    $css .= "  height: 200% !important;\n";
    $css .= "  background: \n";
    $css .= "    radial-gradient(circle, rgba(255, 107, 53, 0.04) 2px, transparent 2px),\n";
    $css .= "    radial-gradient(circle, rgba(255, 107, 53, 0.02) 1px, transparent 1px) !important;\n";
    $css .= "  background-size: 80px 80px, 40px 40px !important;\n";
    $css .= "  background-position: 0 0, 20px 20px !important;\n";
    $css .= "  z-index: -1 !important;\n";
    $css .= "  animation: floatingDots 25s linear infinite !important;\n";
    $css .= "}\n\n";

    $css .= "@keyframes backgroundPulse {\n";
    $css .= "  0%, 100% { \n";
    $css .= "    transform: scale(1) rotate(0deg); \n";
    $css .= "    opacity: 0.7; \n";
    $css .= "  }\n";
    $css .= "  25% { \n";
    $css .= "    transform: scale(1.08) rotate(0.5deg); \n";
    $css .= "    opacity: 0.9; \n";
    $css .= "  }\n";
    $css .= "  50% { \n";
    $css .= "    transform: scale(0.95) rotate(-0.5deg); \n";
    $css .= "    opacity: 0.5; \n";
    $css .= "  }\n";
    $css .= "  75% { \n";
    $css .= "    transform: scale(1.03) rotate(0.3deg); \n";
    $css .= "    opacity: 0.8; \n";
    $css .= "  }\n";
    $css .= "}\n\n";

    $css .= "@keyframes floatingDots {\n";
    $css .= "  0% { \n";
    $css .= "    transform: translate(0, 0) rotate(0deg) scale(1); \n";
    $css .= "  }\n";
    $css .= "  25% { \n";
    $css .= "    transform: translate(-15px, -10px) rotate(90deg) scale(1.1); \n";
    $css .= "  }\n";
    $css .= "  50% { \n";
    $css .= "    transform: translate(10px, -8px) rotate(180deg) scale(0.9); \n";
    $css .= "  }\n";
    $css .= "  75% { \n";
    $css .= "    transform: translate(-8px, 12px) rotate(270deg) scale(1.05); \n";
    $css .= "  }\n";
    $css .= "  100% { \n";
    $css .= "    transform: translate(0, 0) rotate(360deg) scale(1); \n";
    $css .= "  }\n";
    $css .= "}\n\n";

    // Дополнительная защита от JS манипуляций
    $css .= ".hardcore-icon[data-icon] {\n";
    $css .= "  position: relative !important;\n";
    $css .= "  z-index: 999 !important;\n";
    $css .= "}\n\n";

    $css .= ".hardcore-icon[data-icon]:before {\n";
    $css .= "  content: attr(data-icon) !important;\n";
    $css .= "  position: absolute !important;\n";
    $css .= "  top: -9999px !important;\n";
    $css .= "  left: -9999px !important;\n";
    $css .= "  opacity: 0 !important;\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "}\n\n";

    // Принудительная фиксация кнопок рекламы
    $css .= "#openLinkButton .hardcore-icon,\n";
    $css .= "#watchVideoButton .hardcore-icon,\n";
    $css .= "#openAdButton .hardcore-icon {\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  width: 20px !important;\n";
    $css .= "  height: 20px !important;\n";
    $css .= "  margin-right: 8px !important;\n";
    $css .= "  vertical-align: middle !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton,\n";
    $css .= "#watchVideoButton,\n";
    $css .= "#openAdButton {\n";
    $css .= "  display: flex !important;\n";
    $css .= "  align-items: center !important;\n";
    $css .= "  justify-content: flex-start !important;\n";
    $css .= "  gap: 12px !important;\n";
    $css .= "  padding: 16px 20px !important;\n";
    $css .= "  position: relative !important;\n";
    $css .= "  overflow: hidden !important;\n";
    $css .= "  background: linear-gradient(145deg, {$colors['accent_orange']}, " . darkenColor($colors['accent_orange'], 15) . ") !important;\n";
    $css .= "  border: none !important;\n";
    $css .= "  border-radius: 16px !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 8px 16px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3),\n";
    $css .= "    0 4px 8px rgba(0, 0, 0, 0.2),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.2),\n";
    $css .= "    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;\n";
    $css .= "  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n";
    $css .= "  transform: translateY(0) !important;\n";
    $css .= "  font-weight: 600 !important;\n";
    $css .= "  font-size: 15px !important;\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;\n";
    $css .= "  cursor: pointer !important;\n";
    $css .= "}\n\n";

    // === ПРИНУДИТЕЛЬНАЯ ВИДИМОСТЬ ИКОНОК В ГЛАВНЫХ КНОПКАХ ===
    $css .= "#openLinkButton .hardcore-icon,\n";
    $css .= "#watchVideoButton .hardcore-icon,\n";
    $css .= "#openAdButton .hardcore-icon {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "  width: 20px !important;\n";
    $css .= "  height: 20px !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  margin-right: 0 !important;\n";
    $css .= "  flex-shrink: 0 !important;\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "  user-select: none !important;\n";
    $css .= "  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton .hardcore-icon path,\n";
    $css .= "#watchVideoButton .hardcore-icon path,\n";
    $css .= "#openAdButton .hardcore-icon path,\n";
    $css .= "#openLinkButton .hardcore-icon polygon,\n";
    $css .= "#watchVideoButton .hardcore-icon polygon,\n";
    $css .= "#openAdButton .hardcore-icon polygon,\n";
    $css .= "#openLinkButton .hardcore-icon rect,\n";
    $css .= "#watchVideoButton .hardcore-icon rect,\n";
    $css .= "#openAdButton .hardcore-icon rect,\n";
    $css .= "#openLinkButton .hardcore-icon line,\n";
    $css .= "#watchVideoButton .hardcore-icon line,\n";
    $css .= "#openAdButton .hardcore-icon line {\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton .button-text,\n";
    $css .= "#watchVideoButton .button-text,\n";
    $css .= "#openAdButton .button-text {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  font-weight: 600 !important;\n";
    $css .= "  font-size: 15px !important;\n";
    $css .= "  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;\n";
    $css .= "  flex: 1 !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton:hover,\n";
    $css .= "#watchVideoButton:hover,\n";
    $css .= "#openAdButton:hover {\n";
    $css .= "  transform: translateY(-2px) scale(1.02) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 12px 24px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4),\n";
    $css .= "    0 6px 12px rgba(0, 0, 0, 0.3),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.3),\n";
    $css .= "    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;\n";
    $css .= "  background: linear-gradient(145deg, " . lightenColor($colors['accent_orange'], 10) . ", {$colors['accent_orange']}) !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton:active,\n";
    $css .= "#watchVideoButton:active,\n";
    $css .= "#openAdButton:active {\n";
    $css .= "  transform: translateY(1px) scale(0.98) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 4px 8px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3),\n";
    $css .= "    0 2px 4px rgba(0, 0, 0, 0.2),\n";
    $css .= "    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton::before,\n";
    $css .= "#watchVideoButton::before,\n";
    $css .= "#openAdButton::before {\n";
    $css .= "  content: '' !important;\n";
    $css .= "  position: absolute !important;\n";
    $css .= "  top: 0 !important;\n";
    $css .= "  left: -100% !important;\n";
    $css .= "  width: 100% !important;\n";
    $css .= "  height: 100% !important;\n";
    $css .= "  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;\n";
    $css .= "  transition: left 0.6s ease !important;\n";
    $css .= "}\n\n";

    $css .= "#openLinkButton:hover::before,\n";
    $css .= "#watchVideoButton:hover::before,\n";
    $css .= "#openAdButton:hover::before {\n";
    $css .= "  left: 100% !important;\n";
    $css .= "}\n\n";

    // === ЕДИНЫЙ ДИЗАЙН КАРТОЧЕК ===
    $css .= ".history-item,\n";
    $css .= ".withdrawal-item,\n";
    $css .= ".earn-block,\n";
    $css .= ".friends-block,\n";
    $css .= ".currency-info-card {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['bg_card']}, " . darkenColor($colors['bg_card'], 5) . ") !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "  border-top: 3px solid {$colors['accent_orange']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  padding: 20px !important;\n";
    $css .= "  margin-bottom: 16px !important;\n";
    $css .= "  border-radius: 16px !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 8px 16px rgba(0, 0, 0, 0.2),\n";
    $css .= "    0 4px 8px rgba(0, 0, 0, 0.1),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;\n";
    $css .= "  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n";
    $css .= "  position: relative !important;\n";
    $css .= "  overflow: hidden !important;\n";
    $css .= "}\n\n";

    $css .= ".history-item:hover,\n";
    $css .= ".withdrawal-item:hover,\n";
    $css .= ".earn-block:hover,\n";
    $css .= ".friends-block:hover,\n";
    $css .= ".currency-info-card:hover {\n";
    $css .= "  transform: translateY(-2px) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 12px 24px rgba(0, 0, 0, 0.3),\n";
    $css .= "    0 6px 12px rgba(0, 0, 0, 0.15),\n";
    $css .= "    0 0 0 1px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.2),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;\n";
    $css .= "}\n\n";

    $css .= ".history-item::before,\n";
    $css .= ".withdrawal-item::before,\n";
    $css .= ".earn-block::before,\n";
    $css .= ".friends-block::before,\n";
    $css .= ".currency-info-card::before {\n";
    $css .= "  content: '' !important;\n";
    $css .= "  position: absolute !important;\n";
    $css .= "  top: 0 !important;\n";
    $css .= "  left: 0 !important;\n";
    $css .= "  right: 0 !important;\n";
    $css .= "  height: 3px !important;\n";
    $css .= "  background: linear-gradient(90deg, {$colors['accent_orange']}, " . lightenColor($colors['accent_orange'], 20) . ", {$colors['accent_orange']}) !important;\n";
    $css .= "  border-radius: 16px 16px 0 0 !important;\n";
    $css .= "}\n\n";

    $css .= ".history-item svg,\n";
    $css .= ".withdrawal-item svg,\n";
    $css .= ".earn-block svg,\n";
    $css .= ".friends-block svg,\n";
    $css .= ".currency-info-card svg {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    // Принудительная фиксация иконок заголовков
    $css .= "h2, h3 {\n";
    $css .= "  position: relative !important;\n";
    $css .= "}\n\n";

    $css .= "h2 svg, h3 svg {\n";
    $css .= "  position: static !important;\n";
    $css .= "  z-index: 10 !important;\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "  animation: none !important;\n";
    $css .= "  transition: none !important;\n";
    $css .= "}\n\n";

    // Дополнительная фиксация для всех SVG иконок
    $css .= "svg {\n";
    $css .= "  animation: none !important;\n";
    $css .= "}\n\n";

    // Переопределяем стили для конкретных элементов
    $css .= "#tasks-title svg, #earnings-title svg, #friends-title svg {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "  filter: drop-shadow(0 0 8px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4)) !important;\n";
    $css .= "  width: 28px !important;\n";
    $css .= "  height: 28px !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "}\n\n";

    // Принудительно показываем все заголовки
    $css .= "#tasks-title, #earnings-title, #friends-title {\n";
    $css .= "  display: flex !important;\n";
    $css .= "  align-items: center !important;\n";
    $css .= "  justify-content: center !important;\n";
    $css .= "  gap: 12px !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "}\n\n";

    // Исправляем все подзаголовки h3
    $css .= "#balance-title svg, #calculator-title svg, #withdrawal-title svg, #history-title svg,\n";
    $css .= "#share-title svg, #invite-title svg, #stats-title svg, #subscriptions-title svg {\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "  filter: drop-shadow(0 0 6px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4)) !important;\n";
    $css .= "  width: 22px !important;\n";
    $css .= "  height: 22px !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "}\n\n";

    // Исправляем стили заголовков
    $css .= "h2[style*='display: flex'], h3[style*='display: flex'] {\n";
    $css .= "  display: flex !important;\n";
    $css .= "  align-items: center !important;\n";
    $css .= "  gap: 10px !important;\n";
    $css .= "}\n\n";

    // Исправляем кнопку "Обновить историю"
    $css .= ".refresh-history-btn {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['accent_orange']}, {$accentDark}) !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border: none !important;\n";
    $css .= "  border-radius: 8px !important;\n";
    $css .= "  padding: 8px 16px !important;\n";
    $css .= "  font-size: 14px !important;\n";
    $css .= "  cursor: pointer !important;\n";
    $css .= "  transition: all 0.3s ease !important;\n";
    $css .= "  box-shadow: 0 4px 8px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3) !important;\n";
    $css .= "}\n\n";

    $css .= ".refresh-history-btn:hover {\n";
    $css .= "  background: linear-gradient(145deg, #ff9e68, {$colors['accent_orange']}) !important;\n";
    $css .= "  transform: translateY(-2px) !important;\n";
    $css .= "  box-shadow: 0 6px 12px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4) !important;\n";
    $css .= "}\n\n";

    // Исправляем желтую кнопку в навигации
    $css .= ".nav-button {\n";
    $css .= "  background: transparent !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button:hover {\n";
    $css .= "  background: rgba(" . hexToRgb($colors['accent_orange']) . ", 0.1) !important;\n";
    $css .= "  color: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button.active {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['accent_orange']}, {$accentDark}) !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border-radius: 18px !important;\n";
    $css .= "  box-shadow: 0 8px 16px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4) !important;\n";
    $css .= "  transform: translateY(-2px) !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button .cyber-icon {\n";
    $css .= "  fill: {$colors['text_primary']} !important;\n";
    $css .= "  stroke: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button:hover .cyber-icon {\n";
    $css .= "  fill: {$colors['accent_orange']} !important;\n";
    $css .= "  stroke: {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    $css .= ".nav-button.active .cyber-icon {\n";
    $css .= "  fill: {$colors['text_primary']} !important;\n";
    $css .= "  stroke: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    // === ОБЪЕМНЫЕ КНОПКИ ДЕЙСТВИЙ ===
    $css .= ".action-button:not(#openLinkButton):not(#watchVideoButton):not(#openAdButton) {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['bg_secondary']}, " . darkenColor($colors['bg_secondary'], 10) . ") !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "  border-radius: 12px !important;\n";
    $css .= "  padding: 12px 20px !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  font-weight: 600 !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 6px 12px rgba(0, 0, 0, 0.2),\n";
    $css .= "    0 3px 6px rgba(0, 0, 0, 0.1),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.1),\n";
    $css .= "    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;\n";
    $css .= "  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n";
    $css .= "  position: relative !important;\n";
    $css .= "  overflow: hidden !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button:not(#openLinkButton):not(#watchVideoButton):not(#openAdButton):hover {\n";
    $css .= "  transform: translateY(-1px) !important;\n";
    $css .= "  background: linear-gradient(145deg, " . lightenColor($colors['bg_secondary'], 5) . ", {$colors['bg_secondary']}) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 8px 16px rgba(0, 0, 0, 0.25),\n";
    $css .= "    0 4px 8px rgba(0, 0, 0, 0.15),\n";
    $css .= "    0 0 0 1px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button:not(#openLinkButton):not(#watchVideoButton):not(#openAdButton):active {\n";
    $css .= "  transform: translateY(1px) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 3px 6px rgba(0, 0, 0, 0.2),\n";
    $css .= "    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;\n";
    $css .= "}\n\n";

    // === КНОПКИ КОПИРОВАНИЯ ===
    $css .= ".copy-button,\n";
    $css .= "#copy-referral-button {\n";
    $css .= "  background: linear-gradient(145deg, #4CAF50, #45a049) !important;\n";
    $css .= "  border: none !important;\n";
    $css .= "  border-radius: 10px !important;\n";
    $css .= "  padding: 12px !important;\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 6px 12px rgba(76, 175, 80, 0.3),\n";
    $css .= "    0 3px 6px rgba(0, 0, 0, 0.2),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.3),\n";
    $css .= "    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;\n";
    $css .= "  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n";
    $css .= "  cursor: pointer !important;\n";
    $css .= "  min-width: 44px !important;\n";
    $css .= "  min-height: 44px !important;\n";
    $css .= "}\n\n";

    $css .= ".copy-button:hover,\n";
    $css .= "#copy-referral-button:hover {\n";
    $css .= "  background: linear-gradient(145deg, #5CBF60, #4CAF50) !important;\n";
    $css .= "  transform: translateY(-2px) scale(1.05) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 8px 16px rgba(76, 175, 80, 0.4),\n";
    $css .= "    0 4px 8px rgba(0, 0, 0, 0.25),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;\n";
    $css .= "}\n\n";

    $css .= ".copy-button:active,\n";
    $css .= "#copy-referral-button:active {\n";
    $css .= "  transform: translateY(1px) scale(0.95) !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 3px 6px rgba(76, 175, 80, 0.3),\n";
    $css .= "    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;\n";
    $css .= "}\n\n";

    $css .= ".copy-button .copy-icon,\n";
    $css .= "#copy-referral-button .copy-icon {\n";
    $css .= "  color: #ffffff !important;\n";
    $css .= "  fill: none !important;\n";
    $css .= "  stroke: #ffffff !important;\n";
    $css .= "  stroke-width: 2 !important;\n";
    $css .= "  width: 20px !important;\n";
    $css .= "  height: 20px !important;\n";
    $css .= "  display: block !important;\n";
    $css .= "  margin: 0 auto !important;\n";
    $css .= "  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;\n";
    $css .= "}\n\n";

    $css .= ".copy-button:disabled,\n";
    $css .= "#copy-referral-button:disabled {\n";
    $css .= "  background: linear-gradient(145deg, #9E9E9E, #757575) !important;\n";
    $css .= "  cursor: not-allowed !important;\n";
    $css .= "  transform: none !important;\n";
    $css .= "  box-shadow: \n";
    $css .= "    0 2px 4px rgba(0, 0, 0, 0.2),\n";
    $css .= "    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;\n";
    $css .= "}\n\n";

    $css .= ".copy-button:disabled .copy-icon,\n";
    $css .= "#copy-referral-button:disabled .copy-icon {\n";
    $css .= "  color: #BDBDBD !important;\n";
    $css .= "  stroke: #BDBDBD !important;\n";
    $css .= "  opacity: 0.6 !important;\n";
    $css .= "}\n\n";

    // === ГЛОБАЛЬНАЯ ЗАЩИТА ВСЕХ ИКОНОК ===
    $css .= "h2 svg, h3 svg, h4 svg {\n";
    $css .= "  position: static !important;\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "  user-select: none !important;\n";
    $css .= "  z-index: 10 !important;\n";
    $css .= "  animation: none !important;\n";
    $css .= "  transition: none !important;\n";
    $css .= "}\n\n";

    $css .= "h2 .text-content, h3 .text-content, h4 .text-content {\n";
    $css .= "  display: inline !important;\n";
    $css .= "  margin-left: 8px !important;\n";
    $css .= "}\n\n";

    $css .= "h2, h3, h4 {\n";
    $css .= "  display: flex !important;\n";
    $css .= "  align-items: center !important;\n";
    $css .= "  justify-content: center !important;\n";
    $css .= "  gap: 8px !important;\n";
    $css .= "}\n\n";

    // === ПРИНУДИТЕЛЬНАЯ ВИДИМОСТЬ ИКОНОК ===
    $css .= "svg {\n";
    $css .= "  animation: none !important;\n";
    $css .= "  transition: none !important;\n";
    $css .= "}\n\n";

    $css .= ".hardcore-icon,\n";
    $css .= ".tab-icon,\n";
    $css .= ".button-icon,\n";
    $css .= ".copy-icon,\n";
    $css .= ".refresh-icon {\n";
    $css .= "  display: inline-block !important;\n";
    $css .= "  visibility: visible !important;\n";
    $css .= "  opacity: 1 !important;\n";
    $css .= "  pointer-events: none !important;\n";
    $css .= "  user-select: none !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tabs-container {\n";
    $css .= "  background: {$colors['bg_card']} !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "  border-top: 3px solid {$colors['accent_orange']} !important;\n";
    $css .= "}\n\n";

    $css .= ".calculator-header {\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".amount-input-section {\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".amount-input-section label {\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".withdrawal-form {\n";
    $css .= "  background: {$colors['bg_card']} !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "  border-top: 3px solid {$colors['accent_orange']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "}\n\n";

    $css .= ".withdrawal-form label {\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  margin-bottom: 8px !important;\n";
    $css .= "  display: block !important;\n";
    $css .= "  font-weight: 500 !important;\n";
    $css .= "}\n\n";

    // Исправляем отступы в форме вывода
    $css .= ".withdrawal-form {\n";
    $css .= "  padding: 20px !important;\n";
    $css .= "}\n\n";

    $css .= ".withdrawal-form > * {\n";
    $css .= "  margin-bottom: 16px !important;\n";
    $css .= "}\n\n";

    $css .= ".withdrawal-form select, .withdrawal-form input {\n";
    $css .= "  width: 100% !important;\n";
    $css .= "  padding: 12px !important;\n";
    $css .= "  border-radius: 8px !important;\n";
    $css .= "  margin-bottom: 16px !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab {\n";
    $css .= "  background: {$colors['bg_secondary']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "}\n\n";

    $css .= ".currency-tab.active {\n";
    $css .= "  background: {$colors['accent_orange']} !important;\n";
    $css .= "  color: {$colors['primary_dark']} !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['accent_orange']}, {$accentDark}) !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border: none !important;\n";
    $css .= "  box-shadow: 0 12px 24px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4), 0 6px 12px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3) !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button:hover {\n";
    $css .= "  background: linear-gradient(145deg, {$accentLight}, {$colors['accent_orange']}) !important;\n";
    $css .= "  box-shadow: 0 16px 32px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.5), 0 8px 16px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4) !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button:active {\n";
    $css .= "  background: linear-gradient(145deg, {$accentDark}, {$colors['accent_orange']}) !important;\n";
    $css .= "  box-shadow: 0 6px 12px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3), 0 3px 6px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.2) !important;\n";
    $css .= "}\n\n";

    $css .= ".action-button.purple-button, .action-button.orange-button, .action-button.yellow-button {\n";
    $css .= "  background: linear-gradient(145deg, {$colors['accent_orange']}, {$accentDark}) !important;\n";
    $css .= "  box-shadow: 0 12px 24px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.4), 0 6px 12px rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3) !important;\n";
    $css .= "}\n\n";

    $css .= "input, select, textarea {\n";
    $css .= "  background: {$colors['bg_secondary']} !important;\n";
    $css .= "  color: {$colors['text_primary']} !important;\n";
    $css .= "  border: 1px solid {$colors['border_color']} !important;\n";
    $css .= "}\n\n";

    $css .= "input:focus, select:focus, textarea:focus {\n";
    $css .= "  border-color: {$colors['accent_orange']} !important;\n";
    $css .= "  box-shadow: 0 0 0 0.2rem rgba(" . hexToRgb($colors['accent_orange']) . ", 0.25) !important;\n";
    $css .= "}\n\n";
    
    if ($effects['enable_glitch']) {
        $css .= ".glitch-line { animation-duration: {$effects['glitch_speed']}s; opacity: {$effects['glitch_opacity']}; }\n";
    } else {
        $css .= ".glitch-line { display: none; }\n";
    }

    if ($effects['enable_glitch_lines']) {
        $css .= ".app-container::before { display: block; }\n";
    } else {
        $css .= ".app-container::before { display: none !important; }\n";
    }
    
    if ($effects['enable_geometric']) {
        $css .= "/* Геометрический фон */\n";
        $css .= "body::before {\n";
        $css .= "  content: '' !important;\n";
        $css .= "  position: fixed !important;\n";
        $css .= "  top: 0 !important;\n";
        $css .= "  left: 0 !important;\n";
        $css .= "  width: 100% !important;\n";
        $css .= "  height: 100% !important;\n";
        $css .= "  background: linear-gradient(135deg, transparent 0%, transparent 40%, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3) 40%, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.3) 60%, transparent 60%), linear-gradient(-45deg, transparent 0%, transparent 70%, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.1) 70%, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.1) 85%, transparent 85%), repeating-linear-gradient(45deg, transparent, transparent 50px, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.05) 50px, rgba(" . hexToRgb($colors['accent_orange']) . ", 0.05) 52px) !important;\n";
        $css .= "  z-index: -2 !important;\n";
        $css .= "  opacity: {$effects['bg_opacity']} !important;\n";
        $css .= "  transform: scale({$effects['geometric_size']}) !important;\n";
        $css .= "  display: block !important;\n";
        $css .= "}\n\n";

        $css .= "body::after {\n";
        $css .= "  content: '' !important;\n";
        $css .= "  position: fixed !important;\n";
        $css .= "  top: -50% !important;\n";
        $css .= "  right: -20% !important;\n";
        $css .= "  width: 80% !important;\n";
        $css .= "  height: 200% !important;\n";
        $css .= "  background: linear-gradient(45deg, transparent 0%, transparent 30%, {$colors['accent_orange']} 30%, {$colors['accent_orange']} 32%, transparent 32%) !important;\n";
        $css .= "  transform: rotate(15deg) scale({$effects['geometric_size']}) !important;\n";
        $css .= "  z-index: -1 !important;\n";
        $css .= "  opacity: " . ($effects['bg_opacity'] * 0.3) . " !important;\n";
        $css .= "  animation: geometricFloat 20s ease-in-out infinite !important;\n";
        $css .= "  display: block !important;\n";
        $css .= "}\n\n";

        $css .= "@keyframes geometricFloat {\n";
        $css .= "  0%, 100% { transform: rotate(15deg) translateY(0px) scale({$effects['geometric_size']}); }\n";
        $css .= "  50% { transform: rotate(15deg) translateY(-20px) scale({$effects['geometric_size']}); }\n";
        $css .= "}\n\n";
    } else {
        $css .= "body::before, body::after { display: none !important; }\n";
    }
    
    file_put_contents($cssFile, $css);
}



// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🎨 Управление дизайном</h1>
            </div>
            
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>


            
            <div class="row">
                <div class="col-lg-8">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="save_design">
                        
                        <!-- Основные цвета -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🎯 Основные цвета</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="primary_dark" class="form-label">Основной темный цвет</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="primary_dark" name="primary_dark" value="<?php echo $designSettings['colors']['primary_dark']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['primary_dark']; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="accent_orange" class="form-label">Акцентный оранжевый</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="accent_orange" name="accent_orange" value="<?php echo $designSettings['colors']['accent_orange']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['accent_orange']; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="bg_card" class="form-label">Фон карточек</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="bg_card" name="bg_card" value="<?php echo $designSettings['colors']['bg_card']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['bg_card']; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="text_primary" class="form-label">Основной текст</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="text_primary" name="text_primary" value="<?php echo $designSettings['colors']['text_primary']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['text_primary']; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="bg_secondary" class="form-label">Вторичный фон</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="bg_secondary" name="bg_secondary" value="<?php echo $designSettings['colors']['bg_secondary']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['bg_secondary']; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="border_color" class="form-label">Цвет границ</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="border_color" name="border_color" value="<?php echo $designSettings['colors']['border_color']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $designSettings['colors']['border_color']; ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Эффекты -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">⚡ Эффекты и анимации</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="glitch_speed" class="form-label">Скорость глитч-эффектов (сек)</label>
                                        <input type="range" class="form-range" id="glitch_speed" name="glitch_speed" min="1" max="10" step="0.5" value="<?php echo $designSettings['effects']['glitch_speed']; ?>">
                                        <div class="form-text">Текущее значение: <span id="glitch_speed_value"><?php echo $designSettings['effects']['glitch_speed']; ?>s</span></div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="glitch_count" class="form-label">Количество глитч-линий</label>
                                        <input type="range" class="form-range" id="glitch_count" name="glitch_count" min="1" max="8" value="<?php echo $designSettings['effects']['glitch_count']; ?>">
                                        <div class="form-text">Текущее значение: <span id="glitch_count_value"><?php echo $designSettings['effects']['glitch_count']; ?></span></div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="glitch_opacity" class="form-label">Прозрачность глитч-эффектов</label>
                                        <input type="range" class="form-range" id="glitch_opacity" name="glitch_opacity" min="0.1" max="1" step="0.1" value="<?php echo $designSettings['effects']['glitch_opacity']; ?>">
                                        <div class="form-text">Текущее значение: <span id="glitch_opacity_value"><?php echo round($designSettings['effects']['glitch_opacity'] * 100); ?>%</span></div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="bg_opacity" class="form-label">Прозрачность фона</label>
                                        <input type="range" class="form-range" id="bg_opacity" name="bg_opacity" min="0.1" max="1" step="0.1" value="<?php echo $designSettings['effects']['bg_opacity']; ?>">
                                        <div class="form-text">Текущее значение: <span id="bg_opacity_value"><?php echo round($designSettings['effects']['bg_opacity'] * 100); ?>%</span></div>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_glitch" name="enable_glitch" <?php echo $designSettings['effects']['enable_glitch'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_glitch">
                                                Включить глитч-эффекты
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_glitch_lines" name="enable_glitch_lines" <?php echo ($designSettings['effects']['enable_glitch_lines'] ?? false) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_glitch_lines">
                                                Включить глитч-линии в верхней части
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enable_geometric" name="enable_geometric" <?php echo $designSettings['effects']['enable_geometric'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_geometric">
                                                Включить геометрические элементы
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Тема -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">🎭 Тема оформления</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="theme" id="theme_geometric" value="geometric" <?php echo $designSettings['theme'] === 'geometric' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="theme_geometric">
                                                <strong>Геометрическая</strong><br>
                                                <small class="text-muted">Современный дизайн с геометрическими формами</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="theme" id="theme_cyberpunk" value="cyberpunk" <?php echo $designSettings['theme'] === 'cyberpunk' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="theme_cyberpunk">
                                                <strong>Киберпанк</strong><br>
                                                <small class="text-muted">Футуристический стиль с неоновыми эффектами</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="theme" id="theme_minimal" value="minimal" <?php echo $designSettings['theme'] === 'minimal' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="theme_minimal">
                                                <strong>Минимализм</strong><br>
                                                <small class="text-muted">Чистый и простой дизайн</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Кнопки управления -->
                        <div class="d-flex gap-2 mb-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>Сохранить настройки
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="previewChanges()">
                                <i class="bi bi-eye me-2"></i>Предварительный просмотр
                            </button>
                        </div>
                    </form>

                    <!-- Форма сброса -->
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="action" value="reset_design">
                        <button type="submit" class="btn btn-outline-danger" onclick="return confirm('Вы уверены, что хотите сбросить все настройки к умолчанию?')">
                            <i class="bi bi-arrow-clockwise me-2"></i>Сбросить к умолчанию
                        </button>
                    </form>

                    <!-- Принудительный сброс -->
                    <form method="POST" class="d-inline ms-2">
                        <input type="hidden" name="action" value="force_defaults">
                        <button type="submit" class="btn btn-warning" onclick="return confirm('Принудительно установить настройки по умолчанию?')">
                            <i class="bi bi-exclamation-triangle me-2"></i>Принудительный сброс
                        </button>
                    </form>
                </div>

                <!-- Превью -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">👀 Превью</h5>
                        </div>
                        <div class="card-body">
                            <div id="preview-container" style="background: var(--primary-dark, #2a2a2a); padding: 20px; border-radius: 8px; color: var(--text-primary, #ffffff);">
                                <div style="background: var(--bg-card, #3a3a3a); padding: 15px; border-radius: 8px; margin-bottom: 15px; border-top: 3px solid var(--accent-orange, #ff6b35);">
                                    <h6>Пример карточки</h6>
                                    <p class="mb-2">Текст в карточке с новыми цветами</p>
                                    <button style="background: var(--accent-orange, #ff6b35); color: white; border: none; padding: 8px 16px; border-radius: 4px;">Кнопка</button>
                                </div>
                                <div style="background: var(--bg-secondary, #333333); padding: 10px; border-radius: 4px; border: 1px solid var(--border-color, #4a4a4a);">
                                    <small>Вторичный элемент</small>
                                </div>
                            </div>

                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Изменения применятся после сохранения настроек
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Быстрые пресеты -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">🎨 Быстрые пресеты</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="applyPreset('corporate')">
                                    Корпоративный стиль
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="applyPreset('gaming')">
                                    Игровой стиль
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="applyPreset('nature')">
                                    Природный стиль
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="applyPreset('tech')">
                                    Технологичный стиль
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Обновление значений слайдеров в реальном времени
document.addEventListener('DOMContentLoaded', function() {
    const sliders = ['glitch_speed', 'glitch_count', 'glitch_opacity', 'bg_opacity'];

    sliders.forEach(sliderId => {
        const slider = document.getElementById(sliderId);
        const valueSpan = document.getElementById(sliderId + '_value');

        if (slider && valueSpan) {
            slider.addEventListener('input', function() {
                let value = this.value;
                if (sliderId.includes('opacity')) {
                    value = Math.round(value * 100) + '%';
                } else if (sliderId === 'glitch_speed') {
                    value = value + 's';
                }
                valueSpan.textContent = value;
                updatePreview();
            });
        }
    });

    // Обновление цветов
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('input', function() {
            const textInput = this.nextElementSibling;
            if (textInput) {
                textInput.value = this.value;
            }
            updatePreview();
        });
    });
});

// Обновление превью
function updatePreview() {
    const preview = document.getElementById('preview-container');
    if (!preview) return;

    const primaryDark = document.getElementById('primary_dark').value;
    const accentOrange = document.getElementById('accent_orange').value;
    const bgCard = document.getElementById('bg_card').value;
    const textPrimary = document.getElementById('text_primary').value;
    const bgSecondary = document.getElementById('bg_secondary').value;
    const borderColor = document.getElementById('border_color').value;

    preview.style.setProperty('--primary-dark', primaryDark);
    preview.style.setProperty('--accent-orange', accentOrange);
    preview.style.setProperty('--bg-card', bgCard);
    preview.style.setProperty('--text-primary', textPrimary);
    preview.style.setProperty('--bg-secondary', bgSecondary);
    preview.style.setProperty('--border-color', borderColor);
}

// Применение пресетов
function applyPreset(presetName) {
    const presets = {
        corporate: {
            primary_dark: '#1a1a2e',
            accent_orange: '#ff6b35',
            bg_card: '#16213e',
            text_primary: '#ffffff',
            bg_secondary: '#0f3460',
            border_color: '#533483'
        },
        gaming: {
            primary_dark: '#0d1421',
            accent_orange: '#00ff88',
            bg_card: '#1a252f',
            text_primary: '#ffffff',
            bg_secondary: '#162028',
            border_color: '#00ff88'
        },
        nature: {
            primary_dark: '#2d5016',
            accent_orange: '#8bc34a',
            bg_card: '#3e6b1f',
            text_primary: '#ffffff',
            bg_secondary: '#4caf50',
            border_color: '#689f38'
        },
        tech: {
            primary_dark: '#263238',
            accent_orange: '#00bcd4',
            bg_card: '#37474f',
            text_primary: '#ffffff',
            bg_secondary: '#455a64',
            border_color: '#607d8b'
        }
    };

    const preset = presets[presetName];
    if (preset) {
        Object.keys(preset).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                input.value = preset[key];
                const textInput = input.nextElementSibling;
                if (textInput) {
                    textInput.value = preset[key];
                }
            }
        });
        updatePreview();
    }
}

// Предварительный просмотр
function previewChanges() {
    const newWindow = window.open('../../index.html', '_blank');
    if (newWindow) {
        newWindow.addEventListener('load', function() {
            // Применяем стили в новом окне
            updatePreview();
        });
    }
}
</script>

<?php include 'templates/footer.php'; ?>
