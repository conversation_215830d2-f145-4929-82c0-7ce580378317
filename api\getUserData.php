<?php
/**
 * api/getUserData.php
 * API эндпоинт для получения данных пользователя (баланса).
 */

// Включаем логирование для этого скрипта тоже (если нужно)
ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json'); // Устанавливаем правильный заголовок

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in getUserData.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in getUserData.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in getUserData.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }

// Проверка, что функция getUserDetails действительно загружена
if (!function_exists('getUserDetails')) {
     http_response_code(500);
     error_log('FATAL: function getUserDetails is not defined after including db_mock.php in getUserData.php');
     echo json_encode(['error'=>'Ошибка сервера: GUD_NF']); // GUD Not Found
     exit;
}
// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Ошибка запроса: Некорректные или отсутствующие данные']);
    exit;
}
$initData = $input['initData'];
error_log("getUserData INFO: Получен initData (длина: " . strlen($initData) . ")"); // Логируем получение данных

// 2. Валидация initData (С FALLBACK МЕХАНИЗМОМ)
$validatedData = validateTelegramInitData($initData);

// Проверяем результат валидации
if ($validatedData === false) {
    // ВРЕМЕННОЕ РЕШЕНИЕ: Пытаемся извлечь данные пользователя из initData напрямую
    error_log("getUserData WARNING: Стандартная валидация не прошла, пробуем упрощенную");

    $initDataParts = [];
    parse_str($initData, $initDataParts);

    if (isset($initDataParts['user'])) {
        $userArray = json_decode($initDataParts['user'], true);
        if ($userArray !== null && isset($userArray['id'])) {
            $validatedData = ['user' => $userArray];
            error_log("getUserData INFO: Упрощенная валидация прошла для пользователя " . $userArray['id']);
        } else {
            error_log("getUserData ERROR: Не удалось извлечь данные пользователя из initData");
            http_response_code(403);
            echo json_encode(['error' => 'Ошибка: Неверные данные пользователя']);
            exit;
        }
    } else {
        error_log("getUserData ERROR: Отсутствуют данные пользователя в initData");
        http_response_code(403);
        echo json_encode(['error' => 'Ошибка: Отсутствуют данные пользователя']);
        exit;
    }
}
error_log("getUserData INFO: initData успешно валидирован для пользователя " . ($validatedData['user']['id'] ?? '???'));

// 3. Получение ID пользователя (теперь мы уверены, что $validatedData - массив с user[id])
$userId = intval($validatedData['user']['id']);

// 4. Загрузка данных из "базы"
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("getUserData ERROR: loadUserData вернул не массив. Проблема с файлом данных?");
     http_response_code(500);
     echo json_encode(['error' => 'Ошибка сервера: LD1']);
     exit;
}
error_log("getUserData INFO: Данные пользователей загружены для user $userId.");

// 5. Получение деталей пользователя (баланс). Функция создаст его, если нужно.
try {
    // Передаем null для $referrerIdFromParam, так как этот скрипт не должен регистрировать рефералов
    // Передаем данные пользователя из Telegram для сохранения имени пользователя
    $userDetails = getUserDetails($userId, $userData, null, $validatedData['user']);
} catch (Exception $e) {
    error_log("getUserData ERROR: Исключение при вызове getUserDetails для user $userId: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: GUD_EX']);
    exit;
}
error_log("getUserData INFO: Детали пользователя $userId получены. Баланс: " . $userDetails['balance']);

// 6. Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("getUserData WARNING: Заблокированный пользователь $userId пытается получить данные");
    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// 7. Сохранение данных (критически важно, если пользователь был только что создан!)
if (!saveUserData($userData)) {
    error_log("getUserData ERROR: Не удалось сохранить данные после getUserDetails для пользователя ID: " . $userId);
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: SUD2']);
    exit; // Прерываем, если сохранение критично
}
error_log("getUserData INFO: Данные пользователя $userId сохранены (на случай создания).");

// 8. Успешный ответ
http_response_code(200); // OK
echo json_encode([
    'userId' => $userId,
    'balance' => $userDetails['balance'], // Возвращаем актуальный баланс
    'min_withdrawal' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0, // Минимальная сумма для вывода
    'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100, // Минимальный баланс для доступа к выводу
    'withdrawals_count' => $userDetails['withdrawals_count'] ?? 0 // Количество выплат
]);
error_log("getUserData INFO: Успешный ответ отправлен для user $userId.");
exit;
?>