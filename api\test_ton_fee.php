<?php
// Скрипт для тестирования комиссии TON в терминале

// Подключаем необходимые файлы
require_once 'FeeCalculator.php';

// Устанавливаем валюту для теста
$currency = 'ton';
$amount = 100; // Сумма для примера, не влияет на фиксированную комиссию

// Получаем экземпляр калькулятора
$feeCalculator = FeeCalculator::getInstance();

// Получаем комиссию
// Используем внутренний метод getNOWPaymentsFee для прямого доступа к логике комиссий
try {
    $reflectionMethod = new ReflectionMethod('FeeCalculator', 'getNOWPaymentsFee');
    $reflectionMethod->setAccessible(true);
    $feeData = $reflectionMethod->invoke($feeCalculator, $currency, $amount);

    // Выводим результат
    echo "=========================================\n";
    echo "Тест комиссии для: " . strtoupper($currency) . "\n";
    echo "=========================================\n";
    
    if (isset($feeData['fee'])) {
        echo "Сумма комиссии: " . $feeData['fee'] . " " . $feeData['currency'] . "\n";
        echo "Источник: " . $feeData['source'] . "\n";
    } else {
        echo "Не удалось получить комиссию.\n";
        print_r($feeData);
    }
    
    echo "=========================================\n";

} catch (ReflectionException $e) {
    echo "Ошибка: не удалось получить доступ к методу getNOWPaymentsFee. " . $e->getMessage() . "\n";
}

?>