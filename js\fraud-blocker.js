/**
 * js/fraud-blocker.js
 * Система блокирующих экранов для фродеров
 * 
 * Показывает блокирующие экраны с причинами блокировки
 * на английском и русском языках
 */

class FraudBlocker {
    constructor() {
        this.isBlocked = false;
        this.blockReason = null;
        this.blockScreen = null;
        
        console.log('[FraudBlocker] 🛡️ Инициализация системы блокировки фродеров');
    }
    
    /**
     * Проверяет статус фрода пользователя
     */
    async checkFraudStatus(initData) {
        try {
            console.log('[FraudBlocker] 🔍 Проверяем статус фрода пользователя...');
            
            const response = await fetch('api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'check_fraud',
                    initData: initData
                })
            });

            if (!response.ok) {
                console.error('[FraudBlocker] ❌ HTTP ошибка:', response.status, response.statusText);
                return false; // При ошибке API не блокируем пользователя
            }

            const result = await response.json();
            
            if (!result.success) {
                console.error('[FraudBlocker] ❌ Ошибка проверки статуса:', result.error);
                return false;
            }
            
            const fraudStatus = result.fraud_status;
            
            if (fraudStatus.blocked) {
                console.log('[FraudBlocker] 🚫 Пользователь заблокирован за фрод:', fraudStatus.block_reason);
                // this.showBlockScreen(fraudStatus.block_reason);
                return true;
            }
            
            if (fraudStatus.fraud_detected) {
                console.log('[FraudBlocker] ⚠️ Обнаружен фрод, но пользователь не заблокирован:', fraudStatus.fraud_reason);
                // this.showWarningScreen(fraudStatus.fraud_reason);
            }
            
            return false;
            
        } catch (error) {
            console.error('[FraudBlocker] ❌ Ошибка проверки фрода:', error);
            return false;
        }
    }
    
    /**
     * Показывает блокирующий экран
     */
    showBlockScreen(reason) {
        this.isBlocked = true;
        this.blockReason = reason;
        
        const blockData = this.getBlockMessage(reason);
        
        this.createBlockScreen(blockData, 'blocked');
        
        // Блокируем все взаимодействия с приложением
        this.disableAppInteractions();
        
        console.log('[FraudBlocker] 🚫 Показан блокирующий экран:', reason);
    }
    
    /**
     * Показывает предупреждающий экран
     */
    showWarningScreen(reason) {
        const warningData = this.getWarningMessage(reason);
        
        this.createBlockScreen(warningData, 'warning');
        
        console.log('[FraudBlocker] ⚠️ Показан предупреждающий экран:', reason);
    }
    
    /**
     * Получает сообщение блокировки в зависимости от причины
     */
    getBlockMessage(reason) {
        const messages = {
            'fraud_detection_duplicate_fingerprint': {
                title_en: 'Account Blocked - Multiple Accounts Detected',
                message_en: 'Your account has been permanently blocked due to the detection of multiple accounts from the same device. This violates our terms of service.',
                title_ru: 'Аккаунт заблокирован - Обнаружены множественные аккаунты',
                message_ru: 'Ваш аккаунт был навсегда заблокирован из-за обнаружения нескольких аккаунтов с одного устройства. Это нарушает наши условия использования.',
                icon: '🚫'
            },
            'fraud_detection_suspicious_user_agent': {
                title_en: 'Account Blocked - Automated Access Detected',
                message_en: 'Your account has been blocked due to the detection of automated tools or bots. Human access only is allowed.',
                title_ru: 'Аккаунт заблокирован - Обнаружен автоматизированный доступ',
                message_ru: 'Ваш аккаунт заблокирован из-за обнаружения автоматизированных инструментов или ботов. Разрешен только человеческий доступ.',
                icon: '🤖'
            },
            'vpn_detected': {
                title_en: 'VPN/Proxy Detected - Access Restricted',
                message_en: 'We detected that you are using a VPN or proxy connection. Please disable your VPN/proxy and refresh the page to continue using the application.',
                title_ru: 'Обнаружен VPN/Прокси - Доступ ограничен',
                message_ru: 'Мы обнаружили, что вы используете VPN или прокси-соединение. Пожалуйста, отключите VPN/прокси и обновите страницу, чтобы продолжить использование приложения.',
                icon: '🌐'
            },
            'fraud_detection_vpn_detected': {
                title_en: 'Account Blocked - VPN/Proxy Usage Detected',
                message_en: 'Your account has been blocked due to VPN or proxy usage. Please use your real location and IP address.',
                title_ru: 'Аккаунт заблокирован - Обнаружено использование VPN/Прокси',
                message_ru: 'Ваш аккаунт заблокирован из-за использования VPN или прокси. Пожалуйста, используйте ваше реальное местоположение и IP-адрес.',
                icon: '🌐'
            },
            'self_referral_detected': {
                title_en: 'Account Blocked - Self-Referral Detected',
                message_en: 'Your account has been blocked due to self-referral or fake referral activity. Creating multiple accounts or referring yourself is prohibited.',
                title_ru: 'Аккаунт заблокирован - Обнаружен самореферрал',
                message_ru: 'Ваш аккаунт заблокирован из-за самореферрала или создания фейковых рефералов. Создание множественных аккаунтов или реферрал самого себя запрещено.',
                icon: '👥'
            },
            'fraud_detection_self_referral': {
                title_en: 'Account Blocked - Self-Referral Detected',
                message_en: 'Your account has been blocked for attempting to refer yourself or create fake referrals.',
                title_ru: 'Аккаунт заблокирован - Обнаружен самореферрал',
                message_ru: 'Ваш аккаунт заблокирован за попытку пригласить самого себя или создание фальшивых рефералов.',
                icon: '👥'
            },
            'default': {
                title_en: 'Account Blocked - Fraudulent Activity',
                message_en: 'Your account has been blocked due to suspicious or fraudulent activity. Contact support if you believe this is an error.',
                title_ru: 'Аккаунт заблокирован - Мошенническая активность',
                message_ru: 'Ваш аккаунт заблокирован из-за подозрительной или мошеннической активности. Обратитесь в поддержку, если считаете это ошибкой.',
                icon: '⚠️'
            }
        };
        
        return messages[reason] || messages['default'];
    }
    
    /**
     * Получает предупреждающее сообщение
     */
    getWarningMessage(reason) {
        const messages = {
            'duplicate_fingerprint': {
                title_en: 'Warning - Suspicious Activity Detected',
                message_en: 'We detected suspicious activity on your account. Please ensure you are following our terms of service.',
                title_ru: 'Предупреждение - Обнаружена подозрительная активность',
                message_ru: 'Мы обнаружили подозрительную активность на вашем аккаунте. Пожалуйста, убедитесь, что вы соблюдаете наши условия использования.',
                icon: '⚠️'
            },
            'default': {
                title_en: 'Warning - Account Under Review',
                message_en: 'Your account is under review due to unusual activity. Please use the app normally.',
                title_ru: 'Предупреждение - Аккаунт на проверке',
                message_ru: 'Ваш аккаунт находится на проверке из-за необычной активности. Пожалуйста, используйте приложение обычным образом.',
                icon: '🔍'
            }
        };
        
        return messages[reason] || messages['default'];
    }
    
    /**
     * Создает блокирующий экран
     */
    createBlockScreen(data, type) {
        // Удаляем существующий экран, если есть
        this.removeBlockScreen();
        
        const isBlocked = type === 'blocked';
        
        this.blockScreen = document.createElement('div');
        this.blockScreen.id = 'fraud-block-screen';
        this.blockScreen.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${isBlocked ? 'linear-gradient(135deg, #ff4757, #ff3838)' : 'linear-gradient(135deg, #ffa502, #ff6348)'};
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
            text-align: center;
            padding: 20px;
            box-sizing: border-box;
        `;
        
        this.blockScreen.innerHTML = `
            <div style="
                background: rgba(0, 0, 0, 0.3);
                border-radius: 20px;
                padding: 40px;
                max-width: 500px;
                width: 100%;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            ">
                <div style="font-size: 60px; margin-bottom: 20px;">${data.icon}</div>
                
                <h2 style="
                    margin: 0 0 15px 0;
                    font-size: 24px;
                    font-weight: 700;
                    line-height: 1.3;
                ">${data.title_en}</h2>
                
                <p style="
                    margin: 0 0 30px 0;
                    font-size: 16px;
                    line-height: 1.5;
                    opacity: 0.9;
                ">${data.message_en}</p>
                
                <div style="
                    height: 1px;
                    background: rgba(255, 255, 255, 0.3);
                    margin: 30px 0;
                "></div>
                
                <h2 style="
                    margin: 0 0 15px 0;
                    font-size: 24px;
                    font-weight: 700;
                    line-height: 1.3;
                ">${data.title_ru}</h2>
                
                <p style="
                    margin: 0 0 30px 0;
                    font-size: 16px;
                    line-height: 1.5;
                    opacity: 0.9;
                ">${data.message_ru}</p>
                
                ${!isBlocked ? `
                    <button onclick="window.fraudBlocker.removeBlockScreen()" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        color: white;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        margin-top: 20px;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" 
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        Понятно / Understood
                    </button>
                ` : ''}
                
                <div style="
                    margin-top: 30px;
                    font-size: 14px;
                    opacity: 0.7;
                ">
                    UniQPaid Security System
                </div>
            </div>
        `;
        
        document.body.appendChild(this.blockScreen);
        
        // Анимация появления
        this.blockScreen.style.opacity = '0';
        setTimeout(() => {
            this.blockScreen.style.transition = 'opacity 0.5s ease';
            this.blockScreen.style.opacity = '1';
        }, 10);
    }
    
    /**
     * Удаляет блокирующий экран
     */
    removeBlockScreen() {
        if (this.blockScreen) {
            this.blockScreen.style.transition = 'opacity 0.3s ease';
            this.blockScreen.style.opacity = '0';
            
            setTimeout(() => {
                if (this.blockScreen && this.blockScreen.parentNode) {
                    this.blockScreen.parentNode.removeChild(this.blockScreen);
                }
                this.blockScreen = null;
            }, 300);
        }
    }
    
    /**
     * Блокирует все взаимодействия с приложением
     */
    disableAppInteractions() {
        // Блокируем все кнопки
        const buttons = document.querySelectorAll('button, .btn, [onclick]');
        buttons.forEach(button => {
            if (button.id !== 'fraud-block-screen') {
                button.disabled = true;
                button.style.pointerEvents = 'none';
                button.style.opacity = '0.5';
            }
        });
        
        // Блокируем все ссылки
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            link.style.pointerEvents = 'none';
            link.style.opacity = '0.5';
        });
        
        // Блокируем все формы
        const forms = document.querySelectorAll('form, input, textarea, select');
        forms.forEach(form => {
            form.disabled = true;
            form.style.pointerEvents = 'none';
            form.style.opacity = '0.5';
        });
        
        console.log('[FraudBlocker] 🔒 Все взаимодействия с приложением заблокированы');
    }
    
    /**
     * Проверяет, заблокирован ли пользователь
     */
    isUserBlocked() {
        return this.isBlocked;
    }
    
    /**
     * Получает причину блокировки
     */
    getBlockReason() {
        return this.blockReason;
    }

    /**
     * Блокирует пользователя
     */
    blockUser(userId, reason = 'fraud_detected') {
        try {
            console.log(`[FraudBlocker] 🚫 Блокируем пользователя ${userId}, причина: ${reason}`);

            this.isBlocked = true;
            this.blockReason = reason;

            // Сохраняем информацию о блокировке
            const blockInfo = {
                user_id: userId,
                reason: reason,
                timestamp: new Date().toISOString(),
                blocked: true
            };

            // В реальности здесь был бы вызов к серверу
            localStorage.setItem(`fraud_block_${userId}`, JSON.stringify(blockInfo));

            // Показываем экран блокировки
            this.showBlockingScreen(reason);

            return {
                success: true,
                blocked: true,
                reason: reason,
                message: 'Пользователь заблокирован'
            };

        } catch (error) {
            console.error('[FraudBlocker] ❌ Ошибка блокировки пользователя:', error);
            return {
                success: false,
                blocked: false,
                error: error.message
            };
        }
    }

    /**
     * Показывает экран блокировки
     */
    showBlockingScreen(reason = 'fraud_detected') {
        try {
            console.log(`[FraudBlocker] 📺 Показываем экран блокировки: ${reason}`);

            // Используем существующий метод
            // this.showBlockScreen(reason);

            return {
                success: true,
                screen_shown: true,
                reason: reason
            };

        } catch (error) {
            console.error('[FraudBlocker] ❌ Ошибка показа экрана блокировки:', error);
            return {
                success: false,
                screen_shown: false,
                error: error.message
            };
        }
    }
}

// Экспортируем класс в глобальную область видимости
window.FraudBlocker = FraudBlocker;

// Функция для создания глобального экземпляра
function createFraudBlockerInstance() {
    if (!window.fraudBlocker) {
        try {
            window.fraudBlocker = new FraudBlocker();
            console.log('[FraudBlocker] 🎯 Глобальный экземпляр создан');
        } catch (error) {
            console.error('[FraudBlocker] ❌ Ошибка создания экземпляра:', error.message);
        }
    }
    return window.fraudBlocker;
}



// Создаем глобальный экземпляр с проверкой
createFraudBlockerInstance();

console.log('✅ [FraudBlocker] Модуль блокировки фродеров загружен');
