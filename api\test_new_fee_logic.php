<?php
/**
 * Тест новой логики комиссий - показываем пользователю сумму ПОСЛЕ вычета комиссии
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧮 Тест новой логики комиссий</h1>\n";
echo "<p><strong>Цель:</strong> Показывать пользователю сумму ПОСЛЕ вычета комиссии (то что он реально получит)</p>\n";

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/FeeCalculator.php';
require_once __DIR__ . '/currency_cache.php';

// Тестовые данные
$testCases = [
    ['coins' => 1500, 'currency' => 'ton'],
    ['coins' => 2000, 'currency' => 'eth'],
    ['coins' => 5000, 'currency' => 'btc'],
    ['coins' => 1000, 'currency' => 'usdttrc20']
];

echo "<h2>📊 Сравнение старой и новой логики</h2>\n";

foreach ($testCases as $test) {
    $coins = $test['coins'];
    $currency = $test['currency'];
    
    echo "<h3>💰 Тест: {$coins} монет → {$currency}</h3>\n";
    
    // 1. Новая логика через FeeCalculator
    echo "<h4>🚀 Новая логика (FeeCalculator):</h4>\n";
    try {
        $calculator = FeeCalculator::getInstance();
        $result = $calculator->calculateWithdrawalAmount($coins, $currency);
        
        if ($result['success']) {
            echo "✅ <strong>Пользователь получит:</strong> {$result['crypto_amount']} {$currency}<br>\n";
            echo "📤 <strong>Отправим в NOWPayments:</strong> " . ($result['nowpayments_crypto_amount'] ?? $result['crypto_amount_gross'] ?? 'N/A') . " {$currency}<br>\n";
            echo "💸 <strong>Комиссия:</strong> {$result['nowpayments_fee']} {$currency}<br>\n";
            echo "💵 <strong>USD эквивалент:</strong> \${$result['usd_amount']}<br>\n";
            echo "🔧 <strong>Метод расчета:</strong> {$result['calculation_method']}<br>\n";
        } else {
            echo "❌ <strong>Ошибка:</strong> {$result['error']}<br>\n";
            if (isset($result['minimum_coins_needed'])) {
                echo "⚠️ <strong>Нужно минимум:</strong> {$result['minimum_coins_needed']} монет<br>\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ <strong>Исключение:</strong> " . $e->getMessage() . "<br>\n";
    }
    
    // 2. Кэшированные данные
    echo "<h4>💾 Кэшированные данные:</h4>\n";
    try {
        $cachedData = getCachedCurrencyRate($currency);
        if ($cachedData) {
            $rate = $cachedData['rate_usd'];
            $minAmount = $cachedData['min_amount'];
            $networkFee = $cachedData['network_fee'];
            
            $usdAmount = $coins * CONVERSION_RATE;
            $grossCrypto = $usdAmount / $rate;
            $netCrypto = $grossCrypto - $networkFee;
            
            echo "📈 <strong>Курс:</strong> \${$rate} за 1 {$currency}<br>\n";
            echo "🔒 <strong>Минимум NOWPayments:</strong> {$minAmount} {$currency}<br>\n";
            echo "💸 <strong>Сетевая комиссия:</strong> {$networkFee} {$currency}<br>\n";
            echo "✅ <strong>Пользователь получит (кэш):</strong> " . number_format($netCrypto, 8) . " {$currency}<br>\n";
            echo "📤 <strong>Отправим в NOWPayments (кэш):</strong> " . number_format($grossCrypto, 8) . " {$currency}<br>\n";
            
            if ($netCrypto < $minAmount) {
                echo "⚠️ <strong>Внимание:</strong> После комиссии сумма меньше минимума!<br>\n";
            }
        } else {
            echo "❌ Кэш недоступен для {$currency}<br>\n";
        }
    } catch (Exception $e) {
        echo "❌ <strong>Ошибка кэша:</strong> " . $e->getMessage() . "<br>\n";
    }
    
    // 3. Fallback расчет
    echo "<h4>🔄 Fallback расчет:</h4>\n";
    $fallbackRates = [
        'eth' => 2502,
        'btc' => 104620,
        'ton' => 2.9,
        'usdttrc20' => 1.0
    ];
    
    $fallbackFees = [
        'eth' => 0.002,
        'btc' => 0.0001,
        'ton' => 0.05,
        'usdttrc20' => 1.5
    ];
    
    if (isset($fallbackRates[$currency])) {
        $rate = $fallbackRates[$currency];
        $fee = $fallbackFees[$currency];
        
        $usdAmount = $coins * 0.001;
        $grossCrypto = $usdAmount / $rate;
        $netCrypto = max(0, $grossCrypto - $fee);
        
        echo "✅ <strong>Пользователь получит (fallback):</strong> " . number_format($netCrypto, 8) . " {$currency}<br>\n";
        echo "📤 <strong>Отправим в NOWPayments (fallback):</strong> " . number_format($grossCrypto, 8) . " {$currency}<br>\n";
        echo "💸 <strong>Комиссия (fallback):</strong> {$fee} {$currency}<br>\n";
    }
    
    echo "<hr>\n";
}

// 4. Тест минимальных сумм
echo "<h2>🔒 Тест минимальных сумм</h2>\n";

$currencies = ['ton', 'eth', 'btc', 'usdttrc20'];

foreach ($currencies as $currency) {
    echo "<h3>💎 {$currency}</h3>\n";
    
    try {
        $cachedData = getCachedCurrencyRate($currency);
        if ($cachedData) {
            $minAmount = $cachedData['min_amount'];
            $networkFee = $cachedData['network_fee'];
            $rate = $cachedData['rate_usd'];
            
            // Рассчитываем минимум монет с учетом комиссии
            $grossAmountNeeded = $minAmount + $networkFee;
            $minUsd = $grossAmountNeeded * $rate;
            $minCoins = ceil($minUsd / CONVERSION_RATE);
            
            echo "🔒 <strong>Минимум NOWPayments:</strong> {$minAmount} {$currency}<br>\n";
            echo "💸 <strong>Сетевая комиссия:</strong> {$networkFee} {$currency}<br>\n";
            echo "📤 <strong>Нужно отправить:</strong> {$grossAmountNeeded} {$currency}<br>\n";
            echo "💰 <strong>Минимум монет:</strong> {$minCoins}<br>\n";
            
            // Тестируем с минимальным количеством монет
            $calculator = FeeCalculator::getInstance();
            $testResult = $calculator->calculateWithdrawalAmount($minCoins, $currency);
            
            if ($testResult['success']) {
                echo "✅ <strong>Тест с {$minCoins} монет:</strong> Пользователь получит {$testResult['crypto_amount']} {$currency}<br>\n";
            } else {
                echo "❌ <strong>Тест с {$minCoins} монет:</strong> {$testResult['error']}<br>\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ <strong>Ошибка:</strong> " . $e->getMessage() . "<br>\n";
    }
    
    echo "<br>\n";
}

echo "<h2>✅ Тест завершен</h2>\n";
echo "<p><strong>Ключевые изменения:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Пользователь видит сумму ПОСЛЕ вычета комиссии</li>\n";
echo "<li>✅ В NOWPayments отправляется сумма С комиссией</li>\n";
echo "<li>✅ Минимумы рассчитываются с учетом комиссий</li>\n";
echo "<li>✅ Кэшированные данные используются для быстрого расчета</li>\n";
echo "</ul>\n";

echo "<p><a href='../'>← Вернуться к приложению</a></p>\n";
echo "<p><a href='test_withdrawal_fixes.php'>🔧 Основные тесты выплат</a></p>\n";
?>
