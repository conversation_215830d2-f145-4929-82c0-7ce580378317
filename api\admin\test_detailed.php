<?php
session_start();

// Проверка авторизации
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo "Не авторизован. <a href='login.php'>Войти</a>";
    exit;
}

echo "<h1>Тест детальной админки</h1>";

// Путь к файлу настроек
$designSettingsFile = __DIR__ . '/../../design_settings.json';

echo "<p>Путь к файлу: " . $designSettingsFile . "</p>";
echo "<p>Файл существует: " . (file_exists($designSettingsFile) ? 'Да' : 'Нет') . "</p>";

if (file_exists($designSettingsFile)) {
    $json = file_get_contents($designSettingsFile);
    echo "<p>Размер файла: " . strlen($json) . " байт</p>";
    
    $settings = json_decode($json, true);
    if ($settings) {
        echo "<p>JSON валиден</p>";
        echo "<p>Структура:</p>";
        echo "<pre>" . print_r(array_keys($settings), true) . "</pre>";
        
        if (isset($settings['colors'])) {
            echo "<p>Секции цветов:</p>";
            echo "<pre>" . print_r(array_keys($settings['colors']), true) . "</pre>";
        }
    } else {
        echo "<p>Ошибка JSON: " . json_last_error_msg() . "</p>";
    }
}

echo "<p><a href='detailed_design.php'>Перейти к детальной админке</a></p>";
?>
