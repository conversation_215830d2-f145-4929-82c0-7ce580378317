<?php
/**
 * setup_bot_menu.php
 * Настройка Menu Button и Bot Commands для Telegram бота
 */

require_once __DIR__ . '/config.php';

/**
 * Загрузка текстов бота
 */
function loadBotTexts() {
    $textsFile = __DIR__ . '/bot_texts.json';
    if (!file_exists($textsFile)) {
        return false;
    }

    $content = file_get_contents($textsFile);
    return json_decode($content, true);
}

/**
 * Сохранение текстов бота
 */
function saveBotTexts($texts) {
    $textsFile = __DIR__ . '/bot_texts.json';
    return file_put_contents($textsFile, json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

/**
 * Получение текста бота
 */
function getBotText($key, $lang = 'ru', $params = []) {
    static $texts = null;

    if ($texts === null) {
        $texts = loadBotTexts();
    }

    if (!$texts || !isset($texts[$lang])) {
        return $key; // Возвращаем ключ если текст не найден
    }

    // Разбираем путь к тексту (например: "messages.welcome_title")
    $keys = explode('.', $key);
    $value = $texts[$lang];

    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return $key; // Возвращаем ключ если путь не найден
        }
        $value = $value[$k];
    }

    // Заменяем параметры в тексте
    if (!empty($params)) {
        foreach ($params as $param => $replacement) {
            $value = str_replace('{' . $param . '}', $replacement, $value);
        }
    }

    return $value;
}

/**
 * Настройка Menu Button (синяя кнопка в поле ввода)
 */
function setupMenuButton() {
    echo "🔧 Настройка Menu Button...\n";

    // Настройка Menu Button для русского языка
    $menuButtonRu = [
        'type' => 'web_app',
        'text' => getBotText('menu_button.text', 'ru'),
        'web_app' => [
            'url' => WEBAPP_URL
        ]
    ];

    $resultRu = telegramRequest('setChatMenuButton', [
        'menu_button' => json_encode($menuButtonRu)
    ]);

    if ($resultRu) {
        echo "✅ Menu Button (RU) настроена успешно: " . getBotText('menu_button.text', 'ru') . "\n";
    } else {
        echo "❌ Ошибка настройки Menu Button (RU)\n";
    }

    // Настройка Menu Button для английского языка
    $menuButtonEn = [
        'type' => 'web_app',
        'text' => getBotText('menu_button.text', 'en'),
        'web_app' => [
            'url' => WEBAPP_URL
        ]
    ];

    $resultEn = telegramRequest('setChatMenuButton', [
        'menu_button' => json_encode($menuButtonEn)
    ]);

    if ($resultEn) {
        echo "✅ Menu Button (EN) настроена успешно: " . getBotText('menu_button.text', 'en') . "\n";
    } else {
        echo "❌ Ошибка настройки Menu Button (EN)\n";
    }

    return $resultRu && $resultEn;
}

/**
 * Настройка Bot Commands (меню команд)
 */
function setupBotCommands() {
    echo "🔧 Настройка Bot Commands...\n";

    // Команды для русского языка
    $commandsRu = [
        [
            'command' => 'start',
            'description' => getBotText('commands.start_description', 'ru')
        ],
        [
            'command' => 'balance',
            'description' => getBotText('commands.balance_description', 'ru')
        ],
        [
            'command' => 'stats',
            'description' => getBotText('commands.stats_description', 'ru')
        ],
        [
            'command' => 'help',
            'description' => getBotText('commands.help_description', 'ru')
        ]
    ];
    
    $resultRu = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsRu),
        'language_code' => 'ru'
    ]);
    
    if ($resultRu) {
        echo "✅ Bot Commands (RU) настроены успешно\n";
    } else {
        echo "❌ Ошибка настройки Bot Commands (RU)\n";
    }
    
    // Команды для английского языка
    $commandsEn = [
        [
            'command' => 'start',
            'description' => getBotText('commands.start_description', 'en')
        ],
        [
            'command' => 'balance',
            'description' => getBotText('commands.balance_description', 'en')
        ],
        [
            'command' => 'stats',
            'description' => getBotText('commands.stats_description', 'en')
        ],
        [
            'command' => 'help',
            'description' => getBotText('commands.help_description', 'en')
        ]
    ];
    
    $resultEn = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsEn),
        'language_code' => 'en'
    ]);
    
    if ($resultEn) {
        echo "✅ Bot Commands (EN) настроены успешно\n";
    } else {
        echo "❌ Ошибка настройки Bot Commands (EN)\n";
    }
    
    // Команды по умолчанию (без языка)
    $commandsDefault = [
        [
            'command' => 'start',
            'description' => '🚀 Launch app and start earning'
        ],
        [
            'command' => 'balance',
            'description' => '💰 Check balance and withdrawal history'
        ],
        [
            'command' => 'stats',
            'description' => '📊 Earnings and referrals statistics'
        ],
        [
            'command' => 'help',
            'description' => '❓ Help and usage instructions'
        ]
    ];
    
    $resultDefault = telegramRequest('setMyCommands', [
        'commands' => json_encode($commandsDefault)
    ]);
    
    if ($resultDefault) {
        echo "✅ Bot Commands (Default) настроены успешно\n";
    } else {
        echo "❌ Ошибка настройки Bot Commands (Default)\n";
    }
    
    return $resultRu && $resultEn && $resultDefault;
}

/**
 * Настройка описания бота
 */
function setupBotDescription() {
    echo "🔧 Настройка описания бота...\n";
    
    // Описание для русского языка
    $descriptionRu = getBotText('descriptions.full', 'ru');
    
    $resultRu = telegramRequest('setMyDescription', [
        'description' => $descriptionRu,
        'language_code' => 'ru'
    ]);
    
    if ($resultRu) {
        echo "✅ Описание бота (RU) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки описания бота (RU)\n";
    }
    
    // Описание для английского языка
    $descriptionEn = getBotText('descriptions.full', 'en');
    
    $resultEn = telegramRequest('setMyDescription', [
        'description' => $descriptionEn,
        'language_code' => 'en'
    ]);
    
    if ($resultEn) {
        echo "✅ Описание бота (EN) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки описания бота (EN)\n";
    }
    
    // Описание по умолчанию
    $resultDefault = telegramRequest('setMyDescription', [
        'description' => $descriptionEn
    ]);
    
    if ($resultDefault) {
        echo "✅ Описание бота (Default) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки описания бота (Default)\n";
    }
    
    return $resultRu && $resultEn && $resultDefault;
}

/**
 * Настройка короткого описания бота
 */
function setupBotShortDescription() {
    echo "🔧 Настройка короткого описания бота...\n";
    
    // Короткое описание для русского языка
    $shortDescRu = getBotText('descriptions.short', 'ru');
    
    $resultRu = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescRu,
        'language_code' => 'ru'
    ]);
    
    if ($resultRu) {
        echo "✅ Короткое описание бота (RU) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки короткого описания бота (RU)\n";
    }
    
    // Короткое описание для английского языка
    $shortDescEn = getBotText('descriptions.short', 'en');
    
    $resultEn = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescEn,
        'language_code' => 'en'
    ]);
    
    if ($resultEn) {
        echo "✅ Короткое описание бота (EN) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки короткого описания бота (EN)\n";
    }
    
    // Короткое описание по умолчанию
    $resultDefault = telegramRequest('setMyShortDescription', [
        'short_description' => $shortDescEn
    ]);
    
    if ($resultDefault) {
        echo "✅ Короткое описание бота (Default) настроено успешно\n";
    } else {
        echo "❌ Ошибка настройки короткого описания бота (Default)\n";
    }
    
    return $resultRu && $resultEn && $resultDefault;
}

// Запуск настройки если файл вызван напрямую
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🤖 Настройка Telegram бота UniQPaid...\n\n";
    
    $menuSuccess = setupMenuButton();
    echo "\n";
    
    $commandsSuccess = setupBotCommands();
    echo "\n";
    
    $descSuccess = setupBotDescription();
    echo "\n";
    
    $shortDescSuccess = setupBotShortDescription();
    echo "\n";
    
    if ($menuSuccess && $commandsSuccess && $descSuccess && $shortDescSuccess) {
        echo "🎉 Все настройки бота выполнены успешно!\n";
        echo "📱 Теперь у пользователей будет:\n";
        echo "   • Синяя кнопка '🚀 Запустить' в поле ввода\n";
        echo "   • Меню команд: /start, /balance, /stats, /help\n";
        echo "   • Описания бота на русском и английском языках\n";
    } else {
        echo "⚠️ Некоторые настройки не удалось выполнить. Проверьте логи.\n";
    }
}
?>
