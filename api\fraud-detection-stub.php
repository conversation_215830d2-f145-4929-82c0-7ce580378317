<?php
/**
 * Временная заглушка для fraud-detection.php
 * Используется если основной файл не найден на сервере
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка OPTIONS запроса
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Получаем данные
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? '';
    
    error_log("fraud-detection-stub: Получен запрос action = $action");
    
    switch ($action) {
        case 'register_device':
            // Заглушка для регистрации отпечатков
            $response = [
                'success' => true,
                'message' => 'Fingerprint registered (stub mode)',
                'fraud_analysis' => [
                    'is_fraud' => false,
                    'risk_score' => 0,
                    'risk_level' => 'LOW',
                    'reason' => 'Stub mode - no real analysis',
                    'duplicate_users' => [],
                    'indicators' => []
                ],
                'user_status' => [
                    'blocked' => false,
                    'fraud_detected' => false
                ]
            ];
            break;
            
        case 'get_admin_settings':
            // Заглушка для настроек
            $response = [
                'success' => true,
                'settings' => [
                    'enable_antifraud' => true,
                    'fraud_threshold' => 50,
                    'block_vpn' => false,
                    'block_duplicate_fingerprints' => true,
                    'block_self_referrals' => true
                ]
            ];
            break;
            
        default:
            $response = [
                'success' => false,
                'error' => 'Unknown action in stub mode',
                'action' => $action
            ];
            break;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("fraud-detection-stub ERROR: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Stub mode error: ' . $e->getMessage()
    ]);
}
?>
