// === withdrawal.js ===
// Файл: js/withdrawal.js
// Описание: Управляет логикой формы запроса на вывод средств.

class WithdrawalFormManager {
  constructor() {
    this.elements = {
      withdrawalAmountInput: document.getElementById('withdrawal-amount'),
      withdrawalAddressInput: document.getElementById('withdrawal-address'),
      cryptoCurrencySelect: document.getElementById('crypto-currency'),
      requestWithdrawalButton: document.getElementById('request-withdrawal-button'),
      withdrawalError: document.getElementById('withdrawal-error'),
      cryptoAmountField: document.getElementById('crypto-amount'),
    };
  }

  init() {
    console.log('[WithdrawalFormManager] Инициализация формы вывода...');
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.elements.requestWithdrawalButton.addEventListener('click', () => this.handleWithdrawalRequest());
    
    const inputs = [this.elements.withdrawalAmountInput, this.elements.withdrawalAddressInput, this.elements.cryptoCurrencySelect];
    inputs.forEach(input => {
      if (input) {
        input.addEventListener('input', () => this.validateForm());
        input.addEventListener('change', () => this.validateForm());
      }
    });

    if(this.elements.withdrawalAmountInput || this.elements.cryptoCurrencySelect) {
        const triggers = [this.elements.withdrawalAmountInput, this.elements.cryptoCurrencySelect];
        triggers.forEach(el => {
            if(el) {
                el.addEventListener('input', () => this.updateCryptoAmountField());
                el.addEventListener('change', () => this.updateCryptoAmountField());
            }
        });
    }
  }
  
  validateForm() {
    // ... (код валидации, он был корректным)
  }

  async handleWithdrawalRequest() {
    // ... (код отправки, он был корректным)
  }

  async updateCryptoAmountField() {
    // Эта логика теперь живет в calculatorManager, но мы можем ее вызвать
    if (window.calculatorManager) {
        const amount = parseInt(this.elements.withdrawalAmountInput.value, 10) || 0;
        const currency = this.elements.cryptoCurrencySelect.value;
        const currencyInfo = window.calculatorManager.currencyData[currency];
        if (!currencyInfo) {
            this.elements.cryptoAmountField.value = 'Ошибка валюты';
            return;
        }

        const dollarAmount = amount * (appSettings.get('conversion_rate') || 0.001);
        const feeUSD = parseFloat(currencyInfo.networkFee) || 0;
        const netUSD = dollarAmount - feeUSD;

        if (netUSD > 0) {
            const rate = parseFloat(currencyInfo.rate_usd) || 1;
            const cryptoAmount = netUSD / rate;
            this.elements.cryptoAmountField.value = `${cryptoAmount.toFixed(8)} ${currency.toUpperCase()}`;
        } else if (amount > 0) {
            this.elements.cryptoAmountField.value = 'Сумма < комиссии';
        } else {
            this.elements.cryptoAmountField.value = '';
        }
    }
  }
}

window.withdrawalFormManager = new WithdrawalFormManager();
console.log('💳 [WithdrawalFormManager] Менеджер формы вывода загружен.');