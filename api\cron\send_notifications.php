<?php
/**
 * api/cron/send_notifications.php
 * Cron-скрипт для отправки уведомлений неактивным пользователям
 * Запускается каждый день в 10:00 UTC
 * Работает с JSON файлами данных пользователей
 */

// Включаем логирование
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Увеличиваем лимиты для работы с большими файлами
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300); // 5 минут

// Подключение зависимостей
try {
    require_once __DIR__ . '/../config.php';
} catch (Exception $e) {
    error_log("ОШИБКА подключения config.php: " . $e->getMessage());
    exit(1);
}

try {
    require_once __DIR__ . '/../functions.php';
} catch (Exception $e) {
    error_log("ОШИБКА подключения functions.php: " . $e->getMessage());
    exit(1);
}

// Подключаем функции для работы с текстами бота
$botSetupFile = __DIR__ . '/../../bot/setup_bot_menu.php';
if (file_exists($botSetupFile)) {
    try {
        require_once $botSetupFile;
    } catch (Exception $e) {
        // Игнорируем ошибки подключения bot файлов
        error_log("Предупреждение: Не удалось подключить setup_bot_menu.php: " . $e->getMessage());
    }
}

// Логирование начала работы скрипта
$logFile = __DIR__ . '/notifications.log';
function logNotification($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

logNotification("=== НАЧАЛО ОТПРАВКИ УВЕДОМЛЕНИЙ ===");

// Отладочная информация
logNotification("PHP версия: " . PHP_VERSION);
logNotification("Текущая директория: " . __DIR__);
logNotification("USER_DATA_FILE определен: " . (defined('USER_DATA_FILE') ? 'ДА' : 'НЕТ'));
logNotification("Память: " . ini_get('memory_limit') . ", время: " . ini_get('max_execution_time') . "с");

// Проверяем наличие файла данных пользователей
if (!defined('USER_DATA_FILE') || !file_exists(USER_DATA_FILE)) {
    logNotification("ОШИБКА: Файл данных пользователей не найден или константа USER_DATA_FILE не определена.");
    if (defined('USER_DATA_FILE')) {
        logNotification("USER_DATA_FILE путь: " . USER_DATA_FILE);
        logNotification("Файл существует: " . (file_exists(USER_DATA_FILE) ? 'ДА' : 'НЕТ'));
    }
    exit(1);
}

logNotification("Файл данных найден: " . USER_DATA_FILE);
logNotification("Размер файла: " . round(filesize(USER_DATA_FILE) / 1024, 2) . " КБ");

logNotification("Файл данных пользователей найден: " . USER_DATA_FILE);

// Функция получения настроек уведомлений из JSON файла
function getNotificationSettings() {
    // Путь к файлу настроек в папке admin
    $settingsFile = __DIR__ . '/../admin/notification_settings.json';

    // Настройки по умолчанию
    $defaultSettings = [
        'notifications_enabled' => ['value' => '1', 'enabled' => true],
        'message_template' => [
            'value' => 'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!',
            'enabled' => true
        ],
        'bot_username' => ['value' => 'uniqpaid_paid_bot', 'enabled' => true],
        'inactive_hours' => ['value' => '24', 'enabled' => true]
    ];

    if (!file_exists($settingsFile)) {
        // Создаем файл настроек с значениями по умолчанию
        file_put_contents($settingsFile, json_encode($defaultSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        return $defaultSettings;
    }

    $content = file_get_contents($settingsFile);
    $settings = json_decode($content, true);

    if ($settings === null) {
        return $defaultSettings;
    }

    return $settings;
}

// Функция отправки сообщения через Telegram Bot API с полным набором кнопок
function sendTelegramMessage($chatId, $message, $botToken, $userLang = 'ru') {
    $url = "https://api.telegram.org/bot{$botToken}/sendMessage";

    // Тексты кнопок из файла bot_texts.json или fallback
    if (function_exists('getBotText')) {
        $launchAppText = getBotText('buttons.launch_app', $userLang);
        $friendsText = getBotText('buttons.friends', $userLang);
        $balanceText = getBotText('buttons.my_balance', $userLang);
        $statsText = getBotText('buttons.statistics', $userLang);
        $helpText = getBotText('buttons.help', $userLang);
    } else {
        // Fallback тексты если функция недоступна
        if ($userLang === 'en') {
            $launchAppText = '🚀 Launch App';
            $friendsText = '👥 Friends';
            $balanceText = '💰 My Balance';
            $statsText = '📊 Statistics';
            $helpText = '❓ Help';
        } else {
            $launchAppText = '🚀 Запустить приложение';
            $friendsText = '👥 Друзья';
            $balanceText = '💰 Мой баланс';
            $statsText = '📊 Статистика';
            $helpText = '❓ Помощь';
        }
    }

    // Полная inline клавиатура как в основном боте
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => $launchAppText,
                    'web_app' => [
                        'url' => 'https://app.uniqpaid.com/test3/'
                    ]
                ]
            ],
            [
                [
                    'text' => $friendsText,
                    'callback_data' => 'invite_friends'
                ],
                [
                    'text' => $balanceText,
                    'callback_data' => 'my_balance'
                ]
            ],
            [
                [
                    'text' => $statsText,
                    'callback_data' => 'my_stats'
                ],
                [
                    'text' => $helpText,
                    'callback_data' => 'help'
                ]
            ]
        ]
    ];

    $data = [
        'chat_id' => $chatId,
        'text' => $message,
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => true,
        'reply_markup' => json_encode($keyboard)
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data),
            'timeout' => 10
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    if ($result === false) {
        return ['success' => false, 'error' => 'Network error'];
    }

    $response = json_decode($result, true);
    return $response;
}

try {
    // Получаем настройки уведомлений
    $settings = getNotificationSettings();

    // Проверяем, включены ли уведомления
    if (!isset($settings['notifications_enabled']) || !$settings['notifications_enabled']['enabled'] || $settings['notifications_enabled']['value'] !== '1') {
        logNotification("Уведомления отключены в настройках. Завершение работы.");
        exit(0);
    }

    // Получаем параметры
    $messageTemplate = isset($settings['message_template']) ? $settings['message_template']['value'] : '';
    $botUsername = isset($settings['bot_username']) ? $settings['bot_username']['value'] : 'uniqpaid_paid_bot';
    $inactiveHours = isset($settings['inactive_hours']) ? intval($settings['inactive_hours']['value']) : 24;

    if (empty($messageTemplate)) {
        logNotification("ОШИБКА: Шаблон сообщения не настроен");
        exit(1);
    }

    // Проверяем токен бота
    if (!defined('TELEGRAM_BOT_TOKEN') || empty(TELEGRAM_BOT_TOKEN)) {
        logNotification("ОШИБКА: Токен Telegram бота не настроен");
        exit(1);
    }

    logNotification("Настройки загружены: неактивность {$inactiveHours}ч, бот @{$botUsername}");

    // Загружаем данные пользователей из JSON
    logNotification("Начинаем загрузку данных пользователей...");
    $userData = loadUserData();
    logNotification("Загрузка данных завершена");

    if (!$userData || !is_array($userData)) {
        logNotification("ОШИБКА: Не удалось загрузить данные пользователей");
        logNotification("Тип данных: " . gettype($userData));
        exit(1);
    }

    logNotification("Загружено пользователей: " . count($userData));

    // Загружаем логи уведомлений
    $notificationLogsFile = __DIR__ . '/notification_logs.json';
    $notificationLogs = [];
    if (file_exists($notificationLogsFile)) {
        $content = file_get_contents($notificationLogsFile);
        $notificationLogs = json_decode($content, true) ?: [];
    }

    // Получаем неактивных пользователей
    $inactiveUsers = [];
    $currentTime = time();
    $inactiveThreshold = $currentTime - ($inactiveHours * 3600); // Переводим часы в секунды
    $lastDayThreshold = $currentTime - (24 * 3600); // 24 часа назад

    foreach ($userData as $userId => $user) {
        // Проверяем наличие telegram_id (может быть в разных местах)
        $telegramId = null;
        if (isset($user['telegram_data']['id'])) {
            $telegramId = $user['telegram_data']['id'];
        } elseif (isset($user['id'])) {
            $telegramId = $user['id'];
        } elseif (is_numeric($userId)) {
            $telegramId = $userId;
        }

        if (!$telegramId) {
            continue; // Пропускаем пользователей без Telegram ID
        }

        // Проверяем last_activity
        $lastActivity = isset($user['last_activity']) ? $user['last_activity'] : 0;

        // Пользователь неактивен более указанного времени?
        if ($lastActivity >= $inactiveThreshold) {
            continue; // Пользователь активен, пропускаем
        }

        // Проверяем, не отправляли ли уже уведомление за последние 23 часа
        $alreadySent = false;
        foreach ($notificationLogs as $log) {
            if ($log['telegram_id'] == $telegramId && $log['sent_at'] > $lastDayThreshold) {
                $alreadySent = true;
                break;
            }
        }

        if ($alreadySent) {
            continue; // Уже отправляли недавно
        }

        // Добавляем пользователя в список для отправки
        $inactiveUsers[] = [
            'id' => $userId,
            'telegram_id' => $telegramId,
            'first_name' => $user['first_name'] ?? $user['telegram_data']['first_name'] ?? 'Пользователь',
            'last_name' => $user['last_name'] ?? $user['telegram_data']['last_name'] ?? '',
            'username' => $user['username'] ?? $user['telegram_data']['username'] ?? '',
            'last_activity' => $lastActivity,
            'language' => $user['language'] ?? 'ru' // Язык пользователя
        ];

        // Ограничиваем количество для безопасности
        if (count($inactiveUsers) >= 100) {
            break;
        }
    }

    logNotification("Найдено неактивных пользователей: " . count($inactiveUsers));

    if (empty($inactiveUsers)) {
        logNotification("Нет пользователей для отправки уведомлений");
        exit(0);
    }
    
    $sentCount = 0;
    $failedCount = 0;
    $blockedCount = 0;
    
    foreach ($inactiveUsers as $user) {
        // Формируем персональное сообщение
        $personalMessage = str_replace(
            ['{first_name}', '{last_name}', '{username}'],
            [
                $user['first_name'] ?: 'Пользователь',
                $user['last_name'] ?: '',
                $user['username'] ? '@' . $user['username'] : ''
            ],
            $messageTemplate
        );
        
        // Заменяем \n на реальные переносы строк
        $personalMessage = str_replace('\\n', "\n", $personalMessage);
        
        // Отправляем сообщение с кнопкой
        $response = sendTelegramMessage($user['telegram_id'], $personalMessage, TELEGRAM_BOT_TOKEN, $user['language']);
        
        $status = 'failed';
        $errorMessage = null;
        
        if (isset($response['ok']) && $response['ok']) {
            $status = 'sent';
            $sentCount++;
            logNotification("✅ Отправлено: {$user['first_name']} (ID: {$user['telegram_id']})");
        } else {
            $errorMessage = isset($response['description']) ? $response['description'] : 'Unknown error';

            // Проверяем, заблокирован ли бот пользователем
            if (strpos($errorMessage, 'blocked') !== false ||
                strpos($errorMessage, 'user is deactivated') !== false ||
                strpos($errorMessage, 'chat not found') !== false) {
                $status = 'blocked';
                $blockedCount++;
                logNotification("🚫 Заблокирован: {$user['first_name']} (ID: {$user['telegram_id']}) - {$errorMessage}");
            } else {
                $failedCount++;
                logNotification("❌ Ошибка отправки: {$user['first_name']} (ID: {$user['telegram_id']}) - {$errorMessage}");
            }
        }
        
        // Записываем лог отправки в JSON файл
        try {
            $logEntry = [
                'id' => uniqid(),
                'user_id' => $user['id'],
                'telegram_id' => $user['telegram_id'],
                'username' => $user['username'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'message_text' => $personalMessage,
                'status' => $status,
                'error_message' => $errorMessage,
                'last_activity' => $user['last_activity'],
                'sent_at' => $currentTime
            ];

            $notificationLogs[] = $logEntry;

            // Сохраняем обновленные логи
            file_put_contents($notificationLogsFile, json_encode($notificationLogs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), LOCK_EX);

        } catch (Exception $e) {
            logNotification("ОШИБКА записи лога: " . $e->getMessage());
        }

        // Небольшая задержка между отправками
        usleep(100000); // 0.1 секунды
    }

    logNotification("=== РЕЗУЛЬТАТЫ ОТПРАВКИ ===");
    logNotification("Успешно отправлено: {$sentCount}");
    logNotification("Ошибки отправки: {$failedCount}");
    logNotification("Заблокированные пользователи: {$blockedCount}");
    logNotification("Всего обработано: " . count($inactiveUsers));

} catch (Exception $e) {
    logNotification("НЕОЖИДАННАЯ ОШИБКА: " . $e->getMessage());
    exit(1);
}

// === РОТАЦИЯ ЛОГОВ РЕКЛАМНОЙ СТАТИСТИКИ ===
logNotification("=== НАЧАЛО РОТАЦИИ ЛОГОВ ===");

try {
    // Запускаем ротацию логов
    $rotationScript = __DIR__ . '/log_rotation.php';

    if (file_exists($rotationScript)) {
        // Выполняем скрипт ротации
        ob_start();
        include $rotationScript;
        $rotationResult = ob_get_clean();

        // Парсим результат
        $result = json_decode($rotationResult, true);

        if ($result && $result['success']) {
            logNotification("Ротация логов выполнена успешно:");
            logNotification("- Ротация по размеру: " . ($result['rotated_by_size'] ? 'Да' : 'Нет'));
            logNotification("- Ротация по дате: " . ($result['rotated_by_date'] ? 'Да' : 'Нет'));
            logNotification("- Удалено архивов: " . $result['deleted_archives']);
            logNotification("- Всего архивов: " . $result['archive_stats']['count']);
            logNotification("- Размер архивов: " . round($result['archive_stats']['total_size'] / 1024 / 1024, 2) . " МБ");
        } else {
            $error = $result ? $result['error'] : 'Неизвестная ошибка';
            logNotification("ОШИБКА ротации логов: " . $error);
        }
    } else {
        logNotification("ПРЕДУПРЕЖДЕНИЕ: Скрипт ротации логов не найден: " . $rotationScript);
    }

} catch (Exception $rotationError) {
    logNotification("ОШИБКА при выполнении ротации логов: " . $rotationError->getMessage());
}

logNotification("=== ЗАВЕРШЕНИЕ РОТАЦИИ ЛОГОВ ===");
logNotification("=== ЗАВЕРШЕНИЕ ОТПРАВКИ УВЕДОМЛЕНИЙ ===");
exit(0);
?>
