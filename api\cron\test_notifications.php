<?php
/**
 * api/cron/test_notifications.php
 * Тестовый скрипт для проверки работы системы уведомлений
 */

// Включаем отображение ошибок для тестирования
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

echo "<h1>Тест системы уведомлений</h1>\n";

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

// Подключение к базе данных
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ Подключение к базе данных установлено</p>\n";
} catch (PDOException $e) {
    echo "<p>❌ Ошибка подключения к БД: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    exit(1);
}

// Проверяем наличие таблиц
echo "<h2>Проверка таблиц</h2>\n";

$tables = ['notification_settings', 'notification_logs'];
foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Таблица <code>$table</code> существует</p>\n";
        } else {
            echo "<p>❌ Таблица <code>$table</code> не найдена</p>\n";
        }
    } catch (PDOException $e) {
        echo "<p>❌ Ошибка проверки таблицы $table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
}

// Проверяем настройки уведомлений
echo "<h2>Настройки уведомлений</h2>\n";

try {
    $stmt = $pdo->query("SELECT setting_name, setting_value, is_enabled FROM notification_settings");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($settings)) {
        echo "<p>⚠️ Настройки уведомлений не найдены</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Настройка</th><th>Значение</th><th>Включено</th></tr>\n";
        foreach ($settings as $setting) {
            $enabled = $setting['is_enabled'] ? '✅' : '❌';
            $value = htmlspecialchars(substr($setting['setting_value'], 0, 100));
            if (strlen($setting['setting_value']) > 100) {
                $value .= '...';
            }
            echo "<tr><td>{$setting['setting_name']}</td><td>$value</td><td>$enabled</td></tr>\n";
        }
        echo "</table>\n";
    }
} catch (PDOException $e) {
    echo "<p>❌ Ошибка получения настроек: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Проверяем токен бота
echo "<h2>Конфигурация бота</h2>\n";

if (defined('TELEGRAM_BOT_TOKEN') && !empty(TELEGRAM_BOT_TOKEN)) {
    $tokenPreview = substr(TELEGRAM_BOT_TOKEN, 0, 10) . '...' . substr(TELEGRAM_BOT_TOKEN, -10);
    echo "<p>✅ Токен бота настроен: <code>$tokenPreview</code></p>\n";
    
    // Проверяем доступность Telegram API
    $url = "https://api.telegram.org/bot" . TELEGRAM_BOT_TOKEN . "/getMe";
    $response = @file_get_contents($url);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (isset($data['ok']) && $data['ok']) {
            $botInfo = $data['result'];
            echo "<p>✅ Бот доступен: @{$botInfo['username']} ({$botInfo['first_name']})</p>\n";
        } else {
            echo "<p>❌ Ошибка API бота: " . htmlspecialchars($data['description'] ?? 'Unknown error') . "</p>\n";
        }
    } else {
        echo "<p>❌ Не удалось подключиться к Telegram API</p>\n";
    }
} else {
    echo "<p>❌ Токен бота не настроен в config.php</p>\n";
}

// Проверяем пользователей для тестирования
echo "<h2>Пользователи для тестирования</h2>\n";

try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_users,
               COUNT(CASE WHEN telegram_id IS NOT NULL AND telegram_id != '' THEN 1 END) as users_with_telegram,
               COUNT(CASE WHEN last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as inactive_users
        FROM users
    ");
    $userStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>👥 Всего пользователей: {$userStats['total_users']}</p>\n";
    echo "<p>📱 С Telegram ID: {$userStats['users_with_telegram']}</p>\n";
    echo "<p>😴 Неактивных (>24ч): {$userStats['inactive_users']}</p>\n";
    
    // Показываем несколько примеров неактивных пользователей
    $stmt = $pdo->query("
        SELECT telegram_id, first_name, last_name, username, last_activity
        FROM users 
        WHERE telegram_id IS NOT NULL AND telegram_id != ''
        AND last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY last_activity ASC
        LIMIT 5
    ");
    $inactiveUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($inactiveUsers)) {
        echo "<h3>Примеры неактивных пользователей:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Имя</th><th>Telegram ID</th><th>Последняя активность</th></tr>\n";
        foreach ($inactiveUsers as $user) {
            $name = $user['first_name'] . ' ' . ($user['last_name'] ?: '');
            if ($user['username']) {
                $name .= " (@{$user['username']})";
            }
            echo "<tr><td>" . htmlspecialchars($name) . "</td><td>{$user['telegram_id']}</td><td>{$user['last_activity']}</td></tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Ошибка получения статистики пользователей: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Проверяем логи уведомлений
echo "<h2>История уведомлений</h2>\n";

try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_notifications,
               COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
               COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
               COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_count,
               MAX(sent_at) as last_sent
        FROM notification_logs
    ");
    $notificationStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>📧 Всего уведомлений: {$notificationStats['total_notifications']}</p>\n";
    echo "<p>✅ Отправлено: {$notificationStats['sent_count']}</p>\n";
    echo "<p>❌ Ошибки: {$notificationStats['failed_count']}</p>\n";
    echo "<p>🚫 Заблокированы: {$notificationStats['blocked_count']}</p>\n";
    echo "<p>🕐 Последняя отправка: " . ($notificationStats['last_sent'] ?: 'Никогда') . "</p>\n";
    
} catch (PDOException $e) {
    echo "<p>❌ Ошибка получения статистики уведомлений: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Проверяем права доступа к файлам
echo "<h2>Права доступа</h2>\n";

$cronScript = __DIR__ . '/send_notifications.php';
if (file_exists($cronScript)) {
    echo "<p>✅ Cron-скрипт найден: <code>$cronScript</code></p>\n";
    if (is_readable($cronScript)) {
        echo "<p>✅ Cron-скрипт читаемый</p>\n";
    } else {
        echo "<p>❌ Cron-скрипт не читаемый</p>\n";
    }
} else {
    echo "<p>❌ Cron-скрипт не найден</p>\n";
}

$logDir = __DIR__;
if (is_writable($logDir)) {
    echo "<p>✅ Директория логов доступна для записи</p>\n";
} else {
    echo "<p>❌ Директория логов не доступна для записи</p>\n";
}

echo "<h2>Тестирование завершено</h2>\n";
echo "<p><strong>Для запуска cron-скрипта используйте:</strong></p>\n";
echo "<pre>php " . $cronScript . "</pre>\n";
?>
