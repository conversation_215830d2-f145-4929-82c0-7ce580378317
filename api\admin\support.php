<?php
/**
 * api/admin/support.php
 * Страница управления поддержкой
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';
require_once __DIR__ . '/auto_migrate_support.php'; // Автомиграция чатов

// Получаем параметры фильтрации и пагинации
$statusFilter = $_GET['status'] ?? 'all';
$dateFilter = $_GET['date'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = intval($_GET['per_page'] ?? 10); // Количество чатов на странице
$perPage = in_array($perPage, [10, 25, 50]) ? $perPage : 10; // Валидация

// Получаем все чаты поддержки
$allChats = getSupportChats();

// Получаем статистику по статусам
$chatStats = getChatStatusStats();

// Применяем фильтры
$filteredChats = [];
foreach ($allChats as $chatId => $chat) {
    // Фильтр по статусу
    $chatStatus = $chat['status'] ?? 'open';
    if ($statusFilter !== 'all' && $chatStatus !== $statusFilter) {
        continue;
    }

    // Фильтр по дате
    if ($dateFilter !== 'all') {
        $chatDate = date('Y-m-d', strtotime($chat['updated_at']));
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $weekAgo = date('Y-m-d', strtotime('-7 days'));

        switch ($dateFilter) {
            case 'today':
                if ($chatDate !== $today) continue 2;
                break;
            case 'yesterday':
                if ($chatDate !== $yesterday) continue 2;
                break;
            case 'week':
                if ($chatDate < $weekAgo) continue 2;
                break;
        }
    }

    $filteredChats[$chatId] = $chat;
}

// Сортируем чаты по времени последнего обновления (новые сверху)
uasort($filteredChats, function($a, $b) {
    return strtotime($b['updated_at']) - strtotime($a['updated_at']);
});

// Пагинация
$totalChats = count($filteredChats);
$totalPages = ceil($totalChats / $perPage);
$offset = ($page - 1) * $perPage;
$supportChats = array_slice($filteredChats, $offset, $perPage, true);

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-headset me-2"></i>
                    Поддержка пользователей
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="checkWebhook()">
                            <i class="bi bi-globe"></i> Проверить Webhook
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="setupWebhook()">
                            <i class="bi bi-gear"></i> Настроить Webhook
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeWebhook()">
                            <i class="bi bi-trash"></i> Удалить Webhook
                        </button>
                    </div>
                </div>
            </div>

            <!-- Статистика -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Всего чатов
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $chatStats['total']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-chat-dots fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Открытые
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $chatStats['open']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-unlock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-secondary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                        Закрытые
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $chatStats['closed']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-lock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Непрочитанные
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $chatStats['unread']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Активные сегодня
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php
                                        $todayActive = 0;
                                        $today = date('Y-m-d');
                                        foreach ($supportChats as $chat) {
                                            if (strpos($chat['updated_at'], $today) === 0) {
                                                $todayActive++;
                                            }
                                        }
                                        echo $todayActive;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Фильтры и поиск -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Фильтры</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Статус чата</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?php echo $statusFilter === 'all' ? 'selected' : ''; ?>>Все статусы</option>
                                <option value="open" <?php echo $statusFilter === 'open' ? 'selected' : ''; ?>>🟢 Открытые</option>
                                <option value="closed" <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>🔒 Закрытые</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date" class="form-label">Период активности</label>
                            <select class="form-select" id="date" name="date">
                                <option value="all" <?php echo $dateFilter === 'all' ? 'selected' : ''; ?>>Все время</option>
                                <option value="today" <?php echo $dateFilter === 'today' ? 'selected' : ''; ?>>Сегодня</option>
                                <option value="yesterday" <?php echo $dateFilter === 'yesterday' ? 'selected' : ''; ?>>Вчера</option>
                                <option value="week" <?php echo $dateFilter === 'week' ? 'selected' : ''; ?>>За неделю</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="per_page" class="form-label">Показать на странице</label>
                            <select class="form-select" id="per_page" name="per_page">
                                <option value="10" <?php echo $perPage === 10 ? 'selected' : ''; ?>>10</option>
                                <option value="25" <?php echo $perPage === 25 ? 'selected' : ''; ?>>25</option>
                                <option value="50" <?php echo $perPage === 50 ? 'selected' : ''; ?>>50</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-funnel"></i> Применить фильтры
                                </button>
                            </div>
                        </div>
                    </form>

                    <?php if ($statusFilter !== 'all' || $dateFilter !== 'all'): ?>
                        <div class="mt-3">
                            <span class="badge bg-info">
                                Показано <?php echo count($supportChats); ?> из <?php echo $totalChats; ?> чатов
                            </span>
                            <a href="support.php" class="btn btn-sm btn-outline-secondary ms-2">
                                <i class="bi bi-x-circle"></i> Сбросить фильтры
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Список чатов -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Чаты поддержки</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($supportChats)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-chat-dots" style="font-size: 3rem; color: #ccc;"></i>
                            <p class="mt-3 text-muted">Пока нет обращений в поддержку</p>
                            <p class="text-muted">Пользователи могут обратиться к боту @<?php echo SUPPORT_BOT_USERNAME; ?></p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Пользователь</th>
                                        <th>Последнее сообщение</th>
                                        <th>Статус</th>
                                        <th>Язык</th>
                                        <th>Непрочитанные</th>
                                        <th>Последняя активность</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supportChats as $chatId => $chat): ?>
                                        <?php 
                                        $lastMessage = getLastChatMessage($chatId);
                                        $unreadCount = getUnreadMessagesCount($chatId);
                                        $displayName = $chat['first_name'];
                                        if ($chat['last_name']) {
                                            $displayName .= ' ' . $chat['last_name'];
                                        }
                                        if ($chat['username']) {
                                            $displayName .= ' (@' . $chat['username'] . ')';
                                        }
                                        $userLanguage = $chat['language'] ?? 'en';
                                        $languageFlag = $userLanguage === 'ru' ? '🇷🇺' : '🇺🇸';
                                        $chatStatus = $chat['status'] ?? 'open'; // Для совместимости со старыми чатами
                                        $statusClass = $chatStatus === 'closed' ? 'table-secondary' : ($unreadCount > 0 ? 'table-warning' : '');
                                        ?>
                                        <tr class="<?php echo $statusClass; ?>" data-chat-id="<?php echo htmlspecialchars($chatId); ?>">
                                            <td>
                                                <strong><?php echo htmlspecialchars($displayName); ?></strong><br>
                                                <small class="text-muted">ID: <?php echo $chat['user_id']; ?></small>
                                            </td>
                                            <td>
                                                <?php if ($lastMessage): ?>
                                                    <div class="message-preview">
                                                        <?php 
                                                        $messageText = htmlspecialchars($lastMessage['text']);
                                                        if (strlen($messageText) > 100) {
                                                            $messageText = substr($messageText, 0, 100) . '...';
                                                        }
                                                        echo $messageText;
                                                        ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo $lastMessage['from_user'] ? 'От пользователя' : 'От поддержки'; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <em class="text-muted">Нет сообщений</em>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $chatStatus === 'closed' ? 'bg-secondary' : 'bg-success'; ?>"
                                                      id="status-badge-<?php echo htmlspecialchars($chatId); ?>">
                                                    <?php echo $chatStatus === 'closed' ? '🔒 Закрыто' : '🟢 Открыто'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span title="<?php echo $userLanguage === 'ru' ? 'Русский' : 'English'; ?>">
                                                    <?php echo $languageFlag; ?> <?php echo strtoupper($userLanguage); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($unreadCount > 0): ?>
                                                    <span class="badge bg-warning text-dark"><?php echo $unreadCount; ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">—</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('d.m.Y H:i', strtotime($chat['updated_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="support_chat.php?chat_id=<?php echo urlencode($chatId); ?>"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="bi bi-chat-dots"></i> Открыть
                                                    </a>

                                                    <?php if ($chatStatus === 'open'): ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-warning"
                                                                onclick="updateChatStatus('<?php echo htmlspecialchars($chatId); ?>', 'closed')"
                                                                title="Закрыть чат">
                                                            <i class="bi bi-lock"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-success"
                                                                onclick="updateChatStatus('<?php echo htmlspecialchars($chatId); ?>', 'open')"
                                                                title="Открыть чат">
                                                            <i class="bi bi-unlock"></i>
                                                        </button>
                                                    <?php endif; ?>

                                                    <button type="button"
                                                            class="btn btn-sm btn-danger"
                                                            onclick="deleteSupportChat('<?php echo htmlspecialchars($chatId); ?>')"
                                                            title="Удалить чат">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <!-- Пагинация -->
                    <?php if ($totalPages > 1): ?>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <small class="text-muted">
                                    Показано <?php echo count($supportChats); ?> из <?php echo $totalChats; ?> чатов
                                    (страница <?php echo $page; ?> из <?php echo $totalPages; ?>)
                                </small>
                            </div>
                            <nav aria-label="Навигация по страницам">
                                <ul class="pagination pagination-sm mb-0">
                                    <!-- Предыдущая страница -->
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                <i class="bi bi-chevron-left"></i> Предыдущая
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link"><i class="bi bi-chevron-left"></i> Предыдущая</span>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Номера страниц -->
                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    if ($startPage > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                                        </li>
                                        <?php if ($startPage > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($endPage < $totalPages): ?>
                                        <?php if ($endPage < $totalPages - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>"><?php echo $totalPages; ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Следующая страница -->
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                Следующая <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">Следующая <i class="bi bi-chevron-right"></i></span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Модальное окно для проверки webhook -->
<div class="modal fade" id="webhookModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webhookModalTitle">Информация о Webhook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="webhookModalBody">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <p class="mt-2">Проверка webhook...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.message-preview {
    max-width: 300px;
    word-wrap: break-word;
}
</style>

<script>
// Проверка webhook
function checkWebhook() {
    const modal = new bootstrap.Modal(document.getElementById('webhookModal'));
    document.getElementById('webhookModalTitle').textContent = 'Информация о Webhook';
    document.getElementById('webhookModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
            <p class="mt-2">Проверка webhook...</p>
        </div>
    `;
    modal.show();

    fetch('webhook_manager.php?action=check')
        .then(response => response.json())
        .then(data => {
            let content = '<h6>Статус Webhook:</h6>';
            if (data.success) {
                content += '<div class="alert alert-success">✅ Webhook работает корректно</div>';
                content += '<h6>Информация о боте:</h6>';
                content += `<pre>${JSON.stringify(data.bot_info, null, 2)}</pre>`;
                content += '<h6>Информация о webhook:</h6>';
                content += `<pre>${JSON.stringify(data.webhook_info, null, 2)}</pre>`;
            } else {
                content += '<div class="alert alert-danger">❌ Ошибка: ' + data.error + '</div>';
            }
            document.getElementById('webhookModalBody').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('webhookModalBody').innerHTML =
                '<div class="alert alert-danger">❌ Ошибка сети: ' + error.message + '</div>';
        });
}

// Настройка webhook
function setupWebhook() {
    const modal = new bootstrap.Modal(document.getElementById('webhookModal'));
    document.getElementById('webhookModalTitle').textContent = 'Настройка Webhook';
    document.getElementById('webhookModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
            <p class="mt-2">Настройка webhook...</p>
        </div>
    `;
    modal.show();

    fetch('webhook_manager.php?action=setup')
        .then(response => response.json())
        .then(data => {
            let content = '<h6>Результат настройки:</h6>';
            if (data.success) {
                content += '<div class="alert alert-success">✅ Webhook успешно настроен!</div>';
                content += '<p><strong>URL:</strong> ' + data.webhook_url + '</p>';
                content += '<p><strong>Бот:</strong> @' + data.bot_username + '</p>';
            } else {
                content += '<div class="alert alert-danger">❌ Ошибка: ' + data.error + '</div>';
            }
            document.getElementById('webhookModalBody').innerHTML = content;

            // Обновляем страницу через 2 секунды при успехе
            if (data.success) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            document.getElementById('webhookModalBody').innerHTML =
                '<div class="alert alert-danger">❌ Ошибка сети: ' + error.message + '</div>';
        });
}

// Удаление webhook
function removeWebhook() {
    if (!confirm('Вы уверены, что хотите удалить webhook? Бот поддержки перестанет получать сообщения.')) {
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('webhookModal'));
    document.getElementById('webhookModalTitle').textContent = 'Удаление Webhook';
    document.getElementById('webhookModalBody').innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-danger" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
            <p class="mt-2">Удаление webhook...</p>
        </div>
    `;
    modal.show();

    fetch('webhook_manager.php?action=remove')
        .then(response => response.json())
        .then(data => {
            let content = '<h6>Результат удаления:</h6>';
            if (data.success) {
                content += '<div class="alert alert-success">✅ Webhook успешно удален!</div>';
                content += '<p>Бот поддержки больше не будет получать сообщения.</p>';
            } else {
                content += '<div class="alert alert-danger">❌ Ошибка: ' + data.error + '</div>';
            }
            document.getElementById('webhookModalBody').innerHTML = content;

            // Обновляем страницу через 2 секунды при успехе
            if (data.success) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            document.getElementById('webhookModalBody').innerHTML =
                '<div class="alert alert-danger">❌ Ошибка сети: ' + error.message + '</div>';
        });
}

// Обновление статуса чата
function updateChatStatus(chatId, status) {
    const statusText = status === 'closed' ? 'закрыть' : 'открыть';

    if (!confirm(`Вы уверены, что хотите ${statusText} этот чат?`)) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'update_status');
    formData.append('chat_id', chatId);
    formData.append('status', status);

    fetch('manage_support_chat.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Обновляем бейдж статуса
            const statusBadge = document.getElementById('status-badge-' + chatId);
            if (statusBadge) {
                if (status === 'closed') {
                    statusBadge.className = 'badge bg-secondary';
                    statusBadge.textContent = '🔒 Закрыто';
                } else {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = '🟢 Открыто';
                }
            }

            // Обновляем строку таблицы
            const row = document.querySelector(`tr[data-chat-id="${chatId}"]`);
            if (row) {
                if (status === 'closed') {
                    row.className = 'table-secondary';
                } else {
                    row.className = '';
                }
            }

            // Показываем уведомление
            showNotification(`Чат успешно ${status === 'closed' ? 'закрыт' : 'открыт'}!`, 'success');

            // Обновляем страницу через 1 секунду для обновления статистики
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Ошибка при обновлении статуса: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Ошибка сети: ' + error.message, 'error');
    });
}

// Удаление чата
function deleteSupportChat(chatId) {
    if (!confirm('Вы уверены, что хотите удалить этот чат?\n\nВНИМАНИЕ: Будут удалены все сообщения этого чата. Это действие нельзя отменить!')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('chat_id', chatId);

    fetch('manage_support_chat.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Удаляем строку из таблицы
            const row = document.querySelector(`tr[data-chat-id="${chatId}"]`);
            if (row) {
                row.remove();
            }

            showNotification('Чат успешно удален!', 'success');

            // Обновляем страницу через 1 секунду для обновления статистики
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Ошибка при удалении чата: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Ошибка сети: ' + error.message, 'error');
    });
}

// Показать уведомление
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? '✅' : '❌';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${icon} ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Автоматически удаляем уведомление через 5 секунд
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php include 'templates/footer.php'; ?>
