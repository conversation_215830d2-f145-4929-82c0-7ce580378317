/* 
 * CUSTOM.CSS - Автоматически сгенерированный файл
 * Создан: 2025-06-16 19:28:51
 * Тема: geometric
 * Акцентный цвет: #ff7700
 */

:root {
  /* Цвета */
  --primary-dark: #1a1a1a !important;
  --accent-orange: #ff7700 !important;
  --bg-card: #2a2a2a !important;
  --text-primary: #ffffff !important;
  --bg-secondary: #333333 !important;
  --border-color: #4a4a4a !important;
  --global: Array !important;
  --header: Array !important;
  --navigation: Array !important;
  --cards: Array !important;
  --buttons: Array !important;
  --calculator: Array !important;
  --history: Array !important;
  --forms: Array !important;
  --juicy-green: #00ff7f !important;
  --error-red: #ff4444 !important;
  --warning-yellow: #ffd700 !important;
  --info-blue: #00bfff !important;
  --nav-bg: #2a2a2a !important;
  --nav-text: #ffffff !important;
  --nav-active: #ff6b35 !important;
  --icon-color: #391204 !important;
  --button-primary: #ff6b35 !important;
  --button-secondary: #666666 !important;
  --button-text: #ffffff !important;
  --history-border: #ff6b35 !important;

  /* Эффекты */
  --glitch-speed: 3 !important;
  --glitch-count: 3 !important;
  --glitch-opacity: 0.8 !important;
  --bg-opacity: 0.8 !important;
  --geometric-size: 1 !important;

}

/* Основные стили */
body {
  background: var(--primary-dark) !important;
  color: var(--text-primary) !important;
}

/* Карточки */
.card, .earn-block, .friends-block {
  background: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

/* Кнопки - все возможные селекторы */
.action-button, .btn-primary, button, .btn, input[type='submit'], input[type='button'] {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  background-image: linear-gradient(145deg, var(--accent-orange), var(--accent-orange)) !important;
  border-color: var(--accent-orange) !important;
  border: 1px solid var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Кнопки в секциях заработка и друзей */
.earn-section button, .friends-section button, .earn-block button, .friends-block button {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

.action-button:hover, .btn-primary:hover, button:hover, .btn:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

.purple-button:hover, .blue-button:hover, .orange-button:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

.primary-action:hover, .secondary-action:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

.copy-button:hover, .refresh-history-btn:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

/* Принудительное переопределение всех кнопок */
*[class*='button'], *[class*='btn'] {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Навигация - исправленная */
.nav-button.active {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Заголовки и иконки */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
}

/* Иконки заголовков - максимально агрессивные */
h2 svg, h3 svg, h4 svg, h2 > svg, h3 > svg, h4 > svg {
  color: var(--accent-orange) !important;
  fill: var(--accent-orange) !important;
  stroke: var(--accent-orange) !important;
}

/* Иконки в заголовках с style атрибутами */
h2 svg[style], h3 svg[style], h4 svg[style] {
  color: var(--accent-orange) !important;
  fill: var(--accent-orange) !important;
  stroke: var(--accent-orange) !important;
}

/* Переопределение всех SVG с старым оранжевым цветом */
[style*='color: #ff6b35'], [style*='stroke: #ff6b35'], [style*='fill: #ff6b35'] {
  color: var(--accent-orange) !important;
  fill: var(--accent-orange) !important;
  stroke: var(--accent-orange) !important;
}

/* История выплат */
.history-item, .withdrawal-history .history-item {
  border-left: 3px solid var(--accent-orange) !important;
}

.history-item svg, .withdrawal-history svg {
  color: var(--accent-orange) !important;
  fill: var(--accent-orange) !important;
  stroke: var(--accent-orange) !important;
}

.history-status.pending, .status-pending {
  color: var(--accent-orange) !important;
  background: rgba(255, 107, 53, 0.1) !important;
}

.history-status.completed, .status-completed {
  color: var(--juicy-green) !important;
  background: rgba(0, 255, 127, 0.1) !important;
}

/* Навигация */
.app-nav {
  background: var(--bg-card) !important;
  border-top: 1px solid var(--border-color) !important;
}

.nav-button {
  color: var(--text-primary) !important;
}

/* Хедер приложения */
.app-header {
  background: var(--bg-card) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

/* Аватар пользователя */
.user-avatar {
  border: 2px solid var(--accent-orange) !important;
  background: var(--accent-orange) !important;
}

.user-avatar-icon {
  color: var(--primary-dark) !important;
}

/* Специфичные кнопки и элементы */
#shareButton, #copyButton, .copy-button, .share-button {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

.nav-button, .navigation-button, .bottom-nav button {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

.nav-button.active, .navigation-button.active {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* ИСПРАВЛЕНИЕ КОНКРЕТНЫХ ЭЛЕМЕНТОВ ИЗ СКРИНШОТА */
.balance-info, .clickable-balance {
  background: var(--juicy-green) !important;
  background-color: var(--juicy-green) !important;
  border-color: var(--juicy-green) !important;
  color: #ffffff !important;
}

.current-balance-display, .balance-display {
  background: rgba(0, 255, 255, 0.05) !important;
  border: 1px solid var(--border-color) !important;
}

.currency-tab:not(.active) {
  background: var(--bg-secondary) !important;
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

.currency-tab.active {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* МАКСИМАЛЬНО АГРЕССИВНЫЕ СТИЛИ ТОЛЬКО ДЛЯ КНОПОК ДЕЙСТВИЙ */
button[style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='button'][style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='submit'][style*='background']:not(.balance-info):not(.clickable-balance):not(.currency-tab) {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
}

/* Переопределение inline стилей только для кнопок */
button[style*='background-color']:not(.balance-info):not(.clickable-balance):not(.currency-tab), input[type='button'][style*='background-color']:not(.balance-info):not(.clickable-balance):not(.currency-tab) {
  background-color: var(--accent-orange) !important;
}

/* Кнопки с классами button/btn (исключая специальные) */
[class*='button']:not(.nav-button):not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), [class*='btn']:not(.nav-button):not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* ЯДЕРНЫЙ ВАРИАНТ - максимальная специфичность для кликабельных элементов */
html body *[onclick]:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), html body *[role='button']:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), html body *.clickable:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Специфичные элементы приложения */
#earnButton, #friendsButton, #mainButton {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Конкретные кнопки из приложения */
.purple-button, .blue-button, .orange-button, .primary-action, .secondary-action {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

#openLinkButton, #watchVideoButton, #openAdButton, #request-withdrawal-button {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

#share-app-button, #copy-referral-button, #refresh-stats-button {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

.copy-button, .refresh-history-btn {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  border-color: var(--accent-orange) !important;
  color: #ffffff !important;
}

/* Hover эффекты для всех ID кнопок */
#openLinkButton:hover, #watchVideoButton:hover, #openAdButton:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

#request-withdrawal-button:hover, #share-app-button:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

#copy-referral-button:hover, #refresh-stats-button:hover {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

/* Универсальный hover для кликабельных элементов */
*[onclick]:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), *[role='button']:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display), *.clickable:hover:not(.balance-info):not(.clickable-balance):not(.currency-tab):not(.balance-display):not(.current-balance-display) {
  background: var(--accent-orange) !important;
  background-color: var(--accent-orange) !important;
  opacity: 0.9 !important;
  border-color: var(--accent-orange) !important;
}

/* ИСПРАВЛЕНИЕ: Красивая кнопка перехода с правильными отступами */
.action-status-card {
    margin: 30px 0 !important;
    padding: 20px 24px !important;
    border-radius: 16px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    min-height: 64px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 12px !important;
    position: relative !important;
    overflow: hidden !important;
}

.action-status-card .status-icon {
    width: 24px !important;
    height: 24px !important;
    flex-shrink: 0 !important;
}

.action-status-card .status-icon svg {
    width: 100% !important;
    height: 100% !important;
}

.action-status-card .status-text {
    flex: 1 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
}

.action-status-card.available {
    background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%) !important;
    color: #1a1a1a !important;
    border-color: #00ff88 !important;
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3) !important;
    cursor: pointer !important;
    text-align: center !important;
}

.action-status-card.available:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 35px rgba(0, 255, 136, 0.4) !important;
    background: linear-gradient(135deg, #00cc6a 0%, #00aa55 100%) !important;
}

.action-status-card.available:active {
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.3) !important;
}

.action-status-card.neutral {
    background: rgba(102, 102, 102, 0.1) !important;
    color: #666 !important;
    border-color: #444 !important;
    text-align: center !important;
}

.action-status-card.insufficient {
    background: rgba(255, 107, 107, 0.1) !important;
    color: #ff6b6b !important;
    border-color: #ff6b6b !important;
    text-align: center !important;
}

.action-status-card.warning {
    background: rgba(255, 167, 38, 0.1) !important;
    color: #ffa726 !important;
    border-color: #ffa726 !important;
    text-align: center !important;
}

/* Стили для валидации формы выплат */
.validation-card {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid #ff6b6b;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    color: #ff6b6b;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
}

.validation-card.valid {
    background: rgba(0, 255, 136, 0.1);
    border-color: #00ff88;
    color: #00ff88;
}

.validation-card.invalid {
    background: rgba(255, 107, 107, 0.1);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.validation-card.warning {
    background: rgba(255, 167, 38, 0.1);
    border-color: #ffa726;
    color: #ffa726;
}

.validation-card.hidden {
    display: none !important;
}

