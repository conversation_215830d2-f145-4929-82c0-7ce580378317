<?php
/**
 * Исправление конкретной выплаты по ID
 */

require_once 'api/config.php';
require_once 'api/db_mock.php';

// ID выплаты для исправления
$payoutId = '5003151124';

// Загружаем данные пользователей
$userData = loadUserData();

$fixed = false;

foreach ($userData as $userId => &$user) {
    if (empty($user['withdrawals']) || !is_array($user['withdrawals'])) continue;

    foreach ($user['withdrawals'] as &$withdrawal) {
        if (($withdrawal['payout_id'] ?? '') === $payoutId) {
            // Обновляем статус и данные
            $withdrawal['status'] = 'confirmed';
            $withdrawal['updated_at'] = date('Y-m-d H:i:s');
            $withdrawal['txid'] = '0x0969e6cbcddb1f62d7bc6e4d76d1cec9834b08e14c19ba9ba50f8f5dade89c06';
            
            $fixed = true;
            echo "✅ Выплата $payoutId успешно обновлена!\n";
            break 2;
        }
    }
}

if ($fixed) {
    // Сохраняем изменения
    if (saveUserData($userData)) {
        echo "✅ Данные пользователей успешно сохранены\n";
    } else {
        echo "❌ Ошибка сохранения данных\n";
    }
} else {
    echo "❌ Выплата $payoutId не найдена\n";
}
?>