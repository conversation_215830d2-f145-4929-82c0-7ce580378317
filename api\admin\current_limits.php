<?php
/**
 * api/admin/current_limits.php
 * API для получения текущих лимитов системы безопасности
 */

// Включаем отображение ошибок для отладки
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Проверка авторизации администратора
session_start();

// Отладочная информация
error_log("current_limits.php: Проверка сессии. Session ID: " . session_id());
error_log("current_limits.php: admin_logged_in = " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set'));

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    error_log("current_limits.php: Авторизация не пройдена");
    http_response_code(401);
    echo json_encode([
        'error' => 'Требуется авторизация администратора',
        'success' => false,
        'session_id' => session_id(),
        'debug' => [
            'session_exists' => isset($_SESSION['admin_logged_in']),
            'session_value' => $_SESSION['admin_logged_in'] ?? null
        ]
    ]);
    exit;
}

// Подключаем функции безопасности
require_once __DIR__ . '/../security.php';

header('Content-Type: application/json; charset=utf-8');

try {
    error_log("current_limits.php: Начинаем получение лимитов");

    // Получаем текущие лимиты
    $limitsInfo = getCurrentLimits();
    error_log("current_limits.php: Лимиты получены успешно");

    // Добавляем дополнительную информацию
    $response = [
        'success' => true,
        'timestamp' => time(),
        'current_time_utc' => gmdate('Y-m-d H:i:s'),
        'current_time_local' => date('Y-m-d H:i:s'),
        'limits_info' => $limitsInfo,
        'status' => $limitsInfo['is_peak_time'] ? 'PEAK_TIME' : 'NORMAL_TIME',
        'multipliers' => [
            'peak_hourly_multiplier' => 2.0,
            'peak_daily_multiplier' => 1.5
        ],
        'debug' => [
            'session_id' => session_id(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]
    ];

    error_log("current_limits.php: Отправляем ответ");
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    error_log("current_limits.php ERROR: " . $e->getMessage());
    error_log("current_limits.php ERROR Stack: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'error' => 'Ошибка получения информации о лимитах: ' . $e->getMessage(),
        'success' => false,
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
