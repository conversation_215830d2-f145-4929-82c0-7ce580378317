
# Защита лог-файлов от прямого доступа
<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Защита JSON файлов с данными (кроме admin API)
<FilesMatch "^(?!.*admin).*\.json$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Запрещаем листинг директорий
Options -Indexes

# Telegram Mini-App Support
<IfModule mod_headers.c>
    # Разрешаем отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"

    # CORS заголовки для API
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Защита от SQL инъекций в URL
RewriteEngine On
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER) [NC]
RewriteRule ^(.*)$ - [F,L]
