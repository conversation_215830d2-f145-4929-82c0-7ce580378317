/**
 * localization.js
 * Система локализации для мини-приложения
 */

class AppLocalization {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.fallbackLanguage = 'en';
        this.isLoaded = false;

        // Русскоязычные страны
        this.russianSpeakingCountries = [
            'RU', 'BY', 'KZ', 'KG', 'TJ', 'UZ',
            'AM', 'AZ', 'GE', 'MD', 'UA'
        ];
    }

    /**
     * Инициализирует систему локализации
     */
    async init() {
        console.log('🌐 [Localization] Инициализация системы локализации...');

        try {
            // Определяем язык (сначала по IP, потом по Telegram/браузеру)
            await this.detectLanguageByIP();

            // Загружаем переводы после определения языка
            await this.loadTranslations();

            console.log('🌐 [Localization] Система локализации инициализирована успешно');

        } catch (error) {
            console.warn('⚠️ [Localization] Ошибка инициализации, используем значения по умолчанию:', error);
            this.isLoaded = true; // Помечаем как загруженное, чтобы не блокировать работу
        }
    }

    /**
     * Загружает переводы
     */
    async loadTranslations() {
        try {
            // Загружаем русский язык
            const ruResponse = await fetch('./locales/ru.json');
            if (ruResponse.ok) {
                const ruData = await ruResponse.json();
                this.translations['ru'] = ruData.app;
            }

            // Загружаем английский язык
            const enResponse = await fetch('./locales/en.json');
            if (enResponse.ok) {
                const enData = await enResponse.json();
                this.translations['en'] = enData.app;
            }

            this.isLoaded = true;

            // Применяем переводы к интерфейсу после загрузки
            this.applyTranslations();

        // Применяем переводы к элементам с data-translate атрибутами
        this.applyDataTranslateElements();

            console.log('Переводы загружены успешно');

        } catch (error) {
            console.error('Ошибка загрузки переводов:', error);
            this.isLoaded = true; // Помечаем как загруженное, чтобы не блокировать работу
        }
    }

    /**
     * Определяет язык пользователя по IP-адресу (основной метод)
     */
    async detectLanguageByIP() {
        console.log('🌍 [Localization] Определение языка по IP-адресу...');

        try {
            // Используем бесплатный API для определения страны
            const response = await fetch('https://ipapi.co/json/', {
                timeout: 5000 // 5 секунд таймаут
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            const countryCode = data.country_code;

            console.log(`🌍 [Localization] Определена страна: ${countryCode} (${data.country_name || 'Unknown'})`);

            // Проверяем, является ли страна русскоязычной
            if (countryCode && this.russianSpeakingCountries.includes(countryCode.toUpperCase())) {
                this.currentLanguage = 'ru';
                console.log('🇷🇺 [Localization] Установлен русский язык по IP (русскоязычная страна)');
                return;
            } else {
                this.currentLanguage = 'en';
                console.log('🇺🇸 [Localization] Установлен английский язык по IP (не русскоязычная страна)');
                return;
            }

        } catch (error) {
            console.warn('⚠️ [Localization] Ошибка определения IP, используем fallback методы:', error);
            // Если IP определение не сработало, используем старый метод
            this.detectLanguageFallback();
        }
    }

    /**
     * Определяет язык пользователя (fallback метод через Telegram/браузер)
     */
    detectLanguageFallback() {
        console.log('🔄 [Localization] Fallback: Определение языка через Telegram/браузер...');

        // Список русскоязычных кодов языков
        const russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];

        // По умолчанию русский (для fallback)
        this.currentLanguage = 'ru';

        // Проверяем Telegram Web App данные
        if (window.Telegram && window.Telegram.WebApp && window.Telegram.WebApp.initDataUnsafe) {
            const user = window.Telegram.WebApp.initDataUnsafe.user;
            if (user) {
                console.log('[Localization] Данные пользователя Telegram:', user);

                // Проверяем язык интерфейса
                if (user.language_code) {
                    const langCode = user.language_code.toLowerCase();
                    console.log('[Localization] language_code из Telegram:', langCode);

                    // Если язык входит в список русскоязычных - ставим русский
                    if (russianLanguageCodes.includes(langCode)) {
                        this.currentLanguage = 'ru';
                        console.log('🇷🇺 [Localization] Fallback: Установлен русский язык по language_code');
                        return;
                    } else {
                        this.currentLanguage = 'en';
                        console.log('🇺🇸 [Localization] Fallback: Установлен английский язык по language_code');
                        return;
                    }
                }

                // Проверяем страну (если доступно)
                if (user.country_code) {
                    const countryCode = user.country_code.toUpperCase();
                    console.log('[Localization] country_code из Telegram:', countryCode);
                    if (this.russianSpeakingCountries.includes(countryCode)) {
                        this.currentLanguage = 'ru';
                        console.log('🇷🇺 [Localization] Fallback: Установлен русский язык по country_code');
                        return;
                    } else {
                        this.currentLanguage = 'en';
                        console.log('🇺🇸 [Localization] Fallback: Установлен английский язык по country_code');
                        return;
                    }
                }
            }
        }

        // Проверяем язык браузера
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang) {
            const browserLangCode = browserLang.toLowerCase().split('-')[0];
            console.log('[Localization] Язык браузера:', browserLangCode);
            if (russianLanguageCodes.includes(browserLangCode)) {
                this.currentLanguage = 'ru';
                console.log('🇷🇺 [Localization] Fallback: Установлен русский язык по языку браузера');
                return;
            } else {
                this.currentLanguage = 'en';
                console.log('🇺🇸 [Localization] Fallback: Установлен английский язык по языку браузера');
                return;
            }
        }

        console.log('🇷🇺 [Localization] Fallback: Установлен русский язык по умолчанию');
    }

    /**
     * Обновляет элемент по ID с переводом
     */
    updateElementById(elementId, translationKey) {
        const element = document.getElementById(elementId);
        if (element) {
            const translation = this.get(translationKey);
            element.textContent = translation;
        }
    }

    /**
     * 🛡️ СОХРАНЕНИЕ ИКОНОК И REWARD BADGES! Обновляет элемент по ID с переводом, сохраняя SVG иконки и reward badges
     * Эта функция создана для предотвращения удаления иконок и reward badges из заголовков и кнопок
     */
    updateElementByIdPreservingIcons(elementId, translationKey) {
        const element = document.getElementById(elementId);
        if (element) {
            // Сохраняем все SVG иконки и reward badges
            const svgs = Array.from(element.querySelectorAll('svg'));
            const rewardBadges = Array.from(element.querySelectorAll('.reward-badge'));

            // Получаем перевод
            const translation = this.get(translationKey);

            // Если это кнопка с .button-text, обновляем только текст
            const buttonTextSpan = element.querySelector('.button-text');
            if (buttonTextSpan) {
                buttonTextSpan.textContent = translation;
                console.log(`[IconPreservation] Обновлен текст в ${elementId} с сохранением всех элементов`);
                return;
            }

            // Для других элементов - очищаем содержимое и восстанавливаем
            element.innerHTML = '';

            // Восстанавливаем иконки
            svgs.forEach(svg => element.appendChild(svg));

            // Добавляем текст
            const textNode = document.createTextNode(translation);
            element.appendChild(textNode);

            // Восстанавливаем reward badges
            rewardBadges.forEach(badge => element.appendChild(badge));

            console.log(`[IconPreservation] Обновлен ${elementId} с сохранением иконок и reward badges`);
        }
    }

    /**
     * Получает перевод по ключу
     */
    get(key, params = {}) {
        // Если переводы еще не загружены, возвращаем ключ
        if (!this.isLoaded || !this.translations[this.currentLanguage]) {
            return key;
        }

        const keys = key.split('.');
        let translation = this.translations[this.currentLanguage] || {};

        // Ищем перевод по ключу
        for (const k of keys) {
            if (translation && typeof translation === 'object' && translation[k] !== undefined) {
                translation = translation[k];
            } else {
                // Пробуем fallback язык
                translation = this.translations[this.fallbackLanguage] || {};
                for (const fallbackKey of keys) {
                    if (translation && typeof translation === 'object' && translation[fallbackKey] !== undefined) {
                        translation = translation[fallbackKey];
                    } else {
                        return key; // Возвращаем ключ, если перевод не найден
                    }
                }
                break;
            }
        }

        // Если это не строка, возвращаем ключ
        if (typeof translation !== 'string') {
            return key;
        }

        // Заменяем параметры в строке
        for (const [param, value] of Object.entries(params)) {
            translation = translation.replace(new RegExp(`{${param}}`, 'g'), value);
        }

        return translation;
    }

    /**
     * Переключает язык интерфейса
     */
    async switchLanguage(language) {
        console.log(`🌐 [Localization] Переключение языка на: ${language}`);

        if (language !== 'ru' && language !== 'en') {
            console.warn(`⚠️ [Localization] Неподдерживаемый язык: ${language}`);
            return;
        }

        this.currentLanguage = language;

        // Если переводы еще не загружены, загружаем их
        if (!this.isLoaded) {
            await this.loadTranslations();
        }

        // Применяем переводы к интерфейсу
        this.applyTranslations();

        // Применяем переводы к элементам с data-translate атрибутами
        this.applyDataTranslateElements();

        // Обновляем счетчики рекламы
        if (window.adCountersManager) {
            window.adCountersManager.updateLanguage(language);
        }

        console.log(`✅ [Localization] Язык переключен на: ${language}`);
    }

    /**
     * Применяет переводы к элементам интерфейса
     */
    applyTranslations() {
        console.log(`[Localization] Применение переводов для языка: ${this.currentLanguage}`);

        if (!this.isLoaded || !this.translations[this.currentLanguage]) {
            console.warn('[Localization] Переводы еще не загружены, пропускаем применение');
            return;
        }

        // === СТАТУСНЫЕ СООБЩЕНИЯ ===
        const statusMessage = document.getElementById('status-message');
        if (statusMessage && statusMessage.textContent) {
            const statusText = statusMessage.textContent.trim();
            // Переводим стандартные статусные сообщения
            if (statusText === 'Ожидание инициализации...' || statusText === 'Waiting for initialization...') {
                statusMessage.textContent = this.get('status.waiting_initialization');
            } else if (statusText === 'Инициализация приложения...' || statusText === 'Initializing application...') {
                statusMessage.textContent = this.get('status.initializing_app');
            } else if (statusText === 'Загрузка модулей...' || statusText === 'Loading modules...') {
                statusMessage.textContent = this.get('status.loading_modules');
            } else if (statusText === 'Приложение готово к работе' || statusText === 'Application ready to work') {
                statusMessage.textContent = this.get('status.app_ready');
            }
        }

        // === НАВИГАЦИЯ ===
        // Главная
        const navHome = document.querySelector('#nav-home .nav-text');
        if (navHome) navHome.textContent = this.get('nav.home');

        // Заработок
        const navEarn = document.querySelector('#nav-earn .nav-text');
        if (navEarn) navEarn.textContent = this.get('nav.earnings');

        // Друзья
        const navFriends = document.querySelector('#nav-friends .nav-text');
        if (navFriends) navFriends.textContent = this.get('nav.friends');

        // === ШАПКА ===
        // Валюта баланса
        const balanceCurrency = document.querySelector('.balance-currency');
        if (balanceCurrency) balanceCurrency.textContent = this.get('currency.coins');

        // === ГЛАВНАЯ СТРАНИЦА (ЗАДАНИЯ) ===
        // 🛡️ СОХРАНЯЕМ ИКОНКИ! Используем специальную функцию для заголовков с иконками
        this.updateElementByIdPreservingIcons('tasks-title', 'tasks.title');

        // Кнопки заданий - 🛡️ СОХРАНЯЕМ ИКОНКИ И REWARD BADGES!
        const openLinkBtn = document.getElementById('openLinkButton');
        if (openLinkBtn) {
            // Сохраняем SVG иконки и reward badges
            const svgs = Array.from(openLinkBtn.querySelectorAll('svg'));
            const rewardBadges = Array.from(openLinkBtn.querySelectorAll('.reward-badge'));
            const translation = this.get('tasks.open_link');

            // Обновляем только текст в span.button-text
            const buttonTextSpan = openLinkBtn.querySelector('.button-text');
            if (buttonTextSpan) {
                buttonTextSpan.textContent = translation;
            } else {
                // Если span не найден, создаем структуру заново
                openLinkBtn.innerHTML = '';
                svgs.forEach(svg => openLinkBtn.appendChild(svg));
                const textSpan = document.createElement('span');
                textSpan.className = 'button-text';
                textSpan.textContent = translation;
                openLinkBtn.appendChild(textSpan);
                rewardBadges.forEach(badge => openLinkBtn.appendChild(badge));
            }
        }

        const watchVideoBtn = document.getElementById('watchVideoButton');
        if (watchVideoBtn) {
            // Сохраняем SVG иконки и reward badges
            const svgs = Array.from(watchVideoBtn.querySelectorAll('svg'));
            const rewardBadges = Array.from(watchVideoBtn.querySelectorAll('.reward-badge'));
            const translation = this.get('tasks.watch_video');

            // Обновляем только текст в span.button-text
            const buttonTextSpan = watchVideoBtn.querySelector('.button-text');
            if (buttonTextSpan) {
                buttonTextSpan.textContent = translation;
            } else {
                // Если span не найден, создаем структуру заново
                watchVideoBtn.innerHTML = '';
                svgs.forEach(svg => watchVideoBtn.appendChild(svg));
                const textSpan = document.createElement('span');
                textSpan.className = 'button-text';
                textSpan.textContent = translation;
                watchVideoBtn.appendChild(textSpan);
                rewardBadges.forEach(badge => watchVideoBtn.appendChild(badge));
            }
        }

        const openAdBtn = document.getElementById('openAdButton');
        if (openAdBtn) {
            // Сохраняем SVG иконки и reward badges
            const svgs = Array.from(openAdBtn.querySelectorAll('svg'));
            const rewardBadges = Array.from(openAdBtn.querySelectorAll('.reward-badge'));
            const translation = this.get('tasks.watch_ad');

            // Обновляем только текст в span.button-text
            const buttonTextSpan = openAdBtn.querySelector('.button-text');
            if (buttonTextSpan) {
                buttonTextSpan.textContent = translation;
            } else {
                // Если span не найден, создаем структуру заново
                openAdBtn.innerHTML = '';
                svgs.forEach(svg => openAdBtn.appendChild(svg));
                const textSpan = document.createElement('span');
                textSpan.className = 'button-text';
                textSpan.textContent = translation;
                openAdBtn.appendChild(textSpan);
                rewardBadges.forEach(badge => openAdBtn.appendChild(badge));
            }
        }

        // === СЕКЦИЯ ЗАРАБОТКА ===
        // 🛡️ СОХРАНЯЕМ ИКОНКИ! Используем специальную функцию для заголовков с иконками
        this.updateElementByIdPreservingIcons('earnings-title', 'earnings.title');
        this.updateElementByIdPreservingIcons('balance-title', 'earnings.your_balance');
        this.updateElementByIdPreservingIcons('calculator-title', 'earnings.calculator_title');
        this.updateElementByIdPreservingIcons('withdrawal-title', 'earnings.withdrawal_request');
        this.updateElementByIdPreservingIcons('history-title', 'earnings.withdrawal_history');

        const earnBalanceCurrency = document.querySelector('#earn-section .balance-currency');
        if (earnBalanceCurrency) earnBalanceCurrency.textContent = this.get('currency.coins');

        const availableText = document.querySelector('#earn-section .hint');
        if (availableText) {
            const availableAmount = document.getElementById('available-withdrawal')?.textContent || '0';
            availableText.innerHTML = this.get('earnings.available_for_withdrawal', {amount: availableAmount});
        }



        // Заголовок калькулятора
        const calculatorSubtitle = document.querySelector('.calculator-subtitle');
        if (calculatorSubtitle) calculatorSubtitle.textContent = this.get('earnings.exchange_rate');

        const balanceLabel = document.querySelector('.balance-label');
        if (balanceLabel) balanceLabel.textContent = this.get('earnings.your_balance') + ':';

        // Поле ввода суммы - НЕ ПЕРЕЗАПИСЫВАЕМ, используем data-translate
        // const calcAmountLabel = document.querySelector('label[for="calc-amount"]');
        // if (calcAmountLabel) calcAmountLabel.textContent = this.get('earnings.amount_to_withdraw') + ':';

        const calcAmountInput = document.getElementById('calc-amount');
        if (calcAmountInput) calcAmountInput.placeholder = this.get('placeholders.enter_coins_amount');

        const inputSuffix = document.querySelector('.input-suffix');
        if (inputSuffix) inputSuffix.textContent = this.get('currency.coins');

        // Статус баланса в калькуляторе - РАСШИРЕННАЯ ПРОВЕРКА
        const balanceCheck = document.getElementById('balance-check');
        if (balanceCheck) {
            const currentText = balanceCheck.textContent.trim();
            if (currentText === 'Введите сумму' || currentText === 'Enter amount' ||
                currentText === 'Введите сумму для расчета' || currentText === 'Enter amount for calculation' ||
                currentText === 'ВВЕДИТЕ СУММУ' || currentText === 'ENTER AMOUNT') {
                balanceCheck.textContent = this.get('earnings.enter_amount_for_calculation');
            }
        }

        // Табы калькулятора и вывода
        const calculatorTab = document.getElementById('calculator-tab');
        if (calculatorTab) calculatorTab.textContent = this.get('buttons.calculator_tab');

        const withdrawalTab = document.getElementById('withdrawal-tab');
        if (withdrawalTab) withdrawalTab.textContent = this.get('buttons.withdrawal_tab');

        // Табы валют
        const ethTabName = document.querySelector('[data-currency="eth"] .tab-name');
        if (ethTabName) ethTabName.textContent = this.get('currencies.ethereum');

        const btcTabName = document.querySelector('[data-currency="btc"] .tab-name');
        if (btcTabName) btcTabName.textContent = this.get('currencies.bitcoin');

        const usdtTabName = document.querySelector('[data-currency="usdttrc20"] .tab-name');
        if (usdtTabName) usdtTabName.textContent = this.get('currencies.usdt');

        const trxTabName = document.querySelector('[data-currency="trx"] .tab-name');
        if (trxTabName) trxTabName.textContent = this.get('currencies.tron');

        // Информация о валюте и требования
        const requirementLabels = document.querySelectorAll('.requirement-label');
        if (requirementLabels.length >= 5) {
            // Основные лейблы требований
            const minimumLabel = document.getElementById('minimum-label');
            if (minimumLabel) minimumLabel.textContent = this.get('earnings.minimum') + ':';

            const withdrawalAmountLabel = document.getElementById('withdrawal-amount-label');
            if (withdrawalAmountLabel) withdrawalAmountLabel.textContent = this.get('earnings.withdrawal_amount_calc') + ':';

            const networkFeeLabel = document.getElementById('network-fee-label');
            if (networkFeeLabel) networkFeeLabel.textContent = this.get('earnings.network_fee') + ':';

            // Лейблы в секции предупреждений
            const minimumAmountLabel = document.getElementById('minimum-amount-label');
            if (minimumAmountLabel) minimumAmountLabel.textContent = this.get('labels.minimum_amount_label');
        }

        // Описания разделов
        const balanceDescription = document.querySelector('#earn-section .earn-block p');
        if (balanceDescription && balanceDescription.textContent.includes('Текущий баланс')) {
            balanceDescription.textContent = this.get('labels.balance_description');
        }

        const calculatorDescription = document.querySelector('#calculator-section p');
        if (calculatorDescription && calculatorDescription.textContent.includes('Рассчитайте сумму')) {
            calculatorDescription.textContent = this.get('labels.calculator_description');
        }

        const withdrawalDescription = document.querySelector('#withdrawal-section p[data-section="withdrawal_desc"]');
        if (withdrawalDescription) {
            withdrawalDescription.textContent = this.get('labels.withdrawal_form_description');
        }

        // Результаты расчетов
        const calcLabels = document.querySelectorAll('.calc-label');
        if (calcLabels.length >= 3) {
            calcLabels[0].textContent = this.get('earnings.withdrawal_amount') + ':';
            calcLabels[1].textContent = this.get('earnings.network_fee') + ':';
            calcLabels[2].textContent = this.get('earnings.you_will_receive') + ':';
        }

        const efficiencyLabel = document.querySelector('.efficiency-label');
        if (efficiencyLabel) efficiencyLabel.textContent = this.get('earnings.efficiency') + ':';

        // === СЕКЦИЯ ДРУЗЕЙ ===
        // Лейблы статистики
        const statLabels = document.querySelectorAll('.stat-label');
        if (statLabels.length >= 2) {
            statLabels[0].textContent = this.get('friends.total_referrals') + ':';
            statLabels[1].textContent = this.get('friends.earned_from_referrals') + ':';
        }



        const withdrawalHint = document.querySelector('#earn-section .earn-block:nth-child(3) .hint');
        if (withdrawalHint) withdrawalHint.textContent = this.get('earnings.withdrawal_hint');

        const withdrawalAmountLabel = document.querySelector('label[for="withdrawal-amount"]');
        if (withdrawalAmountLabel) {
            const helpIcon = withdrawalAmountLabel.querySelector('.help-tooltip');
            withdrawalAmountLabel.childNodes[0].textContent = this.get('earnings.withdrawal_amount') + ':';
        }

        const tooltipText = document.querySelector('.tooltip-text');
        if (tooltipText) tooltipText.textContent = this.get('earnings.withdrawal_amount_help');

        const withdrawalAmountInput = document.getElementById('withdrawal-amount');
        if (withdrawalAmountInput) withdrawalAmountInput.placeholder = this.get('placeholders.enter_amount');

        const cryptoCurrencyLabel = document.querySelector('label[for="crypto-currency"]');
        if (cryptoCurrencyLabel) cryptoCurrencyLabel.textContent = this.get('labels.selected_crypto');

        const cryptoAmountLabel = document.querySelector('label[for="crypto-amount"]');
        if (cryptoAmountLabel) cryptoAmountLabel.textContent = this.get('labels.amount_to_receive');

        const cryptoAmountInput = document.getElementById('crypto-amount');
        if (cryptoAmountInput) cryptoAmountInput.placeholder = this.get('placeholders.auto_calculated');

        const withdrawalAddressLabel = document.querySelector('label[for="withdrawal-address"]');
        if (withdrawalAddressLabel) withdrawalAddressLabel.textContent = this.get('labels.wallet_address');

        const withdrawalAddressInput = document.getElementById('withdrawal-address');
        if (withdrawalAddressInput) withdrawalAddressInput.placeholder = this.get('placeholders.enter_wallet_address');

        const withdrawBtn = document.getElementById('request-withdrawal-button');
        if (withdrawBtn) withdrawBtn.textContent = this.get('buttons.request_withdrawal');

        // Переводы для пустых состояний истории
        const historyEmptyTitle = document.querySelector('.empty-history-title');
        if (historyEmptyTitle) historyEmptyTitle.textContent = this.get('empty_states.history_empty_title');

        const historyEmptyDescription = document.querySelector('.empty-history-description');
        if (historyEmptyDescription) historyEmptyDescription.textContent = this.get('empty_states.history_empty_description');

        const historyEmptyHint = document.querySelector('.empty-history-hint span:last-child');
        if (historyEmptyHint) historyEmptyHint.textContent = this.get('empty_states.history_empty_hint');

        const importantHint = document.querySelector('#earn-section .earn-block:nth-child(3) .hint:last-child');
        if (importantHint) importantHint.innerHTML = `<strong>${this.get('common.important')}:</strong> ${this.get('earnings.wallet_warning')}`;



        // === СЕКЦИЯ ДРУЗЕЙ ===
        // 🛡️ СОХРАНЯЕМ ИКОНКИ! Используем специальную функцию для заголовков с иконками
        this.updateElementByIdPreservingIcons('friends-title', 'friends.title');
        this.updateElementByIdPreservingIcons('share-title', 'friends.share_app');
        this.updateElementByIdPreservingIcons('invite-title', 'friends.invite_friend');
        this.updateElementByIdPreservingIcons('stats-title', 'friends.referral_stats');
        this.updateElementByIdPreservingIcons('subscriptions-title', 'friends.subscriptions');
        this.updateElementByIdPreservingIcons('referrer-title', 'friends.my_referrer_title');

        const shareText = document.querySelector('#friends-section .friends-block:first-child p');
        if (shareText) shareText.textContent = this.get('friends.share_description');

        const shareBtn = document.getElementById('share-app-button');
        if (shareBtn) {
            // 🛡️ СОХРАНЯЕМ ИКОНКИ! Сохраняем SVG иконки и обновляем только текст
            const svgs = Array.from(shareBtn.querySelectorAll('svg'));
            const translation = this.get('friends.share_button');
            shareBtn.innerHTML = '';
            svgs.forEach(svg => shareBtn.appendChild(svg));
            const textNode = document.createTextNode(translation);
            shareBtn.appendChild(textNode);
        }



        const referralDescription = document.querySelector('#friends-section .friends-block:nth-child(2) p[data-section="invite_friend_desc"]');
        if (referralDescription) referralDescription.textContent = this.get('friends.referral_description');

        // Переводы для реферальной ссылки
        const referralLinkInput = document.getElementById('referral-link-input');
        if (referralLinkInput && referralLinkInput.value === 'Генерация ссылки...') {
            referralLinkInput.value = this.get('status.generating_link');
        }

        const copyReferralButton = document.getElementById('copy-referral-button');
        if (copyReferralButton) copyReferralButton.title = this.get('tooltips.copy_referral');

        // Переводы для статистики рефералов
        const statsDescription = document.querySelector('p[data-section="referral_stats_desc"]');
        if (statsDescription) statsDescription.textContent = this.get('friends.referral_description');

        // Переводы для информации о реферере
        const referrerDescription = document.querySelector('p[data-section="referrer_desc"]');
        if (referrerDescription) referrerDescription.textContent = this.get('friends.referrer_description');

        const referrerInfo = document.getElementById('referrer-info');
        if (referrerInfo) {
            const loadingText = referrerInfo.querySelector('div');
            if (loadingText && loadingText.textContent.includes('Загрузка информации о реферере')) {
                loadingText.innerHTML = `
                    <svg style="width: 24px; height: 24px; margin-right: 8px; color: #FFA500; vertical-align: middle;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                        <circle cx="8.5" cy="7" r="4"/>
                        <path d="M20 8v6"/>
                        <path d="M23 11l-3-3-3 3"/>
                    </svg>
                    ${this.get('status.loading_referrer_info')}<br>
                    <small style="opacity: 0.7;">${this.get('status.checking_who_invited')}</small>
                `;
            }
        }



        const totalReferralsLabel = document.querySelector('.stat-item:first-child .stat-label');
        if (totalReferralsLabel) totalReferralsLabel.textContent = this.get('friends.total_referrals') + ':';

        const referralEarningsLabel = document.querySelector('.stat-item:nth-child(2) .stat-label');
        if (referralEarningsLabel) referralEarningsLabel.textContent = this.get('friends.earned_from_referrals') + ':';

        const refreshStatsBtn = document.getElementById('refresh-stats-button');
        if (refreshStatsBtn) {
            // 🛡️ СОХРАНЯЕМ ИКОНКИ! Сохраняем SVG иконки и обновляем только текст
            const svgs = Array.from(refreshStatsBtn.querySelectorAll('svg'));
            const translation = this.get('friends.refresh_stats');
            refreshStatsBtn.innerHTML = '';
            svgs.forEach(svg => refreshStatsBtn.appendChild(svg));
            const textNode = document.createTextNode(translation);
            refreshStatsBtn.appendChild(textNode);
        }



        console.log(`[Localization] Переводы применены для языка: ${this.currentLanguage}`);
    }

    /**
     * Обновляет плейсхолдер адреса кошелька в зависимости от выбранной валюты
     */
    updateAddressPlaceholder(currency) {
        const addressInput = document.getElementById('withdrawal-address');
        if (!addressInput) return;

        const placeholders = {
            'ton': this.get('placeholders.ton_address'),
            'eth': this.get('placeholders.eth_address'),
            'btc': this.get('placeholders.btc_address'),
            'usdttrc20': this.get('placeholders.usdt_trc20_address'),
            'trx': this.get('placeholders.tron_address')
        };

        const placeholder = placeholders[currency] || this.get('placeholders.enter_wallet_address');
        addressInput.placeholder = placeholder;

        console.log(`[Localization] Обновлен плейсхолдер адреса для валюты: ${currency}`);
    }

    /**
     * Получает текущий язык
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * Устанавливает язык
     */
    setLanguage(language) {
        console.log(`[Localization] Попытка установить язык: ${language}`);

        if (this.translations[language] || language === 'en' || language === 'ru') {
            this.currentLanguage = language;
            console.log(`[Localization] Язык установлен: ${language}`);

            // Применяем переводы только если они загружены
            if (this.isLoaded) {
                this.applyTranslations();
            } else {
                console.log(`[Localization] Переводы еще не загружены, применение отложено`);
            }
        } else {
            console.warn(`[Localization] Неподдерживаемый язык: ${language}`);
        }
    }

    /**
     * Обновляет плейсхолдер адреса кошелька в зависимости от выбранной валюты
     */
    updateAddressPlaceholder(currency) {
        const addressInput = document.getElementById('withdrawal-address');
        if (!addressInput) return;

        const placeholderKey = `placeholders.${currency}_address`;
        let placeholder = this.get(placeholderKey);

        // Если специфичный плейсхолдер не найден, используем общий
        if (placeholder === placeholderKey) {
            placeholder = this.get('placeholders.enter_wallet_address');
        }

        addressInput.placeholder = placeholder;

        console.log(`[Localization] Обновлен плейсхолдер адреса для валюты: ${currency}`);
    }

    /**
     * Применяет переводы к элементам с data-translate атрибутами
     */
    applyDataTranslateElements() {
        const elements = document.querySelectorAll('[data-translate]');

        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            if (key) {
                const translation = this.get(key);
                if (translation !== key) {
                    element.textContent = translation;
                }
            }
        });

        console.log(`[Localization] Обновлено ${elements.length} элементов с data-translate`);
    }
}

// Создаем глобальный экземпляр системы локализации
const appLocalization = new AppLocalization();

// Экспортируем в глобальную область видимости для совместимости
window.AppLocalization = AppLocalization;
window.appLocalization = appLocalization;

// Функции для обратной совместимости
window.updateAddressPlaceholder = (currency) => {
    if (appLocalization) {
        appLocalization.updateAddressPlaceholder(currency);
    }
};

console.log('🌐 [Localization] Система локализации с IP-геолокацией загружена');
