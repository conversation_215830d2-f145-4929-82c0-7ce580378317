// === balance-manager.js ===
// Файл: js/balance-manager.js
// Описание: Управляет состоянием и отображением баланса пользователя.

class BalanceManager {
  constructor() {
    this._currentUserBalance = 0;
    this.elements = {
      balanceAmount: document.getElementById("balance-amount"),
      earnBalanceAmount: document.getElementById("earn-balance-amount"),
      availableWithdrawal: document.getElementById("available-withdrawal"),
      calcBalance: document.getElementById("calc-balance"),
    };

    // Защищенная переменная баланса с валидацией (из оригинала)
    Object.defineProperty(window, 'currentUserBalance', {
      get: () => this._currentUserBalance,
      set: (value) => {
        // Предупреждение о попытке изменения баланса (из оригинала)
        if (typeof value === 'number' && value !== this._currentUserBalance) {
          console.warn('🚨 БЕЗОПАСНОСТЬ: Попытка изменения баланса через консоль заблокирована!');
          console.warn('💡 Баланс можно изменить только через серверные операции.');
          return;
        }
        this._currentUserBalance = value;
      },
      configurable: false,
      enumerable: true
    });
  }

  init(initialBalance = 0) {
    console.log('[BalanceManager] Инициализация.');
    this.updateBalance(initialBalance, 'init');
  }

  updateBalance(newBalance, source = 'unknown') {
    // Безопасное обновление баланса (только для серверных операций)
    const balance = parseInt(newBalance, 10) || 0;
    if (this._currentUserBalance === balance) return;

    console.log(`[BalanceManager] Обновление баланса из "${source}": ${this._currentUserBalance} -> ${balance}`);
    this._currentUserBalance = balance;

    // Обновляем все элементы баланса
    const formattedBalance = balance.toLocaleString();

    if (this.elements.balanceAmount) {
      this.elements.balanceAmount.textContent = formattedBalance;
    }

    if (this.elements.earnBalanceAmount) {
      this.elements.earnBalanceAmount.textContent = formattedBalance;
    }

    if (this.elements.availableWithdrawal) {
      this.elements.availableWithdrawal.textContent = formattedBalance;
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      console.log(`💰 Элемент "Доступно для вывода" обновлен: ${formattedBalance} ${coinsText}`);
    } else {
      console.warn('⚠️ Элемент "available-withdrawal" не найден!');
    }

    // Обновляем баланс в калькуляторе
    if (this.elements.calcBalance) {
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      this.elements.calcBalance.innerHTML = `${formattedBalance} <span data-translate="currency.coins">${coinsText}</span>`;
    }

    // Обновляем расчеты в калькуляторе, если есть введенная сумма
    const calcAmountInput = document.getElementById('calc-amount');
    if (calcAmountInput && calcAmountInput.value) {
      const amount = parseInt(calcAmountInput.value) || 0;
      if (window.calculatorManager && window.calculatorManager.updateDisplay) {
        window.calculatorManager.updateDisplay(amount);
      }
      if (window.calculatorManager && window.calculatorManager.updateBalanceCheck) {
        window.calculatorManager.updateBalanceCheck(amount);
      }
    }

    // Повторно валидируем основную форму вывода, так как изменение баланса может повлиять на ее валидность
    if (window.withdrawalFormManager && window.withdrawalFormManager.validateForm) {
      window.withdrawalFormManager.validateForm();
    }

    // Отправляем событие для других модулей
    window.dispatchEvent(new CustomEvent('balanceChanged', {
      detail: { newBalance: balance, source: source }
    }));

    console.log(`🎯 Баланс обновлен: ${formattedBalance}`);
  }

  getCurrentBalance() {
    return this._currentUserBalance;
  }

  getBalance() {
    return this._currentUserBalance;
  }

  addCoins(amount, source = 'unknown') {
    const newBalance = this._currentUserBalance + amount;
    this.updateBalance(newBalance, source);
  }

  // Проверка достаточности баланса для вывода
  canWithdraw(amount) {
    return this._currentUserBalance >= amount;
  }

  // Проверка минимального баланса для доступа к выводу
  canAccessWithdrawal() {
    const minBalance = window.appSettings ? window.appSettings.get('min_balance_for_withdrawal') : 100;
    return this._currentUserBalance >= minBalance;
  }

  // Форматирование баланса для отображения
  formatBalance(balance = null) {
    const amount = balance !== null ? balance : this._currentUserBalance;
    return amount.toLocaleString();
  }

  // Получение баланса в долларах
  getBalanceInUSD() {
    const coinValue = window.appSettings ? window.appSettings.getCoinValue() : 0.001;
    return this._currentUserBalance * coinValue;
  }

  // Обновление всех элементов баланса без изменения значения
  refreshDisplay() {
    this.updateBalance(this._currentUserBalance, 'refresh');
  }
}

window.balanceManager = new BalanceManager();
console.log('💰 [BalanceManager] Менеджер баланса загружен.');