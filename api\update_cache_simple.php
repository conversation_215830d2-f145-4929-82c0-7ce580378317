<?php
/**
 * Упрощенный cron-скрипт для обновления кеша валют
 * Использует fallback данные если API недоступен
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/currency_cache.php';

echo "🔄 Обновление кеша валют (упрощенная версия)\n";
echo str_repeat("-", 40) . "\n";

// Актуальные данные валют (можно обновлять вручную)
$currencyData = [
    'ton' => [
        'rate_usd' => 2.99,
        'min_amount' => 0.5,
        'network_fee' => 0.15,
        'status' => 'best'
    ],
    'eth' => [
        'rate_usd' => 2556.56,
        'min_amount' => 0.0001,
        'network_fee' => 0.25,
        'status' => 'best'
    ],
    'btc' => [
        'rate_usd' => 106100.00,
        'min_amount' => 0.000005,
        'network_fee' => 0.50,
        'status' => 'good'
    ],
    'usdttrc20' => [
        'rate_usd' => 1.00,
        'min_amount' => 8.58,
        'network_fee' => 5.58,
        'status' => 'expensive'
    ],
    'ltc' => [
        'rate_usd' => 85.88,
        'min_amount' => 0.001,
        'network_fee' => 0.30,
        'status' => 'good'
    ],
    'bch' => [
        'rate_usd' => 458.81,
        'min_amount' => 0.001,
        'network_fee' => 0.35,
        'status' => 'good'
    ],
    'xrp' => [
        'rate_usd' => 0.65,
        'min_amount' => 1.0,
        'network_fee' => 0.10,
        'status' => 'available'
    ],
    'ada' => [
        'rate_usd' => 0.65,
        'min_amount' => 1.0,
        'network_fee' => 0.15,
        'status' => 'available'
    ],
    'dot' => [
        'rate_usd' => 10.00,
        'min_amount' => 0.1,
        'network_fee' => 0.20,
        'status' => 'available'
    ]
];

$updatedCount = 0;
$errorCount = 0;

foreach ($currencyData as $currency => $data) {
    echo "💰 Обновляем " . strtoupper($currency) . "... ";
    
    try {
        $success = updateCurrencyInCache(
            $currency,
            $data['rate_usd'],
            $data['min_amount'],
            $data['network_fee'],
            $data['status']
        );
        
        if ($success) {
            $updatedCount++;
            echo "✅\n";
        } else {
            throw new Exception("Ошибка сохранения в кеш");
        }
        
    } catch (Exception $e) {
        $errorCount++;
        echo "❌ " . $e->getMessage() . "\n";
    }
}

// Обновляем метку времени кеша
$cache = loadCurrencyCache();
$cache['last_updated'] = time();
$cache['update_interval'] = 900; // 15 минут
saveCurrencyCache($cache);

echo str_repeat("-", 40) . "\n";
echo "📊 Результат: Обновлено {$updatedCount}, ошибок {$errorCount}\n";

if ($updatedCount > 0) {
    echo "🎉 Кеш успешно обновлен!\n";
    
    // Показываем обновленные данные
    echo "\n📋 Обновленные минимумы:\n";
    foreach (['ton', 'eth', 'btc', 'usdttrc20'] as $currency) {
        if (isset($currencyData[$currency])) {
            $data = $currencyData[$currency];
            $rate = $data['rate_usd'];
            $minAmount = $data['min_amount'];
            $networkFee = $data['network_fee'];
            
            // Рассчитываем минимум в монетах с учетом комиссии
            $minUsd = $minAmount * $rate;
            $minCoins = ceil($minUsd / CONVERSION_RATE);
            $feeInCoins = ceil(($networkFee / $rate) / CONVERSION_RATE);
            $totalMinCoins = $minCoins + $feeInCoins;
            
            echo "   💎 " . strtoupper($currency) . ": {$totalMinCoins} монет\n";
        }
    }
} else {
    echo "⚠️ Не удалось обновить кеш\n";
}

echo "\n✅ Обновление завершено!\n";
?>
