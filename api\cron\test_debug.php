<?php
echo "=== ТЕСТ ОТЛАДКИ ===\n";

echo "1. Проверка подключения config.php...\n";
try {
    require_once __DIR__ . '/../config.php';
    echo "✅ config.php подключен\n";
    echo "USER_DATA_FILE: " . (defined('USER_DATA_FILE') ? USER_DATA_FILE : 'НЕ ОПРЕДЕЛЕН') . "\n";
} catch (Exception $e) {
    echo "❌ Ошибка подключения config.php: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n2. Проверка подключения functions.php...\n";
try {
    require_once __DIR__ . '/../functions.php';
    echo "✅ functions.php подключен\n";
} catch (Exception $e) {
    echo "❌ Ошибка подключения functions.php: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n3. Проверка файла данных пользователей...\n";
if (defined('USER_DATA_FILE')) {
    echo "Путь к файлу: " . USER_DATA_FILE . "\n";
    echo "Файл существует: " . (file_exists(USER_DATA_FILE) ? 'ДА' : 'НЕТ') . "\n";
    if (file_exists(USER_DATA_FILE)) {
        echo "Размер файла: " . filesize(USER_DATA_FILE) . " байт\n";
        echo "Права доступа: " . substr(sprintf('%o', fileperms(USER_DATA_FILE)), -4) . "\n";
    }
} else {
    echo "❌ USER_DATA_FILE не определен\n";
}

echo "\n4. Проверка функции loadUserData...\n";
try {
    if (function_exists('loadUserData')) {
        echo "✅ Функция loadUserData существует\n";
        $userData = loadUserData();
        if ($userData === false) {
            echo "❌ loadUserData вернула false\n";
        } elseif (is_array($userData)) {
            echo "✅ loadUserData вернула массив с " . count($userData) . " пользователями\n";
        } else {
            echo "❌ loadUserData вернула неожиданный тип: " . gettype($userData) . "\n";
        }
    } else {
        echo "❌ Функция loadUserData не найдена\n";
    }
} catch (Exception $e) {
    echo "❌ Ошибка при вызове loadUserData: " . $e->getMessage() . "\n";
}

echo "\n5. Проверка токена бота...\n";
echo "TELEGRAM_BOT_TOKEN определен: " . (defined('TELEGRAM_BOT_TOKEN') ? 'ДА' : 'НЕТ') . "\n";
if (defined('TELEGRAM_BOT_TOKEN')) {
    echo "Длина токена: " . strlen(TELEGRAM_BOT_TOKEN) . " символов\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
