<?php
/**
 * API endpoint для мониторинга лимитов запросов
 * Публичный доступ для мониторинга
 */

// Отключаем вывод ошибок для чистого JSON
ini_set('display_errors', 0);
error_reporting(0);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'RateLimiter.php';

try {
    $rateLimiter = new RateLimiter();
    $stats = $rateLimiter->getStats();
    
    // Добавляем дополнительную информацию
    $stats['status'] = $stats['can_make_request'] ? 'OK' : 'RATE_LIMITED';
    $stats['timestamp'] = time();
    $stats['datetime'] = date('Y-m-d H:i:s');
    
    // Рассчитываем процент использования
    $stats['usage_percent_minute'] = round(($stats['requests_last_minute'] / $stats['max_per_minute']) * 100, 1);
    $stats['usage_percent_hour'] = round(($stats['requests_last_hour'] / $stats['max_per_hour']) * 100, 1);
    
    // Определяем уровень предупреждения
    if ($stats['usage_percent_minute'] >= 90) {
        $stats['warning_level'] = 'CRITICAL';
        $stats['warning_message'] = 'Критический уровень использования лимитов';
    } elseif ($stats['usage_percent_minute'] >= 70) {
        $stats['warning_level'] = 'HIGH';
        $stats['warning_message'] = 'Высокий уровень использования лимитов';
    } elseif ($stats['usage_percent_minute'] >= 50) {
        $stats['warning_level'] = 'MEDIUM';
        $stats['warning_message'] = 'Средний уровень использования лимитов';
    } else {
        $stats['warning_level'] = 'LOW';
        $stats['warning_message'] = 'Нормальный уровень использования лимитов';
    }
    
    echo json_encode([
        'success' => true,
        'data' => $stats
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
