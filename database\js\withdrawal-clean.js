/**
 * НОВЫЙ ЧИСТЫЙ МЕНЕДЖЕР ФОРМЫ ВЫПЛАТ - Без проверок минималок и лишнего кода
 */
class CleanWithdrawalForm {
  constructor() {
    this.elements = {};
    this.init();
  }

  init() {
    this.bindElements();
    this.bindEvents();
    console.log('💰 [CleanWithdrawalForm] Чистая форма выплат загружена');
  }

  bindElements() {
    this.elements = {
      amountInput: document.getElementById('withdrawal-amount'),
      currencySelect: document.getElementById('crypto-currency'),
      addressInput: document.getElementById('withdrawal-address'),
      cryptoAmountField: document.getElementById('crypto-amount'),
      submitButton: document.getElementById('request-withdrawal-btn'),
      validationCard: document.querySelector('.validation-card')
    };
  }

  bindEvents() {
    // Автообновление при вводе суммы
    if (this.elements.amountInput) {
      this.elements.amountInput.addEventListener('input', () => {
        this.updateCryptoAmount();
        this.validateForm();
      });
    }

    // Автообновление при смене валюты
    if (this.elements.currencySelect) {
      this.elements.currencySelect.addEventListener('change', () => {
        this.updateCryptoAmount();
        this.updateAddressPlaceholder();
        this.validateForm();
      });
    }

    // Валидация адреса
    if (this.elements.addressInput) {
      this.elements.addressInput.addEventListener('input', () => {
        this.validateForm();
      });
    }

    // Обработка отправки
    if (this.elements.submitButton) {
      this.elements.submitButton.addEventListener('click', () => {
        this.handleSubmit();
      });
    }
  }

  /**
   * КЛЮЧЕВОЙ МЕТОД: Обновляет криптосумму без проверок минималок
   */
  async updateCryptoAmount() {
    const amount = parseFloat(this.elements.amountInput?.value) || 0;
    const currency = this.elements.currencySelect?.value || 'ton';
    
    if (amount <= 0) {
      this.setCryptoField('', '#666', '');
      return;
    }

    // Проверяем только баланс пользователя
    const userBalance = window.balanceManager?.getBalance() || 0;
    if (amount > userBalance) {
      const needed = amount - userBalance;
      const coinsText = window.appLocalization ?
        window.appLocalization.get('currency.coins') :
        'монет';
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds') :
        'Недостаточно средств';
      const needText = window.appLocalization ?
        window.appLocalization.get('earnings.need') :
        'Нужно';
      this.setCryptoField(`❌ ${insufficientText}! ${needText}: ${needed} ${coinsText}`, '#ff6b6b', insufficientText);
      return;
    }

    try {
      // Получаем криптосумму
      const cryptoAmount = await this.getCryptoAmount(amount, currency);
      
      if (cryptoAmount > 0) {
        const prefix = this.getCurrencyPrefix(currency);
        // ФОРМАТ: CRYPTO_AMOUNT PREFIX
        this.setCryptoField(`${cryptoAmount.toFixed(8)} ${prefix}`, '#00ff88', `Вы получите: ${cryptoAmount.toFixed(8)} ${prefix}`);
      } else {
        const errorText = window.appLocalization ?
          window.appLocalization.get('earnings.calculation_error') :
          'Ошибка расчёта';
        this.setCryptoField(`❌ ${errorText}`, '#ff6b6b', errorText);
      }
    } catch (error) {
      console.error('[CleanWithdrawalForm] Ошибка:', error);
      const errorText = window.appLocalization ?
        window.appLocalization.get('earnings.calculation_error') :
        'Ошибка расчёта';
      this.setCryptoField(`❌ ${errorText}`, '#ff6b6b', errorText);
    }
  }

  /**
   * Получает криптосумму через API
   */
  async getCryptoAmount(coinAmount, currency) {
    try {
      const response = await fetch(`${window.API_BASE_URL}/calculateWithdrawalAmount.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          coins_amount: coinAmount,
          currency: currency
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.crypto_amount) {
          return parseFloat(data.crypto_amount);
        }
      }
    } catch (error) {
      console.warn('[CleanWithdrawalForm] API недоступен, используем fallback');
    }
    
    // Fallback расчёт
    return this.calculateFallbackCrypto(coinAmount, currency);
  }

  /**
   * Fallback расчёт с вычетом сетевых комиссий
   * Показывает пользователю сумму ПОСЛЕ вычета комиссии (то что он реально получит)
   */
  calculateFallbackCrypto(coinAmount, currency) {
    const dollarAmount = coinAmount * 0.001;

    // Обновленные курсы (синхронизированы с calculator-original.js)
    const rates = {
      eth: 2502,      // ETH ~$2502
      btc: 104620,    // BTC ~$104620
      ton: 2.9,       // TON ~$2.9
      usdttrc20: 1.0, // USDT ~$1.0
      trx: 0.25       // TRX ~$0.25
    };

    // Сетевые комиссии (синхронизированы с FeeCalculator.php)
    const fees = {
      eth: 0.002,      // 0.002 ETH
      btc: 0.0001,     // 0.0001 BTC
      ton: 0.05,       // 0.05 TON
      usdttrc20: 1.5,  // 1.5 USDT
      trx: 1.0         // 1.0 TRX
    };

    const rate = rates[currency] || 1;
    const fee = fees[currency] || 0;

    const grossAmount = dollarAmount / rate;
    const netAmount = grossAmount - fee; // Вычитаем комиссию - показываем что получит пользователь

    console.log(`[WithdrawalClean] Fallback для ${currency}: ${coinAmount} монет = ${netAmount.toFixed(8)} (после комиссии ${fee})`);

    return Math.max(0, netAmount);
  }

  /**
   * Устанавливает значение в поле криптосуммы
   */
  setCryptoField(value, color, title) {
    if (this.elements.cryptoAmountField) {
      this.elements.cryptoAmountField.value = value;
      this.elements.cryptoAmountField.style.color = color;
      this.elements.cryptoAmountField.title = title;

      // ВАЖНО: Перевалидируем форму после изменения поля авторасчёта
      setTimeout(() => this.validateForm(), 100);
    }
  }

  /**
   * Обновляет placeholder для адреса
   */
  updateAddressPlaceholder() {
    if (!this.elements.addressInput || !this.elements.currencySelect) return;
    
    const currency = this.elements.currencySelect.value;
    const defaultPlaceholder = window.appLocalization ?
      window.appLocalization.get('placeholders.enter_wallet_address') :
      'Введите адрес';

    const placeholders = {
      eth: defaultPlaceholder,
      btc: defaultPlaceholder,
      ton: defaultPlaceholder,
      usdttrc20: defaultPlaceholder,
      trx: defaultPlaceholder
    };
    
    this.elements.addressInput.placeholder = placeholders[currency] || defaultPlaceholder;
  }

  /**
   * Проверяет состояние поля авторасчёта
   */
  isCryptoAmountValid() {
    if (!this.elements.cryptoAmountField) return false;

    const value = this.elements.cryptoAmountField.value.trim();
    const color = this.elements.cryptoAmountField.style.color;

    // Поле пустое
    if (!value) return false;

    // Поле содержит ошибку (красный цвет или символы ошибки)
    if (color === '#ff6b6b' || color === 'var(--cyber-error-color, #ff6b6b)' ||
        color === 'rgb(255, 107, 107)' || value.includes('❌') ||
        value.includes('⚠️') || value.includes('💸')) {
      return false;
    }

    // Поле содержит предупреждение (оранжевый цвет)
    if (color === '#ffa726' || color === 'var(--cyber-warning-color, #ffa726)' ||
        color === 'rgb(255, 167, 38)') {
      return false;
    }

    // Поле содержит корректную криптосумму (зеленый цвет)
    return color === '#00ff88' || color === 'var(--cyber-success-color, #00ff88)' ||
           color === 'rgb(0, 255, 136)';
  }

  /**
   * Валидация формы
   */
  validateForm() {
    const amount = parseFloat(this.elements.amountInput?.value) || 0;
    const address = this.elements.addressInput?.value?.trim() || '';
    const userBalance = window.balanceManager?.getBalance() || 0;
    const cryptoAmountValid = this.isCryptoAmountValid();

    let isValid = true;
    let message = '';

    if (amount <= 0) {
      message = window.appLocalization ?
        window.appLocalization.get('earnings.enter_amount_for_withdrawal') :
        'Введите сумму для вывода';
      isValid = false;
    } else if (amount > userBalance) {
      message = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds') :
        'Недостаточно средств';
      isValid = false;
    } else if (!cryptoAmountValid) {
      // НОВАЯ ПРОВЕРКА: Блокируем если поле авторасчёта содержит ошибки
      const cryptoValue = this.elements.cryptoAmountField?.value || '';
      if (cryptoValue.includes('❌')) {
        message = window.appLocalization ?
          window.appLocalization.get('earnings.calculation_error') :
          'Ошибка расчёта';
      } else if (cryptoValue.includes('⚠️')) {
        message = window.appLocalization ?
          window.appLocalization.get('earnings.insufficient_for_withdrawal') :
          'Недостаточно для вывода';
      } else if (cryptoValue.includes('💸')) {
        message = window.appLocalization ?
          window.appLocalization.get('earnings.fee_exceeds_amount') :
          'Комиссия превышает сумму';
      } else {
        message = window.appLocalization ?
          window.appLocalization.get('earnings.wait_calculation') :
          'Ожидание расчёта';
      }
      isValid = false;
    } else if (!address) {
      message = window.appLocalization ?
        window.appLocalization.get('earnings.enter_wallet_address') :
        'Введите адрес кошелька';
      isValid = false;
    } else {
      message = window.appLocalization ?
        window.appLocalization.get('earnings.form_filled_correctly') :
        'Форма заполнена корректно';
    }

    this.updateValidationCard(message, isValid);

    if (this.elements.submitButton) {
      this.elements.submitButton.disabled = !isValid;
    }
  }

  /**
   * Обновляет карточку валидации
   */
  updateValidationCard(message, isValid) {
    if (this.elements.validationCard) {
      this.elements.validationCard.textContent = message;
      this.elements.validationCard.className = `validation-card ${isValid ? 'valid' : 'invalid'}`;
      this.elements.validationCard.style.color = isValid ? '#00ff88' : '#ff6b6b';
    }
  }

  /**
   * Обработка отправки формы
   */
  async handleSubmit() {
    const amount = parseFloat(this.elements.amountInput?.value) || 0;
    const address = this.elements.addressInput?.value?.trim() || '';
    const currency = this.elements.currencySelect?.value || 'ton';

    try {
      // Получаем криптосумму для отправки в NOWPayments
      const cryptoAmountNet = await this.getCryptoAmount(amount, currency);
      const cryptoAmountGross = await this.getGrossCryptoAmount(amount, currency);
      
      console.log(`[CleanWithdrawalForm] Отправляем в NOWPayments: ${cryptoAmountGross} (до комиссии), пользователь получит: ${cryptoAmountNet} (после комиссии)`);

      const response = await fetch(`${window.API_BASE_URL}/requestWithdrawal.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          initData: window.Telegram?.WebApp?.initData || '',
          amount: amount,
          address: address,
          currency: currency,
          crypto_amount: cryptoAmountGross // Отправляем сумму ДО вычета комиссии
        })
      });

      const result = await response.json();
      
      if (result.success) {
        const successText = window.appLocalization ?
          window.appLocalization.get('earnings.withdrawal_request_sent') :
          '✅ Запрос на вывод отправлен успешно!';
        alert(successText);
        this.clearForm();
      } else {
        const errorText = window.appLocalization ?
          window.appLocalization.get('earnings.withdrawal_error') :
          'Ошибка';
        const unknownErrorText = window.appLocalization ?
          window.appLocalization.get('earnings.unknown_error') :
          'Неизвестная ошибка';
        alert(`❌ ${errorText}: ${result.error || unknownErrorText}`);
      }
    } catch (error) {
      console.error('[CleanWithdrawalForm] Ошибка отправки:', error);
      const sendErrorText = window.appLocalization ?
        window.appLocalization.get('earnings.request_send_error') :
        '❌ Ошибка отправки запроса';
      alert(sendErrorText);
    }
  }

  /**
   * Получает сумму ДО вычета комиссии для NOWPayments
   */
  async getGrossCryptoAmount(coinAmount, currency) {
    const dollarAmount = coinAmount * 0.001;
    const rates = { eth: 2670, btc: 97000, ton: 6.7, usdttrc20: 1, trx: 0.25 };
    const rate = rates[currency] || 1;
    
    return dollarAmount / rate; // Сумма ДО вычета комиссии
  }

  /**
   * Получает префикс валюты
   */
  getCurrencyPrefix(currency) {
    const prefixes = { eth: 'ETH', btc: 'BTC', ton: 'TON', usdttrc20: 'USDT', trx: 'TRX' };
    return prefixes[currency] || currency.toUpperCase();
  }

  /**
   * Очищает форму
   */
  clearForm() {
    if (this.elements.amountInput) this.elements.amountInput.value = '';
    if (this.elements.addressInput) this.elements.addressInput.value = '';
    if (this.elements.cryptoAmountField) {
      this.elements.cryptoAmountField.value = '';
      this.elements.cryptoAmountField.style.color = '';
      this.elements.cryptoAmountField.title = '';
    }
    this.validateForm();
  }
}

// Инициализация
window.cleanWithdrawalForm = new CleanWithdrawalForm();

console.log('🚀 [CleanWithdrawalForm] Новая чистая форма выплат готова!');
