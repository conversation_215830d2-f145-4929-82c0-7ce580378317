<?php
// Прямая запись в лог без функций
$logFile = __DIR__ . '/notifications.log';
$timestamp = date('Y-m-d H:i:s');
file_put_contents($logFile, "[$timestamp] ТЕСТ ПРЯМОЙ ЗАПИСИ В ЛОГ\n", FILE_APPEND | LOCK_EX);

echo "Тест записи в лог выполнен\n";

// Теперь попробуем подключить config.php
try {
    file_put_contents($logFile, "[$timestamp] Подключаем config.php\n", FILE_APPEND | LOCK_EX);
    require_once __DIR__ . '/../config.php';
    file_put_contents($logFile, "[$timestamp] config.php подключен успешно\n", FILE_APPEND | LOCK_EX);
} catch (Exception $e) {
    file_put_contents($logFile, "[$timestamp] ОШИБКА config.php: " . $e->getMessage() . "\n", FILE_APPEND | LOCK_EX);
    exit(1);
}

// Теперь попробуем подключить functions.php
try {
    file_put_contents($logFile, "[$timestamp] Подключаем functions.php\n", FILE_APPEND | LOCK_EX);
    require_once __DIR__ . '/../functions.php';
    file_put_contents($logFile, "[$timestamp] functions.php подключен успешно\n", FILE_APPEND | LOCK_EX);
} catch (Exception $e) {
    file_put_contents($logFile, "[$timestamp] ОШИБКА functions.php: " . $e->getMessage() . "\n", FILE_APPEND | LOCK_EX);
    exit(1);
}

// Теперь попробуем функцию logMessage
if (function_exists('logMessage')) {
    file_put_contents($logFile, "[$timestamp] Функция logMessage найдена\n", FILE_APPEND | LOCK_EX);
} else {
    file_put_contents($logFile, "[$timestamp] Функция logMessage НЕ найдена\n", FILE_APPEND | LOCK_EX);
}

file_put_contents($logFile, "[$timestamp] ТЕСТ ЗАВЕРШЕН УСПЕШНО\n", FILE_APPEND | LOCK_EX);
echo "Тест завершен\n";
?>
