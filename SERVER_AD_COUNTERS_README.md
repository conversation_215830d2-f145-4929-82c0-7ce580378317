# 🖥️ Серверная система счетчиков рекламы

## 🎯 **Решение проблемы**

Полностью переделана система лимитов показов рекламы с localStorage на **серверное хранение в JSON файле**. Теперь данные надежно сохраняются и синхронизируются между всеми обновлениями страницы.

## 🏗️ **Архитектура системы**

### 📁 **Файловая структура**
```
database/
├── ad_limits.json          # Основной файл с лимитами пользователей

api/
├── ad_limits_manager.php   # Серверный API для работы с лимитами

js/
├── server-ad-counters.js   # Клиентский модуль для работы с сервером
└── ads-manager-full.js     # Обновлен для использования серверной системы
```

### 🗄️ **Структура данных (ad_limits.json)**
```json
{
  "daily_limits": {
    "native_banner": 20,
    "rewarded_video": 20,
    "interstitial": 20
  },
  "user_counts": {
    "user_123456": {
      "native_banner": 5,
      "rewarded_video": 12,
      "interstitial": 3
    },
    "user_789012": {
      "native_banner": 20,
      "rewarded_video": 8,
      "interstitial": 15
    }
  },
  "last_reset_date": "2024-01-15",
  "version": "1.0"
}
```

## 🔧 **Компоненты системы**

### 1. **Серверный API (ad_limits_manager.php)**

**Основные функции:**
- `getUserAdCount($userId, $adType)` - получить текущий счетчик
- `incrementUserAdCount($userId, $adType)` - увеличить счетчик
- `getRemainingCount($userId, $adType)` - получить оставшиеся показы
- `isLimitReached($userId, $adType)` - проверить лимит
- `resetUserCounters($userId)` - сбросить счетчики пользователя
- `checkDailyReset()` - автоматический ежедневный сброс

**API endpoints:**
- `GET ?action=get_user_limits&user_id=123` - получить все лимиты пользователя
- `POST action=increment_counter&user_id=123&ad_type=native_banner` - увеличить счетчик
- `GET ?action=check_limit&user_id=123&ad_type=native_banner` - проверить лимит
- `POST action=reset_user&user_id=123` - сбросить счетчики пользователя
- `GET ?action=get_stats` - получить глобальную статистику

### 2. **Клиентский модуль (server-ad-counters.js)**

**Основные методы:**
- `init()` - инициализация с получением ID пользователя
- `incrementCounter(adType)` - увеличение счетчика через сервер
- `updateCounter(adType)` - обновление отображения счетчика
- `refreshLimits()` - обновление данных с сервера
- `resetCounters()` - сброс счетчиков пользователя

**Особенности:**
- Автоматическое получение Telegram User ID
- Кэширование данных для быстрого доступа
- Плавная анимация изменений
- Поддержка локализации
- Fallback на тестовый ID для разработки

### 3. **Интеграция с ads-manager-full.js**

Обновлена логика обработки наград:
```javascript
// Приоритет систем:
if (window.serverAdCountersManager && window.serverAdCountersManager.isInitialized) {
  // 1. Серверная система (приоритет)
  await window.serverAdCountersManager.incrementCounter(adType);
} else if (window.adCountersManager) {
  // 2. Локальная система (fallback)
  window.adCountersManager.incrementCounter(adType);
} else {
  // 3. Старая система (последний fallback)
  this.incrementAdCount(adType);
}
```

## 🚀 **Преимущества новой системы**

### ✅ **Надежность**
- **Серверное хранение** - данные не теряются при очистке браузера
- **Автоматический ежедневный сброс** - точно в 00:00 UTC
- **Блокировка файлов** - предотвращение конфликтов при записи
- **Валидация данных** - проверка корректности JSON

### ✅ **Производительность**
- **Кэширование на клиенте** - быстрый доступ к данным
- **Минимальные запросы** - обновление только при необходимости
- **Асинхронные операции** - не блокируют интерфейс

### ✅ **Масштабируемость**
- **Поддержка множества пользователей** - каждый со своими лимитами
- **Глобальная статистика** - аналитика по всем пользователям
- **Легкое добавление новых типов рекламы**

### ✅ **Удобство разработки**
- **Подробное логирование** - легкая отладка
- **Тестовые страницы** - быстрая проверка функций
- **API документация** - понятные endpoints

## 🧪 **Тестирование**

### **Автоматическое тестирование**
Создан файл `test_server_counters.html`:

1. **Откройте** `test_server_counters.html` в браузере
2. **Проверьте** инициализацию и получение User ID
3. **Нажмите** кнопки просмотра рекламы
4. **Убедитесь**, что счетчики уменьшаются
5. **Перезагрузите** страницу - счетчики должны сохраниться

### **Ручное тестирование в приложении**
1. **Откройте** основное приложение
2. **Просмотрите** рекламу - счетчики уменьшаются
3. **Обновите** страницу - счетчики сохраняются
4. **Проверьте** в разных браузерах - данные синхронизированы

### **Тестирование API**
```bash
# Получить лимиты пользователя
curl "http://your-domain/api/ad_limits_manager.php?action=get_user_limits&user_id=123"

# Увеличить счетчик
curl -X POST "http://your-domain/api/ad_limits_manager.php" \
  -d "action=increment_counter&user_id=123&ad_type=native_banner"

# Получить статистику
curl "http://your-domain/api/ad_limits_manager.php?action=get_stats"
```

## 🔧 **Настройка и развертывание**

### **1. Права доступа**
```bash
# Убедитесь, что папка database доступна для записи
chmod 755 database/
chmod 644 database/ad_limits.json
```

### **2. Проверка работы**
1. **Откройте** `test_server_counters.html`
2. **Проверьте** инициализацию в логе
3. **Убедитесь**, что файл `database/ad_limits.json` создается

### **3. Мониторинг**
- **Размер файла** - следите за ростом `ad_limits.json`
- **Логи ошибок** - проверяйте PHP error log
- **Производительность** - время ответа API

## 🎯 **Результат**

### **До (localStorage):**
- ❌ Данные терялись при очистке браузера
- ❌ Не синхронизировались между устройствами
- ❌ Проблемы с обновлением при перезагрузке
- ❌ Нет централизованной статистики

### **После (серверная система):**
- ✅ **Надежное хранение** данных на сервере
- ✅ **Синхронизация** между всеми устройствами
- ✅ **Мгновенное обновление** при перезагрузке
- ✅ **Централизованная статистика** и аналитика
- ✅ **Автоматический ежедневный сброс**
- ✅ **Поддержка множества пользователей**

## 🔍 **Отладка**

### **Проверка работы системы:**
```javascript
// В консоли браузера
console.log('User ID:', window.serverAdCountersManager.userId);
console.log('Initialized:', window.serverAdCountersManager.isInitialized);
console.log('Limits:', window.serverAdCountersManager.getAllLimitsInfo());

// Принудительное обновление
await window.serverAdCountersManager.refreshLimits();

// Сброс счетчиков для тестирования
await window.serverAdCountersManager.resetCounters();
```

### **Проверка серверной части:**
```php
// Прямая проверка API
$manager = new AdLimitsManager();
$info = $manager->getUserLimitsInfo('test_user_123');
var_dump($info);
```

## 🎉 **Заключение**

Серверная система счетчиков рекламы полностью решает все проблемы с localStorage:

- 🔒 **Надежность** - данные всегда сохраняются
- ⚡ **Скорость** - мгновенное обновление при загрузке
- 🌐 **Синхронизация** - работает на всех устройствах
- 📊 **Аналитика** - полная статистика по пользователям
- 🛠️ **Удобство** - легкая отладка и тестирование

**Система готова к продакшену!** 🚀
