<?php
/**
 * api/fraud-stats.php
 * API для получения расширенной статистики антифрод системы
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/db_mock.php';

try {
    // Получаем параметры запроса
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get_stats';
    $period = $_GET['period'] ?? $_POST['period'] ?? '24h';
    
    switch ($action) {
        case 'get_stats':
            echo json_encode(getFraudStats($period));
            break;
            
        case 'get_charts_data':
            echo json_encode(getChartsData($period));
            break;
            
        case 'get_real_time_stats':
            echo json_encode(getRealTimeStats());
            break;
            
        case 'get_top_threats':
            echo json_encode(getTopThreats($period));
            break;
            
        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log('fraud-stats ERROR: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Получает общую статистику фрода
 */
function getFraudStats($period = '24h') {
    try {
        $timeRange = getTimeRange($period);
        
        // Загружаем данные
        $userData = loadUserData();
        $fraudLog = loadFraudLog();
        $vpnLog = loadVPNLog();
        $blockedDevices = loadBlockedDevices();
        
        $stats = [
            'period' => $period,
            'time_range' => $timeRange,
            'total_users' => count($userData),
            'blocked_users' => 0,
            'fraud_attempts' => 0,
            'vpn_detections' => 0,
            'blocked_devices' => count($blockedDevices),
            'success_rate' => 0,
            'risk_distribution' => [
                'low' => 0,
                'medium' => 0,
                'high' => 0,
                'critical' => 0
            ],
            'block_reasons' => [],
            'hourly_stats' => []
        ];
        
        // Анализируем пользователей
        foreach ($userData as $userId => $user) {
            if ($user['blocked'] ?? false) {
                $stats['blocked_users']++;
                
                $blockReason = $user['block_reason'] ?? 'unknown';
                $stats['block_reasons'][$blockReason] = ($stats['block_reasons'][$blockReason] ?? 0) + 1;
            }
            
            // Анализируем риск-скор
            $riskScore = $user['risk_score'] ?? 0;
            if ($riskScore >= 80) {
                $stats['risk_distribution']['critical']++;
            } elseif ($riskScore >= 60) {
                $stats['risk_distribution']['high']++;
            } elseif ($riskScore >= 30) {
                $stats['risk_distribution']['medium']++;
            } else {
                $stats['risk_distribution']['low']++;
            }
        }
        
        // Анализируем лог фрода за период
        foreach ($fraudLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $timeRange['start'] && $entryTime <= $timeRange['end']) {
                $stats['fraud_attempts']++;
            }
        }
        
        // Анализируем VPN детекции за период
        foreach ($vpnLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $timeRange['start'] && $entryTime <= $timeRange['end']) {
                if ($entry['vpn_detected'] ?? false) {
                    $stats['vpn_detections']++;
                }
            }
        }
        
        // Рассчитываем успешность защиты
        $totalThreats = $stats['fraud_attempts'] + $stats['vpn_detections'];
        $blockedThreats = $stats['blocked_users'];
        $stats['success_rate'] = $totalThreats > 0 ? round(($blockedThreats / $totalThreats) * 100, 2) : 100;
        
        // Генерируем почасовую статистику
        $stats['hourly_stats'] = generateHourlyStats($fraudLog, $vpnLog, $timeRange);
        
        return [
            'success' => true,
            'stats' => $stats,
            'generated_at' => time()
        ];
        
    } catch (Exception $e) {
        error_log('getFraudStats ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Получает данные для графиков
 */
function getChartsData($period = '24h') {
    try {
        $timeRange = getTimeRange($period);
        $fraudLog = loadFraudLog();
        $vpnLog = loadVPNLog();
        
        // Данные для графика активности по времени
        $timelineData = [];
        $hours = [];
        
        // Создаем массив часов для периода
        for ($i = 0; $i < 24; $i++) {
            $hour = date('H:00', $timeRange['start'] + ($i * 3600));
            $hours[] = $hour;
            $timelineData[$hour] = [
                'fraud_attempts' => 0,
                'vpn_detections' => 0,
                'total_threats' => 0
            ];
        }
        
        // Заполняем данные из логов
        foreach ($fraudLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $timeRange['start'] && $entryTime <= $timeRange['end']) {
                $hour = date('H:00', $entryTime);
                if (isset($timelineData[$hour])) {
                    $timelineData[$hour]['fraud_attempts']++;
                    $timelineData[$hour]['total_threats']++;
                }
            }
        }
        
        foreach ($vpnLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $timeRange['start'] && $entryTime <= $timeRange['end']) {
                if ($entry['vpn_detected'] ?? false) {
                    $hour = date('H:00', $entryTime);
                    if (isset($timelineData[$hour])) {
                        $timelineData[$hour]['vpn_detections']++;
                        $timelineData[$hour]['total_threats']++;
                    }
                }
            }
        }
        
        // Данные для круговой диаграммы типов угроз
        $threatTypes = [
            'VPN/Proxy' => 0,
            'Device Fingerprint' => 0,
            'Suspicious Activity' => 0,
            'Self Referral' => 0,
            'Other' => 0
        ];
        
        $userData = loadUserData();
        foreach ($userData as $user) {
            if ($user['blocked'] ?? false) {
                $reason = $user['block_reason'] ?? 'Other';
                
                if (strpos($reason, 'VPN') !== false || strpos($reason, 'Proxy') !== false) {
                    $threatTypes['VPN/Proxy']++;
                } elseif (strpos($reason, 'fingerprint') !== false) {
                    $threatTypes['Device Fingerprint']++;
                } elseif (strpos($reason, 'suspicious') !== false) {
                    $threatTypes['Suspicious Activity']++;
                } elseif (strpos($reason, 'referral') !== false) {
                    $threatTypes['Self Referral']++;
                } else {
                    $threatTypes['Other']++;
                }
            }
        }
        
        return [
            'success' => true,
            'charts' => [
                'timeline' => [
                    'labels' => $hours,
                    'data' => array_values($timelineData)
                ],
                'threat_types' => [
                    'labels' => array_keys($threatTypes),
                    'data' => array_values($threatTypes)
                ]
            ],
            'generated_at' => time()
        ];
        
    } catch (Exception $e) {
        error_log('getChartsData ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Получает статистику в реальном времени
 */
function getRealTimeStats() {
    try {
        $now = time();
        $lastHour = $now - 3600;
        $last5Minutes = $now - 300;
        
        $fraudLog = loadFraudLog();
        $vpnLog = loadVPNLog();
        
        $stats = [
            'last_hour' => [
                'fraud_attempts' => 0,
                'vpn_detections' => 0,
                'new_blocks' => 0
            ],
            'last_5_minutes' => [
                'fraud_attempts' => 0,
                'vpn_detections' => 0,
                'new_blocks' => 0
            ],
            'system_status' => 'operational',
            'active_threats' => 0
        ];
        
        // Анализируем последний час
        foreach ($fraudLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $lastHour) {
                $stats['last_hour']['fraud_attempts']++;
                if ($entryTime >= $last5Minutes) {
                    $stats['last_5_minutes']['fraud_attempts']++;
                }
            }
        }
        
        foreach ($vpnLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $lastHour && ($entry['vpn_detected'] ?? false)) {
                $stats['last_hour']['vpn_detections']++;
                if ($entryTime >= $last5Minutes) {
                    $stats['last_5_minutes']['vpn_detections']++;
                }
            }
        }
        
        // Определяем статус системы
        $totalThreats = $stats['last_hour']['fraud_attempts'] + $stats['last_hour']['vpn_detections'];
        if ($totalThreats > 50) {
            $stats['system_status'] = 'high_load';
        } elseif ($totalThreats > 20) {
            $stats['system_status'] = 'moderate_load';
        }
        
        $stats['active_threats'] = $totalThreats;
        
        return [
            'success' => true,
            'real_time' => $stats,
            'timestamp' => $now
        ];
        
    } catch (Exception $e) {
        error_log('getRealTimeStats ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Получает топ угроз
 */
function getTopThreats($period = '24h') {
    try {
        $timeRange = getTimeRange($period);
        $userData = loadUserData();
        
        $threats = [];
        
        foreach ($userData as $userId => $user) {
            if ($user['blocked'] ?? false) {
                $blockedAt = strtotime($user['blocked_at'] ?? '');
                if ($blockedAt >= $timeRange['start'] && $blockedAt <= $timeRange['end']) {
                    $threats[] = [
                        'user_id' => $userId,
                        'reason' => $user['block_reason'] ?? 'Unknown',
                        'risk_score' => $user['risk_score'] ?? 0,
                        'blocked_at' => $user['blocked_at'] ?? '',
                        'ip_address' => $user['ip_address'] ?? 'Unknown',
                        'device_fingerprint' => substr($user['device_fingerprint'] ?? '', 0, 16) . '...'
                    ];
                }
            }
        }
        
        // Сортируем по риск-скору
        usort($threats, function($a, $b) {
            return $b['risk_score'] - $a['risk_score'];
        });
        
        return [
            'success' => true,
            'top_threats' => array_slice($threats, 0, 10),
            'total_threats' => count($threats)
        ];
        
    } catch (Exception $e) {
        error_log('getTopThreats ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Вспомогательные функции

function getTimeRange($period) {
    $now = time();
    
    switch ($period) {
        case '1h':
            return ['start' => $now - 3600, 'end' => $now];
        case '6h':
            return ['start' => $now - 21600, 'end' => $now];
        case '24h':
            return ['start' => $now - 86400, 'end' => $now];
        case '7d':
            return ['start' => $now - 604800, 'end' => $now];
        case '30d':
            return ['start' => $now - 2592000, 'end' => $now];
        default:
            return ['start' => $now - 86400, 'end' => $now];
    }
}

function loadFraudLog() {
    $logFile = __DIR__ . '/../database/fraud_log.json';
    if (file_exists($logFile)) {
        return json_decode(file_get_contents($logFile), true) ?? [];
    }
    return [];
}

function loadVPNLog() {
    $logFile = __DIR__ . '/../database/vpn_analysis_log.json';
    if (file_exists($logFile)) {
        return json_decode(file_get_contents($logFile), true) ?? [];
    }
    return [];
}

function loadBlockedDevices() {
    $logFile = __DIR__ . '/../database/blocked_devices.json';
    if (file_exists($logFile)) {
        return json_decode(file_get_contents($logFile), true) ?? [];
    }
    return [];
}

function generateHourlyStats($fraudLog, $vpnLog, $timeRange) {
    $hourlyStats = [];
    
    for ($i = 0; $i < 24; $i++) {
        $hourStart = $timeRange['start'] + ($i * 3600);
        $hourEnd = $hourStart + 3600;
        
        $hourStats = [
            'hour' => date('H:00', $hourStart),
            'fraud_attempts' => 0,
            'vpn_detections' => 0,
            'total_events' => 0
        ];
        
        foreach ($fraudLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $hourStart && $entryTime < $hourEnd) {
                $hourStats['fraud_attempts']++;
                $hourStats['total_events']++;
            }
        }
        
        foreach ($vpnLog as $entry) {
            $entryTime = $entry['timestamp'] ?? 0;
            if ($entryTime >= $hourStart && $entryTime < $hourEnd && ($entry['vpn_detected'] ?? false)) {
                $hourStats['vpn_detections']++;
                $hourStats['total_events']++;
            }
        }
        
        $hourlyStats[] = $hourStats;
    }
    
    return $hourlyStats;
}
?>
