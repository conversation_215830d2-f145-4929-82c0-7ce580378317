<?php
echo "🧪 ПРОСТОЙ ТЕСТ CALLBACK\n\n";

// Тестовые данные
$testData = [
    'id' => '5003353973',
    'extra_id' => '7947418432'
];

echo "Тестовые данные: " . json_encode($testData) . "\n";

// Извлекаем user_id
$userId = $testData['extra_id'] ?? null;
echo "Извлечен user_id: {$userId}\n";

if ($userId) {
    $userId = intval($userId);
    echo "Конвертирован в integer: {$userId}\n";
    
    // Проверяем файл
    if (file_exists('api/user_data.json')) {
        echo "✅ Файл user_data.json существует\n";
        
        $jsonContent = file_get_contents('api/user_data.json');
        if ($jsonContent) {
            echo "✅ Файл успешно прочитан\n";
            
            $userData = json_decode($jsonContent, true);
            if ($userData && isset($userData[$userId])) {
                echo "✅ Пользователь {$userId} найден\n";
                echo "Имя: " . ($userData[$userId]['first_name'] ?? 'не указано') . "\n";
            } else {
                echo "❌ Пользователь {$userId} не найден\n";
            }
        } else {
            echo "❌ Не удалось прочитать файл\n";
        }
    } else {
        echo "❌ Файл user_data.json не найден\n";
    }
}

echo "\nТест завершен.\n";
?>
