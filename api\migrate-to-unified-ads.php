<?php
/**
 * api/migrate-to-unified-ads.php
 * Скрипт миграции для обновления существующих PHP файлов
 * Обновляет старые файлы для совместимости с новой архитектурой
 */

require_once __DIR__ . '/ads-config.php';

class AdsMigration {
    private $backupDir;
    private $migrationLog = [];
    
    public function __construct() {
        $this->backupDir = __DIR__ . '/backup_' . date('Y-m-d_H-i-s');
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * Главный метод миграции
     */
    public function migrate() {
        echo "🚀 Начинаем миграцию к единой архитектуре рекламы...\n";
        
        try {
            // 1. Создаем резервные копии
            $this->createBackups();
            
            // 2. Обновляем config.php
            $this->updateConfigFile();
            
            // 3. Обновляем существующие API файлы
            $this->updateApiFiles();
            
            // 4. Создаем файлы совместимости
            $this->createCompatibilityFiles();
            
            // 5. Проверяем миграцию
            $this->validateMigration();
            
            echo "✅ Миграция завершена успешно!\n";
            $this->printMigrationSummary();
            
        } catch (Exception $e) {
            echo "❌ Ошибка миграции: " . $e->getMessage() . "\n";
            $this->rollback();
        }
    }
    
    /**
     * Создание резервных копий
     */
    private function createBackups() {
        echo "📦 Создаем резервные копии...\n";
        
        $filesToBackup = [
            'config.php',
            'recordAdView.php',
            'ad_limits_manager.php',
            'logAdClick.php',
            'ad_functions.php'
        ];
        
        foreach ($filesToBackup as $file) {
            $sourcePath = __DIR__ . '/' . $file;
            if (file_exists($sourcePath)) {
                $backupPath = $this->backupDir . '/' . $file;
                copy($sourcePath, $backupPath);
                $this->migrationLog[] = "Создана резервная копия: $file";
                echo "  ✓ $file\n";
            }
        }
    }
    
    /**
     * Обновление config.php
     */
    private function updateConfigFile() {
        echo "🔧 Обновляем config.php...\n";
        
        $configPath = __DIR__ . '/config.php';
        if (!file_exists($configPath)) {
            echo "  ⚠️ config.php не найден, пропускаем\n";
            return;
        }
        
        $content = file_get_contents($configPath);
        
        // Добавляем подключение новой конфигурации
        $newInclude = "\n// Подключение новой централизованной конфигурации рекламы\nrequire_once __DIR__ . '/ads-config.php';\n";
        
        if (strpos($content, 'ads-config.php') === false) {
            // Добавляем после первого <?php
            $content = preg_replace('/(<\?php\s*)/', '$1' . $newInclude, $content, 1);
            file_put_contents($configPath, $content);
            $this->migrationLog[] = "Обновлен config.php - добавлено подключение ads-config.php";
            echo "  ✓ Добавлено подключение ads-config.php\n";
        }
    }
    
    /**
     * Обновление API файлов
     */
    private function updateApiFiles() {
        echo "🔄 Обновляем API файлы...\n";
        
        // Обновляем recordAdView.php
        $this->updateRecordAdView();
        
        // Обновляем logAdClick.php
        $this->updateLogAdClick();
        
        // Обновляем ad_limits_manager.php
        $this->updateAdLimitsManager();
    }
    
    /**
     * Обновление recordAdView.php
     */
    private function updateRecordAdView() {
        $filePath = __DIR__ . '/recordAdView.php';
        if (!file_exists($filePath)) return;
        
        $content = file_get_contents($filePath);
        
        // Добавляем редирект на новый API
        $redirectCode = '
// МИГРАЦИЯ: Редирект на новый единый API
if (!isset($_GET[\'legacy\'])) {
    $_REQUEST[\'action\'] = \'record_view\';
    require_once __DIR__ . \'/ads-api.php\';
    exit;
}
';
        
        if (strpos($content, 'ads-api.php') === false) {
            $content = preg_replace('/(<\?php\s*)/', '$1' . $redirectCode, $content, 1);
            file_put_contents($filePath, $content);
            $this->migrationLog[] = "Обновлен recordAdView.php - добавлен редирект на новый API";
            echo "  ✓ recordAdView.php\n";
        }
    }
    
    /**
     * Обновление logAdClick.php
     */
    private function updateLogAdClick() {
        $filePath = __DIR__ . '/logAdClick.php';
        if (!file_exists($filePath)) return;
        
        $content = file_get_contents($filePath);
        
        $redirectCode = '
// МИГРАЦИЯ: Редирект на новый единый API
if (!isset($_GET[\'legacy\'])) {
    $_REQUEST[\'action\'] = \'log_click\';
    require_once __DIR__ . \'/ads-api.php\';
    exit;
}
';
        
        if (strpos($content, 'ads-api.php') === false) {
            $content = preg_replace('/(<\?php\s*)/', '$1' . $redirectCode, $content, 1);
            file_put_contents($filePath, $content);
            $this->migrationLog[] = "Обновлен logAdClick.php - добавлен редирект на новый API";
            echo "  ✓ logAdClick.php\n";
        }
    }
    
    /**
     * Обновление ad_limits_manager.php
     */
    private function updateAdLimitsManager() {
        $filePath = __DIR__ . '/ad_limits_manager.php';
        if (!file_exists($filePath)) return;
        
        $content = file_get_contents($filePath);
        
        // Добавляем подключение новой конфигурации
        $includeCode = "\nrequire_once __DIR__ . '/ads-config.php';\n";
        
        if (strpos($content, 'ads-config.php') === false) {
            $content = preg_replace('/(<\?php\s*)/', '$1' . $includeCode, $content, 1);
            file_put_contents($filePath, $content);
            $this->migrationLog[] = "Обновлен ad_limits_manager.php - добавлено подключение конфигурации";
            echo "  ✓ ad_limits_manager.php\n";
        }
    }
    
    /**
     * Создание файлов совместимости
     */
    private function createCompatibilityFiles() {
        echo "🔗 Создаем файлы совместимости...\n";
        
        // Создаем legacy-wrapper для старых вызовов
        $this->createLegacyWrapper();
        
        // Создаем файл конфигурации для JavaScript
        $this->createJsConfigFile();
    }
    
    /**
     * Создание legacy wrapper
     */
    private function createLegacyWrapper() {
        $wrapperPath = __DIR__ . '/ads-legacy-wrapper.php';
        
        $wrapperContent = '<?php
/**
 * ads-legacy-wrapper.php
 * Обертка для обратной совместимости со старыми вызовами API
 */

require_once __DIR__ . \'/ads-config.php\';

// Маппинг старых функций на новые
function recordAdView($userId, $adType, $initData) {
    $_REQUEST[\'action\'] = \'record_view\';
    $_POST = json_encode([
        \'initData\' => $initData,
        \'adType\' => $adType
    ]);
    
    ob_start();
    require __DIR__ . \'/ads-api.php\';
    $result = ob_get_clean();
    
    return json_decode($result, true);
}

function logAdClick($userId, $adType, $clickType, $initData) {
    $_REQUEST[\'action\'] = \'log_click\';
    $_POST = json_encode([
        \'initData\' => $initData,
        \'adType\' => $adType,
        \'clickType\' => $clickType
    ]);
    
    ob_start();
    require __DIR__ . \'/ads-api.php\';
    $result = ob_get_clean();
    
    return json_decode($result, true);
}

// Экспорт констант для совместимости
foreach (AdsConfig::getAllAdTypes() as $adType) {
    $constantName = \'AD_REWARD_\' . strtoupper($adType[\'id\']);
    if (!defined($constantName)) {
        define($constantName, $adType[\'reward\']);
    }
}
?>';
        
        file_put_contents($wrapperPath, $wrapperContent);
        $this->migrationLog[] = "Создан ads-legacy-wrapper.php";
        echo "  ✓ ads-legacy-wrapper.php\n";
    }
    
    /**
     * Создание JS конфигурационного файла
     */
    private function createJsConfigFile() {
        $configPath = __DIR__ . '/ads-config.json';
        
        $jsConfig = AdsConfig::getJsConfig();
        file_put_contents($configPath, json_encode($jsConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $this->migrationLog[] = "Создан ads-config.json для JavaScript";
        echo "  ✓ ads-config.json\n";
    }
    
    /**
     * Валидация миграции
     */
    private function validateMigration() {
        echo "🔍 Проверяем миграцию...\n";
        
        $errors = [];
        
        // Проверяем новые файлы
        $requiredFiles = [
            'ads-config.php',
            'ads-api.php',
            'ads-legacy-wrapper.php',
            'ads-config.json'
        ];
        
        foreach ($requiredFiles as $file) {
            if (!file_exists(__DIR__ . '/' . $file)) {
                $errors[] = "Отсутствует файл: $file";
            }
        }
        
        // Проверяем конфигурацию
        $validation = AdsConfig::validate();
        if (!$validation['is_valid']) {
            $errors = array_merge($errors, $validation['errors']);
        }
        
        if (!empty($errors)) {
            throw new Exception("Ошибки валидации: " . implode(', ', $errors));
        }
        
        echo "  ✓ Все проверки пройдены\n";
    }
    
    /**
     * Откат изменений
     */
    private function rollback() {
        echo "🔄 Выполняем откат изменений...\n";
        
        $backupFiles = glob($this->backupDir . '/*');
        foreach ($backupFiles as $backupFile) {
            $originalFile = __DIR__ . '/' . basename($backupFile);
            copy($backupFile, $originalFile);
            echo "  ✓ Восстановлен " . basename($backupFile) . "\n";
        }
    }
    
    /**
     * Вывод сводки миграции
     */
    private function printMigrationSummary() {
        echo "\n📋 Сводка миграции:\n";
        foreach ($this->migrationLog as $entry) {
            echo "  • $entry\n";
        }
        
        echo "\n📁 Резервные копии сохранены в: {$this->backupDir}\n";
        echo "\n🎯 Следующие шаги:\n";
        echo "  1. Проверьте работу рекламной системы\n";
        echo "  2. Обновите JavaScript код для использования новых модулей\n";
        echo "  3. При необходимости используйте legacy режим (?legacy=1)\n";
        echo "  4. После проверки можно удалить резервные копии\n";
    }
}

// Запуск миграции если файл вызван напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $migration = new AdsMigration();
    $migration->migrate();
}
?>
