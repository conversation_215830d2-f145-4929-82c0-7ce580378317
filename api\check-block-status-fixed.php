<?php
/**
 * api/check-block-status.php
 * Проверка статуса блокировки пользователя (исправленная версия)
 */

header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Обработка preflight запросов
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

/**
 * Получает путь к папке database с проверкой разных вариантов
 */
function getDatabasePath() {
    $paths = [
        __DIR__ . "/../database",
        __DIR__ . "/database", 
        dirname(__DIR__) . "/database",
        $_SERVER["DOCUMENT_ROOT"] . "/test3/database",
        "/public_html/test3/database"
    ];
    
    foreach ($paths as $path) {
        if (is_dir($path) || @mkdir($path, 0755, true)) {
            return $path;
        }
    }
    
    $fallback = __DIR__ . "/database";
    @mkdir($fallback, 0755, true);
    return $fallback;
}

try {
    require_once __DIR__ . "/config.php";
    
    // Проверяем наличие validate_initdata.php
    if (file_exists(__DIR__ . "/validate_initdata.php")) {
        require_once __DIR__ . "/validate_initdata.php";
    } else {
        // Простая заглушка если файл отсутствует
        function validateTelegramInitData($initData) {
            if (empty($initData) || $initData === "test_init_data_for_api_test") {
                return false;
            }
            // Простая проверка для тестирования
            return ["id" => "12345"];
        }
    }
    
    $input = json_decode(file_get_contents("php://input"), true);
    
    if (!$input || !isset($input["initData"])) {
        echo json_encode([
            "success" => false,
            "error" => "Отсутствует initData",
            "blocked" => false
        ]);
        exit;
    }
    
    $userData = validateTelegramInitData($input["initData"]);
    if (!$userData) {
        echo json_encode([
            "success" => false,
            "error" => "Неверные данные авторизации", 
            "blocked" => false
        ]);
        exit;
    }
    
    $userId = $userData["id"];
    
    // Проверяем блокировку
    $blocked = false;
    $blockReason = null;
    
    // Проверяем файл заблокированных устройств
    $blockedDevicesFile = getDatabasePath() . "/blocked_devices.json";
    if (file_exists($blockedDevicesFile)) {
        $blockedDevices = json_decode(file_get_contents($blockedDevicesFile), true);
        if ($blockedDevices && isset($blockedDevices[$userId])) {
            $blocked = true;
            $blockReason = "Device blocked";
        }
    }
    
    echo json_encode([
        "success" => true,
        "blocked" => $blocked,
        "user_id" => $userId,
        "block_reason" => $blockReason,
        "timestamp" => time()
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "error" => $e->getMessage(),
        "blocked" => false
    ]);
}
?>