<?php
/**
 * ads-legacy-wrapper.php
 * Обертка для обратной совместимости со старыми вызовами API
 */

require_once __DIR__ . '/ads-config.php';

// Маппинг старых функций на новые
function recordAdView($userId, $adType, $initData) {
    $_REQUEST['action'] = 'record_view';
    $_POST = json_encode([
        'initData' => $initData,
        'adType' => $adType
    ]);
    
    ob_start();
    require __DIR__ . '/ads-api.php';
    $result = ob_get_clean();
    
    return json_decode($result, true);
}

function logAdClick($userId, $adType, $clickType, $initData) {
    $_REQUEST['action'] = 'log_click';
    $_POST = json_encode([
        'initData' => $initData,
        'adType' => $adType,
        'clickType' => $clickType
    ]);
    
    ob_start();
    require __DIR__ . '/ads-api.php';
    $result = ob_get_clean();
    
    return json_decode($result, true);
}

// Экспорт констант для совместимости
foreach (AdsConfig::getAllAdTypes() as $adType) {
    $constantName = 'AD_REWARD_' . strtoupper($adType['id']);
    if (!defined($constantName)) {
        define($constantName, $adType['reward']);
    }
}
?>