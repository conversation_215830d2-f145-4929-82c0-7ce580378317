/* ======================================== */
/* CRYPTO WALLET TELEGRAM MINI APP - ELEGANT STYLE */
/* ======================================== */

/* Import elegant fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap");

/* --- ELEGANT COLOR PALETTE & VARIABLES --- */
:root {
  /* Dark Cyberpunk Color Scheme */
  --primary-bg: #1a1a1a;
  --secondary-bg: #2a2a2a;
  --card-bg: #333333;
  --accent-primary: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  --accent-secondary: #FFA500; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.2);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-strong: rgba(0, 0, 0, 0.25);
  --success-color: #4CAF50;
  --error-color: #F44336;
  --warning-color: #FF9800;

  /* Legacy compatibility */
  --app-bg-color: var(--secondary-bg);
  --app-secondary-bg-color: var(--card-bg);
  --app-text-color: var(--text-primary);
  --app-hint-color: var(--text-secondary);
  --app-primary-color: var(--accent-primary);
  --app-primary-text-color: var(--text-primary);
  --app-secondary-button-bg: var(--accent-secondary);
  --app-secondary-button-text: var(--text-primary);
  --app-destructive-color: var(--error-color);
  --app-separator-color: var(--border-light);

  /* Button colors - unified elegant style */
  --button-primary: var(--accent-primary);
  --button-secondary: var(--accent-secondary);
  --page-transition-duration: 0.3s;

  /* Sprite settings */
  --sprite-url: "images/sprite.svg";
  --icon-width: 24px;
  --icon-height: 24px;
  --sprite-total-width: calc(var(--icon-width) * 3);
  --sprite-total-height: calc(var(--icon-height) * 3);
}

/* --- ELEGANT ANIMATIONS --- */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes gentlePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* --- ELEGANT BACKGROUND --- */
.cyberpunk-coins-bg {
  display: none; /* Remove cyberpunk background */
}

/* Remove glitch lines */
.glitch-line {
  display: none;
}

/* --- GLOBAL STYLES --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, sans-serif;
  color: var(--text-primary);
  background:
    linear-gradient(135deg, #FFF8E1 0%, #FFECB3 25%, #FFE082 50%, #FFECB3 75%, #FFF8E1 100%),
    radial-gradient(circle at 20% 80%, rgba(255, 193, 7, 0.1) 0%, transparent 50%), /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    radial-gradient(circle at 80% 20%, rgba(255, 165, 0, 0.08) 0%, transparent 50%); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
  font-weight: 400;
  line-height: 1.5;
}

/* ÃƒÂ°Ã…Â¸Ã¢â‚¬â€œÃ‚Â¼ÃƒÂ¯Ã‚Â¸Ã‚Â ÃƒÂÃ…Â¸ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ‚Â£ÃƒÂÃ…Â¸ÃƒÂÃ‚Â ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬â€ÃƒÂÃ‚Â ÃƒÂÃ‚ÂÃƒÂÃ‚Â§ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬Â¢ ÃƒÂÃ‚Â¤ÃƒÂÃ…Â¾ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬â„¢ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬Â¢ ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬ËœÃƒÂÃ‚Â ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬Â¢ ÃƒÂÃ¢â‚¬â„¢ ÃƒÂÃ…Â¸ÃƒÂÃ‚Â¡ÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ¢â‚¬â„¢ÃƒÂÃ¢â‚¬ÂÃƒÂÃ…Â¾ÃƒÂÃ‚Â­ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ‚Â¢ÃƒÂÃ¢â‚¬Â¢ */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    /* Сочные салатовые геометрические фигуры */
    radial-gradient(circle at 20% 30%, rgba(50, 205, 50, 0.3) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(34, 139, 34, 0.25) 0%, transparent 35%),
    radial-gradient(circle at 60% 20%, rgba(124, 252, 0, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 30% 80%, rgba(0, 255, 127, 0.15) 0%, transparent 25%),
    radial-gradient(circle at 90% 40%, rgba(50, 205, 50, 0.18) 0%, transparent 28%),
    radial-gradient(circle at 10% 60%, rgba(144, 238, 144, 0.22) 0%, transparent 32%),
    linear-gradient(45deg, rgba(50, 205, 50, 0.1) 0%, transparent 50%, rgba(34, 139, 34, 0.08) 100%),
    linear-gradient(135deg, rgba(124, 252, 0, 0.05) 0%, transparent 60%, rgba(0, 255, 127, 0.06) 100%) !important;
  opacity: 0.5; /* ÃƒÂ°Ã…Â¸Ã…Â½Ã‚Â¯ ÃƒÂÃ…Â¸ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ‚Â£ÃƒÂÃ…Â¸ÃƒÂÃ‚Â ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬â€ÃƒÂÃ‚Â ÃƒÂÃ‚ÂÃƒÂÃ‚Â§ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ÃƒÂÃ‚Â¡ÃƒÂÃ‚Â¢ÃƒÂÃ‚Â¬ 50% */
  z-index: -1;
  pointer-events: none;
}

/* ÃƒÂÃ‚Â¡ÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¿Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂµÃƒÂÃ‚Â²ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â‚¬Â¹ */
body::after,
.app-container::after {
  display: none;
}

/* --- MAIN CONTAINER --- */
.app-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0 16px 80px 16px;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
  max-width: 480px;
  margin: 0 auto;
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    transparent 0%,
    #FFA500 20%,
    #FFC107 40%,
    #FFD54F 60%,
    #FFC107 80%,
    transparent 100%); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  z-index: 1000;
  animation: glitchStripe 3s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

@keyframes glitchStripe {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100vw);
  }
}

/* --- HEADER STYLES --- */
.app-header {
  width: 100%;
  background: var(--secondary-bg);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-light);
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  z-index: 10;
  box-shadow: 0 2px 8px var(--shadow-light);
  height: 64px;
}

.app-header::before {
  display: none;
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  overflow: hidden;
  border: 2px solid #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background: linear-gradient(135deg, #FFA500, #FFC107) !important; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  box-shadow: 0 0 12px rgba(255, 193, 7, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  position: relative;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
}

.balance-info {
  display: flex;
  align-items: center;
  background: var(--accent-primary);
  padding: 8px 16px;
  border-radius: 20px;
  flex-shrink: 0;
  transition: all 0.3s ease;
  min-width: 90px;
  height: 36px;
  justify-content: center;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.balance-info:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.balance-amount {
  font-size: 14px;
  font-weight: 600;
  margin-right: 4px;
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  white-space: nowrap;
}

.balance-currency {
  font-size: 12px;
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.8;
}

/* --- SECTIONS & PAGES --- */
/* КРИТИЧЕСКИ ВАЖНЫЕ СТИЛИ ДЛЯ ОТСТУПОВ - НЕ ТРОГАТЬ! */
.app-main,
.app-section {
  display: flex !important;
  flex-direction: column !important;
  gap: 20px !important; /* ЗАЩИЩЕННЫЕ ОТСТУПЫ МЕЖДУ КНОПКАМИ - НЕ УДАЛЯТЬ! */
  width: 100%;
  position: fixed;
  top: 64px;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  bottom: 80px;
  padding: 20px 16px;
  opacity: 1;
  transition: opacity var(--page-transition-duration) ease-in-out;
  will-change: opacity;
  background-color: transparent;
  z-index: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* ДОПОЛНИТЕЛЬНАЯ ЗАЩИТА ОТСТУПОВ */
.app-main > *,
.app-section > * {
  margin-bottom: 0 !important; /* Убираем margin-bottom, используем только gap */
  margin-top: 0 !important;    /* Убираем margin-top, используем только gap */
}

/* ИСКЛЮЧЕНИЯ ДЛЯ ЗАГОЛОВКОВ */
.app-main h2,
.app-section h2 {
  margin-bottom: 24px !important; /* Заголовки могут иметь отступ снизу */
}

.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}

.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
  display: flex !important;
}

.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 24px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 28px;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.5px;
  position: relative;
}

/* ÃƒÂÃ…Â¸Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂµÃƒÂÃ‚Â²ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â‚¬Â¹ Ãƒâ€˜Ã†â€™ ÃƒÂÃ‚Â·ÃƒÂÃ‚Â°ÃƒÂÃ‚Â³ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂºÃƒÂÃ‚Â»Ãƒâ€˜Ã…Â½Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ */
.app-main h2::before,
.app-section h2::before,
.app-main h2::after,
.app-section h2::after {
  display: none;
}

/* --- STATUS MESSAGE --- */
.status-message {
  padding: 16px 20px;
  font-size: 14px;
  text-align: center;
  min-height: 20px;
  border-radius: 12px;
  word-wrap: break-word;
  margin-bottom: 20px;
  background: var(--card-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  opacity: 1;
  font-weight: 500;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--success-color);
  background: rgba(76, 175, 80, 0.1);
  border-color: var(--success-color);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.status-message.error {
  color: var(--error-color);
  background: rgba(244, 67, 54, 0.1);
  border-color: var(--error-color);
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2);
}

/* --- ELEGANT BUTTONS --- */
.action-button {
  width: 100%;
  padding: 18px 60px 18px 28px; /* Увеличен правый отступ для иконки награды */
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center; /* Центрирование содержимого кнопки */
  gap: 8px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center; /* Центрирование текста */
  font-family: "Inter", sans-serif;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 165, 0, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  transform: translateY(0);
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow:
    0 16px 32px rgba(255, 193, 7, 0.5),
    0 8px 16px rgba(255, 193, 7, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(255, 165, 0, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background: linear-gradient(145deg, #FFEB3B, #FFD54F);
}

.action-button:active {
  transform: translateY(1px);
  box-shadow:
    0 6px 12px rgba(255, 193, 7, 0.3),
    0 3px 6px rgba(255, 193, 7, 0.2),
    inset 0 1px 0 rgba(0, 0, 0, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background: linear-gradient(145deg, #FFC107, #FFA500); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

/* Button variants - all use same elegant style */
.action-button.purple-button,
.action-button.orange-button,
.action-button.yellow-button {
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 165, 0, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.action-button.purple-button:hover,
.action-button.orange-button:hover,
.action-button.yellow-button:hover {
  transform: translateY(-3px);
  box-shadow:
    0 16px 32px rgba(255, 193, 7, 0.5),
    0 8px 16px rgba(255, 193, 7, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(255, 165, 0, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background: linear-gradient(145deg, #FFEB3B, #FFD54F);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: var(--border-medium) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
  pointer-events: none !important;
}

.action-button:disabled::before {
  display: none;
}

.action-button:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Pressed effect */
.action-button.pressed {
  transform: translateY(2px) scale(0.98);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4), inset 0 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: none !important;
  cursor: not-allowed !important;
  opacity: 0.7;
}

/* Countdown overlay */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.9),
    rgba(26, 26, 46, 0.9)
  );
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 900;
  color: #FFFFFF;
  border-radius: 12px;
  z-index: 10;
  font-family: "Orbitron", monospace;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 193, 7, 0.6),
    0 0 30px rgba(255, 193, 7, 0.4),
    2px 2px 4px rgba(0, 0, 0, 0.8); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  animation: pulse 1s ease-in-out infinite;
  pointer-events: all !important;
  cursor: not-allowed !important;
  border: 2px solid rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  box-shadow:
    inset 0 0 20px rgba(255, 193, 7, 0.1),
    0 0 20px rgba(0, 0, 0, 0.5); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

/* ÃƒÂÃ¢â‚¬ÂÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂºÃƒâ€˜Ã‚ÂÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¹ ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚Â¹ÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â° */
.countdown-overlay::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 40px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: -1;
  border: 1px solid rgba(255, 193, 7, 0.5); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

/* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¾Ãƒâ€˜Ã¢â€šÂ¬ Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚Â¹ÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â° */
.countdown-overlay .countdown-time {
  position: relative;
  z-index: 2;
  /* ИСПРАВЛЕНИЕ: Убираем градиент и розовые эффекты */
  color: #FFFFFF !important;
  font-weight: 900;
  font-size: 26px;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    2px 2px 4px rgba(0, 0, 0, 0.8) !important;
  /* ИСПРАВЛЕНИЕ: Убираем все градиенты и фильтры */
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: #FFFFFF !important;
  background-clip: unset !important;
  filter: none !important;
  /* ИСПРАВЛЕНИЕ: Убираем любые box-shadow и border-radius */
  box-shadow: none !important;
  border-radius: 0 !important;
  border: none !important;
}

/* --- NAVIGATION --- */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 480px;
  width: calc(100% - 32px);
  background: var(--secondary-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 16px max(12px, env(safe-area-inset-bottom)) 16px;
  border-radius: 24px 24px 0 0;
  border-top: 1px solid var(--border-light);
  z-index: 100;
  height: 80px;
  box-shadow: 0 -4px 20px var(--shadow-light);
  margin-bottom: 0;
}

.app-nav::before {
  display: none;
}

.nav-button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  font-size: 12px;
  flex-grow: 1;
  transition: all 0.3s ease;
  border-radius: 12px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.nav-button:hover {
  color: var(--text-secondary);
  background: rgba(255, 215, 0, 0.1);
}

.nav-button.active {
  color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  background: linear-gradient(145deg, #FFD54F, #FFC107);
  border-radius: 18px;
  box-shadow:
    0 8px 16px rgba(255, 193, 7, 0.4),
    0 4px 8px rgba(255, 193, 7, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(255, 165, 0, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border: none;
  transform: translateY(-2px);
}

.nav-button .nav-icon,
.nav-button .cyber-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  filter: brightness(0) invert(0.4);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-button.active .nav-icon {
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%)
    hue-rotate(83deg) brightness(99%) contrast(93%);
  animation: neonGlow 2s ease-in-out infinite;
}

/* ÃƒÂÃ…Â¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ SVG ÃƒÂÃ‚Â¸ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â½ÃƒÂÃ‚Â°ÃƒÂÃ‚Â²ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â³ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¸ */
.nav-button .cyber-icon {
  fill: #666666;
  stroke: #666666;
  stroke-width: 1.5;
  filter: none;
  transition: all 0.3s ease;
}

.nav-button:hover .cyber-icon {
  fill: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  stroke: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  filter: drop-shadow(0 0 4px rgba(255, 193, 7, 0.5)); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.nav-button.active .cyber-icon {
  fill: #1A1A1A;
  stroke: #1A1A1A;
  filter: drop-shadow(0 0 8px rgba(26, 26, 26, 0.8));
}

.nav-button:active {
  transform: translateY(0) scale(0.95);
}

/* --- CONTENT BLOCKS --- */
.friends-block,
.earn-block {
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  padding: 28px;
  border-radius: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  box-shadow:
    0 8px 20px rgba(255, 193, 7, 0.15),
    0 4px 10px rgba(255, 165, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 193, 7, 0.1); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  position: relative;
  animation: fadeIn 0.6s ease-out;
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

.friends-block::before,
.earn-block::before {
  display: none;
}

.friends-block h3,
.earn-block h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â²ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¼ Ãƒâ€˜Ã¢â‚¬Å¾ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½ÃƒÂÃ‚Âµ */
  font-size: 20px;
  font-weight: 600;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.3px;
}

.friends-block p,
.earn-block p {
  font-size: 14px;
  color: #4d4d4d; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ */
  margin-top: 0;
  margin-bottom: 16px;
  line-height: 1.6;
  font-family: "Inter", sans-serif;
}

.friends-block p.hint,
.earn-block p.hint {
  font-size: 13px;
  color: #666666; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ */
  opacity: 0.8;
}

/* ÃƒÂÃ…Â¸Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â½Ãƒâ€˜Ã†â€™ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚ÂºÃƒÂÃ‚Â°ÃƒÂÃ‚Â·Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ ÃƒÂÃ‚Â²Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Âµ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â‚¬Â¹ ÃƒÂÃ‚Â²ÃƒÂÃ‚Â½Ãƒâ€˜Ã†â€™Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â±ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² */
.friends-block *,
.earn-block * {
  max-height: none !important;
  overflow: visible !important;
}

/* ÃƒÂÃ‚Â£ÃƒÂÃ‚Â±ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ ÃƒÂÃ‚Â»Ãƒâ€˜Ã…Â½ÃƒÂÃ‚Â±Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â³Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â‚¬Â¹ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â²Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â‚¬Â¦ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â¦ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² */
.friends-block > *,
.earn-block > * {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* --- FORMS --- */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: -10px;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: 500;
  text-transform: none; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  letter-spacing: 0.5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 16px 20px;
  border: 1px solid var(--border-light);
  background: var(--card-bg);
  color: var(--text-primary);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  font-family: "Inter", sans-serif;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.withdrawal-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23FFC107%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E"); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background-repeat: no-repeat;
  background-position: right 15px top 50%;
  background-size: 12px auto;
  padding-right: 40px;
}

/* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â option Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² */
.withdrawal-form select option {
  background: #333333; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  color: var(--text-primary);
  padding: 12px 15px;
  border: none;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-size: 14px;
  font-weight: 500;
}

.withdrawal-form select option:hover,
.withdrawal-form select option:focus {
  background: #4a4a4a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  color: var(--accent-primary);
}

.withdrawal-form select option:checked {
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  font-weight: 600;
}

/* --- REFERRAL LINK AREA --- */
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  background: var(--card-bg);
  color: var(--text-primary);
  border-radius: 12px;
  font-size: 14px;
  font-family: "Inter", monospace;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.referral-link-area .copy-button {
  padding: 0;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border: 1px solid var(--accent-primary);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
}

.referral-link-area .copy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
  background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary));
  border-color: var(--accent-primary);
}

/* --- CALCULATOR STYLES --- */
.calculator-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--card-bg);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.calculator-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  font-family: "Inter", sans-serif;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.balance-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-family: "Inter", sans-serif;
}

.balance-amount {
  color: var(--accent-secondary);)
  --accent-secondary; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: 600;
  font-size: 16px;
  font-family: "Orbitron", monospace;
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.amount-input-section {
  background: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border: 1px solid var(--border-light); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
}

.amount-input-section label {
  display: block;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  text-transform: none; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  letter-spacing: 1px;
}

.input-group {
  position: relative;
  margin-bottom: 15px;
}

.input-group input {
  width: 100%;
  padding: 18px 90px 18px 18px;
  border: 1px solid var(--border-light);
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  box-sizing: border-box;
  background: var(--card-bg);
  color: var(--text-primary);
  transition: all 0.3s ease;
  font-family: "Inter", sans-serif;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.input-group input:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  outline: none;
}

.input-suffix {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  font-family: "Inter", sans-serif;
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

#dollar-equivalent {
  font-weight: 600;
  color: var(--accent-primary); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-size: 18px;
  font-family: "Orbitron", monospace;
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.balance-status {
  padding: 6px 15px;
  border-radius: 25px;
  font-size: 12px;
  font-weight: 500;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-status.sufficient {
  background: rgba(76, 175, 80, 0.2);
  color: var(--success-color);
  border: 1px solid var(--success-color);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.balance-status.insufficient {
  background: rgba(244, 67, 54, 0.2);
  color: var(--error-color);
  border: 1px solid var(--error-color);
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
}

.balance-status.neutral {
  background: rgba(176, 176, 176, 0.2);
  color: var(--text-muted);
  border: 1px solid var(--text-muted);
}

/* --- CURRENCY TABS --- */
.currency-tabs-container {
  margin-top: 25px;
}

.currency-tabs-header {
  display: flex;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 8px;
  margin-bottom: 24px;
  overflow-x: auto;
  gap: 6px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
  flex-wrap: nowrap;
  -webkit-overflow-scrolling: touch;
}

.currency-tab {
  flex: 1;
  min-width: 85px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 18px 14px;
  border: 1px solid rgba(255, 193, 7, 0.2); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  flex-shrink: 1;
  color: var(--text-secondary);
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  position: relative;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  box-shadow:
    0 4px 8px rgba(255, 193, 7, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.currency-tab:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  color: var(--text-primary);
  transform: translateY(-3px);
  box-shadow:
    0 6px 12px rgba(255, 193, 7, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border-color: rgba(255, 193, 7, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.currency-tab.active {
  background: linear-gradient(145deg, #FFD54F, #FFC107); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow:
    0 8px 16px rgba(255, 193, 7, 0.3),
    0 4px 8px rgba(255, 193, 7, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: 700;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}

/* ÃƒÂÃ‚Â­ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â³ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¸ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»Ãƒâ€˜Ã…Â½Ãƒâ€˜Ã¢â‚¬Å¡ ÃƒÂÃ‚Â² Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â²ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¶ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â */
.currency-tab[data-currency="eth"] .tab-icon,
.currency-tab[data-currency="btc"] .tab-icon,
.currency-tab[data-currency="usdttrc20"] .tab-icon,
.currency-tab[data-currency="ton"] .tab-icon {
  color: #FFD700; /* ÃƒÂÃ¢â‚¬â€ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¹ */
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

/* ÃƒÂÃ‚ÂÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â²ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã†â€™Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¼ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â²ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ ÃƒÂÃ‚Â² ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¶ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â */
.currency-tab.active .tab-icon {
  color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  filter: drop-shadow(0 0 6px rgba(26, 26, 26, 0.5));
}

.tab-name {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.tab-symbol {
  font-size: 10px;
  opacity: 0.7;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-family: "Inter", monospace;
}

/* --- CURRENCY INFO CARD --- */
.currency-info-card {
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  border: 1px solid rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border-radius: 24px;
  padding: 28px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 12px 24px rgba(255, 193, 7, 0.2),
    0 6px 12px rgba(255, 165, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 193, 7, 0.1); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.currency-info-card::before {
  display: none;
}

.currency-title {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.currency-icon {
  font-size: 32px;
  color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  filter: drop-shadow(0 0 6px rgba(255, 193, 7, 0.4)); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  display: inline-block;
  margin-right: 12px;
}

.currency-full-name {
  font-size: 20px;
  font-weight: 600;
  color: #e1e1e1; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ */
  font-family: "Inter", sans-serif;
}

.currency-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-family: "Inter", sans-serif;
}

.currency-badge.status-best {
  background: var(--accent-primary);
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  box-shadow: 0 2px 8px var(--shadow-light);
}

/* --- STATS & LISTS --- */
.referral-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  min-width: 120px;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
}

.stat-label {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 8px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  font-family: "Inter", sans-serif;
}

.referrals-list,
.withdrawal-history {
  margin: 16px 0;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid var(--border-light);
  box-shadow: 0 2px 8px var(--shadow-light);
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.withdrawal-item {
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px var(--shadow-light);
}

.withdrawal-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.withdrawal-status {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  letter-spacing: 0.5px;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.status-failed {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.status-processing {
  background: linear-gradient(135deg, #FFC107, #FFA500); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.status-confirmed {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: white;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.status-cancelled {
  background: linear-gradient(135deg, #888888, #666666);
  color: white;
  box-shadow: 0 0 10px rgba(136, 136, 136, 0.3);
}

.status-expired {
  background: linear-gradient(135deg, #ff8800, #cc6600);
  color: white;
  box-shadow: 0 0 10px rgba(255, 136, 0, 0.3);
}

.status-unknown {
  background: linear-gradient(135deg, #555555, #333333);
  color: #cccccc;
  box-shadow: 0 0 10px rgba(85, 85, 85, 0.3);
}

/* --- ERROR MESSAGES --- */
.error-message {
  color: #F44336 !important; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: bold !important;
  font-style: normal !important;
  text-shadow: 0 0 5px rgba(244, 67, 54, 0.5); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  /* ÃƒÂÃ‚Â£ÃƒÂÃ‚Â±Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â´Ãƒâ€˜Ã¢â‚¬ËœÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â³ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‹â€ ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â±ÃƒÂÃ‚Â¾ÃƒÂÃ‚Âº */
}

/* --- MISC ELEMENTS --- */
.user-avatar-icon {
  width: 26px;
  height: 26px;
  color: #1A1A1A !important; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 6px rgba(0, 0, 0, 0.6));
  z-index: 3;
  position: relative;
  stroke: #1A1A1A !important; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  fill: none !important;
}

/* Elegant Icon Styles ÃƒÂÃ‚Â² Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â²ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¶ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â */
.section-icon {
  width: 24px;
  height: 24px;
  color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  margin-right: 12px;
  vertical-align: middle;
  filter: drop-shadow(0 0 4px rgba(255, 193, 7, 0.3)); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.block-icon {
  width: 20px;
  height: 20px;
  color: #FFA500; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  margin-right: 8px;
  vertical-align: middle;
  filter: drop-shadow(0 0 2px rgba(255, 165, 0, 0.3));
}

.refresh-icon {
  width: 16px;
  height: 16px;
  color: #FFA500; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 2px rgba(255, 165, 0, 0.3));
}

.copy-icon {
  width: 16px;
  height: 16px;
  color: #DAA520; /* ÃƒÂÃ¢â‚¬â€ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾-ÃƒÂÃ‚Â¶ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â */
  filter: drop-shadow(0 0 2px rgba(218, 165, 32, 0.3));
}

/* Button icon styles */
.button-icon {
  width: 18px !important;
  height: 18px !important;
  margin-right: 8px;
  vertical-align: middle;
  flex-shrink: 0;
  display: inline-block;
}

.action-button .button-icon {
  width: 16px !important;
  height: 16px !important;
  margin-right: 6px;
  flex-shrink: 0;
}

/* SVG ÃƒÂÃ‚Â¸ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â² ÃƒÂÃ‚ÂºÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚ÂºÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Â¦ */
.action-button svg {
  width: 16px !important;
  height: 16px !important;
  margin-right: 6px;
  flex-shrink: 0;
}

/* ÃƒÂÃ…Â¾ÃƒÂÃ‚Â±Ãƒâ€˜Ã¢â‚¬Â°ÃƒÂÃ‚Â¸ÃƒÂÃ‚Âµ Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â SVG */
svg {
  max-width: 100%;
  height: auto;
}

/* ÃƒÂÃ‹Å“ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â² ÃƒÂÃ‚Â½ÃƒÂÃ‚Â°ÃƒÂÃ‚Â²ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â³ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¸ */
.nav-button svg {
  width: 20px !important;
  height: 20px !important;
}

/* Icon animations */
.refresh-history-btn:hover .refresh-icon,
.action-button:hover .refresh-icon {
  transform: rotate(180deg);
}

/* Section and block title icons */
h2 .section-icon,
h3 .block-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}

h2 {
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  display: flex;
  align-items: center;
}

/* Currency icon styles */
.currency-icon {
  width: 20px;
  height: 20px;
  color: #FFD700;
  margin-right: 8px;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

/* Status icon styles */
.status-icon svg {
  width: 20px;
  height: 20px;
  color: #FFA500;
  filter: drop-shadow(0 0 3px rgba(255, 165, 0, 0.3));
}

.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-balance:hover {
  transform: scale(1.05);
}

/* --- RESPONSIVE DESIGN --- */

/* === АДАПТИВНЫЕ СТИЛИ ДЛЯ ВАЛЮТНОЙ КАРТОЧКИ === */
@media (max-width: 768px) {
  .currency-info-card {
    padding: 20px 16px;
    border-radius: 16px;
    margin: 0 8px;
  }

  .currency-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .currency-full-name {
    font-size: 18px;
    text-align: center;
    width: 100%;
  }

  .currency-badge {
    align-self: center;
    margin-top: 4px;
  }

  .currency-requirements {
    gap: 12px;
  }

  .requirement-section {
    gap: 6px;
  }

  .requirement-section:not(:last-child) {
    padding-bottom: 10px;
  }

  .requirement-item {
    min-height: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .requirement-label {
    font-size: 13px;
    width: 100%;
    text-align: left;
  }

  .requirement-value {
    font-size: 13px;
    text-align: left;
    margin-left: 0;
    width: 100%;
  }

  .requirement-item.minimum-info,
  .requirement-item.missing-info {
    align-items: center;
    text-align: center;
  }

  .requirement-item.minimum-info .requirement-label,
  .requirement-item.missing-info .requirement-label {
    text-align: center;
    width: 100%;
  }

  .requirement-item.minimum-info .requirement-value,
  .requirement-item.missing-info .requirement-value {
    text-align: center;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .currency-info-card {
    padding: 16px 12px;
    border-radius: 12px;
    margin: 0 4px;
  }

  .currency-title {
    gap: 8px;
    margin-bottom: 12px;
  }

  .currency-full-name {
    font-size: 16px;
  }

  .currency-badge {
    font-size: 10px;
    padding: 4px 8px;
  }

  .currency-requirements {
    gap: 10px;
  }

  .requirement-section {
    gap: 4px;
  }

  .requirement-section:not(:last-child) {
    padding-bottom: 8px;
  }

  .requirement-label {
    font-size: 12px;
  }

  .requirement-value {
    font-size: 12px;
  }

  .warning-section {
    padding: 12px;
    border-radius: 8px;
  }

  .warning-section::before {
    font-size: 14px;
    top: -6px;
    left: 10px;
    padding: 0 4px;
  }

  .result-section {
    padding: 10px;
    border-radius: 6px;
  }

  .currency-tabs-header {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 6px;
    gap: 4px;
  }

  .currency-tab {
    min-width: 80px;
    flex-shrink: 1;
    padding: 14px 8px;
  }

  .tab-icon {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .tab-name {
    font-size: 11px;
    letter-spacing: 0.3px;
  }

  .tab-symbol {
    font-size: 9px;
  }
}

/* ÃƒÂÃ¢â‚¬ÂÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã…â€™ ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã…â€™ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â¦ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² */
@media (max-width: 360px) {
  .currency-info-card {
    padding: 12px 8px;
    border-radius: 10px;
    margin: 0 2px;
  }

  .currency-title {
    gap: 6px;
    margin-bottom: 10px;
  }

  .currency-full-name {
    font-size: 14px;
  }

  .currency-badge {
    font-size: 9px;
    padding: 3px 6px;
  }

  .currency-requirements {
    gap: 8px;
  }

  .requirement-section {
    gap: 3px;
  }

  .requirement-section:not(:last-child) {
    padding-bottom: 6px;
  }

  .requirement-label {
    font-size: 11px;
  }

  .requirement-value {
    font-size: 11px;
  }

  .warning-section {
    padding: 10px;
    border-radius: 6px;
  }

  .warning-section::before {
    font-size: 12px;
    top: -5px;
    left: 8px;
    padding: 0 3px;
  }

  .result-section {
    padding: 8px;
    border-radius: 4px;
  }

  .currency-tab {
    min-width: 75px;
    padding: 12px 6px;
  }

  .tab-icon {
    font-size: 18px;
  }

  .tab-name {
    font-size: 10px;
  }

  .tab-symbol {
    font-size: 8px;
  }

  /* Адаптивные стили для action-status-card */
  .action-status-card {
    padding: 12px;
    margin-top: 12px;
    border-radius: 8px;
  }

  .status-icon svg {
    width: 16px;
    height: 16px;
  }

  .status-text {
    font-size: 12px;
    line-height: 1.3;
  }
}

/* === ДОПОЛНИТЕЛЬНЫЕ АДАПТИВНЫЕ СТИЛИ === */
@media (max-width: 768px) {
  .action-status-card {
    padding: 16px;
    margin-top: 16px;
    border-radius: 12px;
  }

  .status-icon svg {
    width: 20px;
    height: 20px;
  }

  .status-text {
    font-size: 14px;
    line-height: 1.4;
  }
}

@media (max-width: 480px) {
  .action-status-card {
    padding: 14px;
    margin-top: 14px;
    border-radius: 10px;
  }

  .status-icon svg {
    width: 18px;
    height: 18px;
  }

  .status-text {
    font-size: 13px;
    line-height: 1.3;
  }

  /* Адаптивные стили для валютных иконок */
  .currency-icon {
    width: 24px;
    height: 24px;
    font-size: 24px;
    margin-right: 8px;
  }
}

/* === АДАПТИВНЫЕ СТИЛИ ДЛЯ ВАЛЮТНЫХ ИКОНОК === */
@media (max-width: 768px) {
  .currency-icon {
    width: 28px;
    height: 28px;
    font-size: 28px;
    margin-right: 10px;
  }
}

@media (max-width: 480px) {
  .currency-icon {
    width: 26px;
    height: 26px;
    font-size: 26px;
    margin-right: 8px;
  }
}

/* === УЛУЧШЕНИЕ ОБЩЕГО ВИДА НА МОБИЛЬНЫХ === */
@media (max-width: 768px) {
  .currency-content {
    padding: 0 8px;
  }

  .currency-main-info {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .currency-content {
    padding: 0 4px;
  }

  .currency-main-info {
    margin-bottom: 12px;
  }
}

@media (max-width: 360px) {
  .currency-content {
    padding: 0 2px;
  }

  .currency-main-info {
    margin-bottom: 10px;
  }
}

.app-main h2,
.app-section h2 {
  font-size: 20px;
}

.action-button {
  padding: 14px 20px;
  font-size: 15px;
}

/* --- LEGACY COMPATIBILITY --- */
.icon {
  display: inline-block;
  width: var(--icon-width);
  height: var(--icon-height);
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: var(--sprite-total-width) var(--sprite-total-height);
}

/* Icon positions - keeping original sprite positions */
.icon-energy {
  background-position: 0 0;
}
.icon-money {
  background-position: calc(var(--icon-width) * -1) 0;
}
.icon-ruble {
  background-position: calc(var(--icon-width) * -2) 0;
}
.icon-link {
  background-position: 0 calc(var(--icon-height) * -1);
}
.icon-play {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -1);
}
.icon-video-camera {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -1);
}
.icon-home {
  background-position: 0 calc(var(--icon-height) * -2);
}
.icon-dollar {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -2);
}
.icon-friends {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -2);
}

/* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¶ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â»ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Å¡ */
.withdrawal-date .text-muted {
  color: #666;
  font-size: 11px;
  margin-left: 8px;
}

.withdrawal-hash {
  color: #999;
  font-size: 11px;
  font-family: "Orbitron", monospace;
  margin-top: 5px;
}

.withdrawal-payout-id {
  color: var(--text-secondary);
  font-size: 11px;
  font-family: "Orbitron", monospace;
  opacity: 0.8;
  margin-top: 4px;
}

/* ÃƒÂÃ…Â¡ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚ÂºÃƒÂÃ‚Â° ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¶ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â²ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â */
.track-button {
  background: linear-gradient(45deg, #FFD54F, #FFC107);
  border: none;
  color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¾ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â° */
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
  text-transform: uppercase;
  font-family: "Inter", sans-serif;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.track-button:hover {
  background: linear-gradient(45deg, #FFEB3B, #FFD54F);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.track-button:active {
  transform: scale(0.95);
}

/* ÃƒÂÃ‚ÂÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â±ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã†â€™Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â° */
.history-item.status-updated {
  animation: statusUpdate 1s ease-in-out;
}

@keyframes statusUpdate {
  0% {
    background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
    border-color: rgba(255, 193, 7, 0.3);
  }
  50% {
    background: linear-gradient(145deg, #FFEB3B, #FFD54F);
    border-color: #FFC107;
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
  }
  100% {
    background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
    border-color: rgba(255, 193, 7, 0.3);
  }
}

/* --- WITHDRAWAL RECOMMENDATIONS STYLES --- */
.withdrawal-recommendations {
  margin: 15px 0;
  padding: 15px;
  background: linear-gradient(145deg, #FFFFFF, #FFF8E1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
}

.recommendations-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.help-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  width: 24px;
  height: 24px;
  position: relative;
}

.help-icon:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.1);
  color: var(--text-primary);
}

.help-icon svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}

/* Tooltip positioned above the help icon */
.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  z-index: 99999 !important; /* Максимальный z-index для отображения поверх всех элементов */
  background: var(--card-bg);
  border: 1px solid var(--border-medium);
  border-radius: 12px;
  padding: 16px;
  width: 280px;
  max-width: calc(100vw - 40px);
  box-shadow: 0 8px 24px var(--shadow-strong);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: normal;
  text-align: left;
  /* Умное позиционирование - не выходим за границы экрана */
  right: auto;
}

/* Tooltip arrow */
.tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  border: 6px solid transparent;
  border-top-color: var(--border-medium);
  z-index: 99999 !important;
}

.tooltip::before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -7px;
  border: 7px solid transparent;
  border-top-color: var(--card-bg);
  z-index: 99998 !important;
}

.tooltip p {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* Show tooltip on hover and when active */
.help-icon:hover .tooltip,
.tooltip.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
  pointer-events: auto;
}

/* Responsive positioning */
@media (max-width: 480px) {
  .tooltip {
    width: 280px;
    max-width: 95vw;
    font-size: 12px;
    padding: 12px;
    /* ÃƒÂÃ…Â¸Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚ÂµÃƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼, ÃƒÂÃ‚Â½ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹Ãƒâ€˜Ã¢â‚¬Â¦ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â´Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒÂÃ‚Â°ÃƒÂÃ‚Â·ÃƒÂÃ‚ÂºÃƒÂÃ‚Â° ÃƒÂÃ‚Â·ÃƒÂÃ‚Â° ÃƒÂÃ‚Â³Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â Ãƒâ€˜Ã¢â‚¬Â¹ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° */
    left: 50%;
    transform: translateX(-50%);
  }

  /* ÃƒÂÃ¢â‚¬Â¢Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â´Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒÂÃ‚Â°ÃƒÂÃ‚Â·ÃƒÂÃ‚ÂºÃƒÂÃ‚Â° ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹Ãƒâ€˜Ã¢â‚¬Â¦ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â´ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ ÃƒÂÃ‚Â·ÃƒÂÃ‚Â° ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ ÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â¹ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â° */
  .tooltip {
    right: auto;
  }

  /* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂºÃƒÂÃ‚Â° ÃƒÂÃ‚Â²Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂµÃƒÂÃ‚Â³ÃƒÂÃ‚Â´ÃƒÂÃ‚Â° ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ Ãƒâ€˜Ã¢â‚¬Â ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Å¡Ãƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã†â€™ ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ ÃƒÂÃ‚ÂºÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ */
  .tooltip::after {
    left: 50%;
    margin-left: -6px;
  }

  .tooltip::before {
    left: 50%;
    margin-left: -7px;
  }
}

/* Animation for smooth appearance */
@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
}

.tooltip.show {
  animation: tooltipSlideIn 0.3s ease-out;
}
p.calculator-subtitle {
    width: 100%;
    text-align: center;
}
.calculator-header {
    flex-direction: column;
    justify-content: center;
}
/* === WITHDRAWAL HISTORY STYLES === */
.withdrawal-history {
    margin-top: 15px;
}

.history-container {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px var(--shadow-light);
}

/* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Âº ÃƒÂÃ‚Â²Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¿ÃƒÂÃ‚Â»ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Å¡ */
.withdrawal-item {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.withdrawal-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #FFA500, #FFC107, #FFD700);
}

.withdrawal-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-medium);
    border-color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.withdrawal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.withdrawal-amount {
    font-size: 18px;
    font-weight: bold;
    color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    font-family: "Orbitron", monospace;
    text-shadow: 0 0 8px rgba(255, 193, 7, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.withdrawal-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed, .status-finished {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(102, 187, 106, 0.1));
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.4);
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.2);
    font-weight: 600;
}

.status-pending, .status-processing {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 165, 0, 0.1)); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    color: #ffaa00;
    border: 1px solid rgba(255, 170, 0, 0.4);
    box-shadow: 0 0 8px rgba(255, 170, 0, 0.2);
    font-weight: 600;
}

.status-failed, .status-rejected {
    background: linear-gradient(135deg, rgba(255, 68, 68, 0.2), rgba(200, 50, 50, 0.1));
    color: #ff4444;
    border: 1px solid rgba(255, 68, 68, 0.4);
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.2);
    font-weight: 600;
}

.withdrawal-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

.withdrawal-currency {
    font-weight: bold;
    color: #FFD700;
    text-transform: uppercase;
    text-shadow: 0 0 6px rgba(255, 215, 0, 0.3);
}

.withdrawal-date {
    color: #FFD54F; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    font-size: 12px;
    opacity: 0.8;
}

.withdrawal-address {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    word-break: break-all;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 165, 0, 0.05)); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    border: 1px solid rgba(255, 193, 7, 0.2); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    padding: 6px 8px;
    border-radius: 6px;
    margin-top: 8px;
}

.withdrawal-success-info, .withdrawal-error-info, .withdrawal-pending-info {
    margin-top: 8px;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.withdrawal-success-info {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.withdrawal-error-info {
    background: rgba(255, 68, 68, 0.1);
    color: #ff4444;
    border: 1px solid rgba(255, 68, 68, 0.2);
}

.withdrawal-pending-info {
    background: rgba(255, 170, 0, 0.1);
    color: #ffaa00;
    border: 1px solid rgba(255, 170, 0, 0.2);
}

.withdrawal-blockchain-link a {
    color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    text-decoration: none;
    font-size: 11px;
}

.withdrawal-blockchain-link a:hover {
    color: var(--accent-primary);
    text-decoration: underline;
}

.cancel-button {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.cancel-button:hover {
    background: linear-gradient(135deg, #ff6666, #ff0000);
    transform: scale(1.05);
}

/* ÃƒÂÃ‚ÂÃƒÂÃ‚Â´ÃƒÂÃ‚Â°ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â²ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Âµ Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã…â€™ ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã…â€™ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â¦ Ãƒâ€˜Ã‚ÂÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â² */
@media (max-width: 360px) {
    .withdrawal-item {
        padding: 8px;
        font-size: 12px;
    }

    .withdrawal-amount {
        font-size: 14px;
    }

    .withdrawal-status {
        padding: 3px 6px;
        font-size: 10px;
    }

    .withdrawal-address {
        font-size: 9px;
        padding: 3px 4px;
    }

    .withdrawal-details {
        font-size: 11px;
    }
}

/* ÃƒÂÃ‚Â¡Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚ÂºÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¿ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â±ÃƒÂÃ‚Â½ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¸ */
.refresh-history-btn {
    background: linear-gradient(145deg, #FFD54F, #FFC107);
    color: #1A1A1A; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
    border: none;
    padding: 12px 20px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: "Inter", sans-serif;
    box-shadow:
        0 6px 12px rgba(255, 193, 7, 0.4),
        0 3px 6px rgba(255, 193, 7, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(255, 165, 0, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.refresh-history-btn:hover {
    transform: translateY(-2px);
    background: linear-gradient(145deg, #FFEB3B, #FFD54F);
    box-shadow:
        0 8px 16px rgba(255, 193, 7, 0.5),
        0 4px 8px rgba(255, 193, 7, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.5),
        inset 0 -2px 0 rgba(255, 165, 0, 0.4); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

.refresh-history-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px var(--shadow-light);
}

/* ÃƒÂÃ‚Â£ÃƒÂÃ‚Â»Ãƒâ€˜Ã†â€™Ãƒâ€˜Ã¢â‚¬Â¡Ãƒâ€˜Ã‹â€ ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½ÃƒÂÃ‚Â½ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¿Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¾ÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã†â€™Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚ÂºÃƒÂÃ‚Â° ÃƒÂÃ‚Â´ÃƒÂÃ‚Â»Ãƒâ€˜Ã‚Â ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚ÂÃƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¸ÃƒÂÃ‚Â¸ */
.history-list {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 193, 7, 0.3) transparent;
}

.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #FFD54F, #FFC107);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #FFEB3B, #FFD54F);
}

/* ÃƒÂÃ¢â‚¬â€ÃƒÂÃ‚Â°ÃƒÂÃ‚Â³ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â²ÃƒÂÃ‚ÂºÃƒÂÃ‚Â¸ Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â°ÃƒÂÃ‚Â±ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¸Ãƒâ€˜Ã¢â‚¬Â Ãƒâ€˜Ã¢â‚¬Â¹ ÃƒÂÃ‚Â±ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™Ãƒâ€˜Ã‹â€ ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â½ÃƒÂÃ‚Âµ ÃƒÂÃ‚Â½Ãƒâ€˜Ã†â€™ÃƒÂÃ‚Â¶ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ - ÃƒÂÃ‚Â¸Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â¿ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»Ãƒâ€˜Ã…â€™ÃƒÂÃ‚Â·Ãƒâ€˜Ã†â€™ÃƒÂÃ‚ÂµÃƒÂÃ‚Â¼ ÃƒÂÃ‚ÂºÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â€šÂ¬Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Â¡ÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ Ãƒâ€˜Ã¢â‚¬Å¾ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â¼ÃƒÂÃ‚Â°Ãƒâ€˜Ã¢â‚¬Å¡ */

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    align-items: center;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item > span {
    flex: 1;
    text-align: center;
    font-size: 14px;
}

.status-pending {
    color: #FFD700; /* ÃƒÂÃ‚Â·ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â»ÃƒÂÃ‚Â¾Ãƒâ€˜Ã¢â‚¬Å¡ÃƒÂÃ‚Â¾ÃƒÂÃ‚Â¹ */
}

.status-confirmed {
    color: #4CAF50; /* ÃƒÂÃ‚Â·ÃƒÂÃ‚ÂµÃƒÂÃ‚Â»ÃƒÂÃ‚ÂµÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ */
}

.status-failed {
    color: #FF0000; /* ÃƒÂÃ‚ÂºÃƒâ€˜Ã¢â€šÂ¬ÃƒÂÃ‚Â°Ãƒâ€˜Ã‚ÂÃƒÂÃ‚Â½Ãƒâ€˜Ã¢â‚¬Â¹ÃƒÂÃ‚Â¹ */
}

.history-placeholder {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

/* === ÃƒÂÃ¢â‚¬ÂÃƒÂÃ…Â¾ÃƒÂÃ…Â¸ÃƒÂÃ…Â¾ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ‚ÂÃƒÂÃ‹Å“ÃƒÂÃ‚Â¢ÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ‚Â¬ÃƒÂÃ‚ÂÃƒÂÃ‚ÂÃƒÂÃ‚Â¯ ÃƒÂÃ‹Å“ÃƒÂÃ‚ÂÃƒÂÃ‚Â¤ÃƒÂÃ…Â¾ÃƒÂÃ‚Â ÃƒÂÃ…â€œÃƒÂÃ‚ÂÃƒÂÃ‚Â¦ÃƒÂÃ‹Å“ÃƒÂÃ‚Â¯ ÃƒÂÃ…Â¾ ÃƒÂÃ¢â‚¬â„¢ÃƒÂÃ‚Â«ÃƒÂÃ…Â¸ÃƒÂÃ¢â‚¬ÂºÃƒÂÃ‚ÂÃƒÂÃ‚Â¢ÃƒÂÃ‚ÂÃƒÂÃ‚Â¥ === */
.withdrawal-success-info {
  color: #4CAF50;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.withdrawal-error-info {
  color: #ff4444;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(255, 68, 68, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ff4444;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: 500;
}

.withdrawal-pending-info {
  color: #ffaa00;
  font-size: 12px;
  margin-top: 8px;
  padding: 6px 10px;
  background: rgba(255, 170, 0, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ffaa00;
  font-family: "Inter", sans-serif; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  font-weight: 500;
}

.withdrawal-blockchain-link {
  margin-top: 6px;
}

.withdrawal-blockchain-link a {
  color: #FFC107; /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  text-decoration: none;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.1); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border-radius: 3px;
  border: 1px solid rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  transition: all 0.3s ease;
  font-family: "Inter", sans-serif;
  font-weight: 500;
}

.withdrawal-blockchain-link a:hover {
  background: rgba(255, 193, 7, 0.2); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  border-color: rgba(255, 193, 7, 0.5); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3); /* ÃƒÂÃ‹Å“ÃƒÂÃ¢â‚¬â€ÃƒÂÃ…â€œÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ¢â‚¬Â¢ÃƒÂÃ‚ÂÃƒÂÃ…Â¾ */
}

div:nth-child(3) > div.amount-input-section > div.input-group > span {
    display: none;
}

input#calc-amount {
    padding: 18px 9px 18px 10px;
}

.amount-input-section {
    padding: 24px 18px;
}
#balance-amount {
  color: var(--primary-bg);
}

/* === ИСПРАВЛЕНИЕ TOOLTIP - НЕ ВЫХОДИТ ЗА ПРЕДЕЛЫ ЭКРАНА === */
/* Переопределяем позиционирование tooltip для мобильных устройств */
@media (max-width: 480px) {
  .tooltip {
    width: 260px !important;
    max-width: calc(100vw - 20px) !important;
    left: auto !important;
    right: 0 !important;
    transform: none !important;
    z-index: 99999 !important; /* Максимальный z-index и для мобильных */
  }

  /* Если кнопка помощи в начале строки, показываем tooltip слева */
  .recommendations-title .help-icon:first-child .tooltip {
    left: 0 !important;
    right: auto !important;
  }

  /* Стрелочка tooltip всегда по центру относительно кнопки */
  .tooltip::after {
    left: auto !important;
    right: 12px !important;
    margin-left: 0 !important;
    margin-right: -6px !important;
  }

  .tooltip::before {
    left: auto !important;
    right: 12px !important;
    margin-left: 0 !important;
    margin-right: -7px !important;
  }

  /* Для кнопок слева стрелочка слева */
  .recommendations-title .help-icon:first-child .tooltip::after {
    left: 12px !important;
    right: auto !important;
    margin-left: -6px !important;
    margin-right: 0 !important;
  }

  .recommendations-title .help-icon:first-child .tooltip::before {
    left: 12px !important;
    right: auto !important;
    margin-left: -7px !important;
    margin-right: 0 !important;
  }
}

/* Дополнительная защита для очень узких экранов */
@media (max-width: 320px) {
  .tooltip {
    width: 240px !important;
    max-width: calc(100vw - 16px) !important;
    left: 8px !important;
    right: 8px !important;
    transform: none !important;
    z-index: 99999 !important; /* Максимальный z-index для всех экранов */
  }

  .tooltip::after,
  .tooltip::before {
    left: 50% !important;
    right: auto !important;
    margin-left: -6px !important;
    margin-right: 0 !important;
  }

  .tooltip::before {
    margin-left: -7px !important;
  }
}

/* === КРУГЛЫЕ БЕЛЫЕ ИКОНКИ С НАГРАДАМИ === */
/* 🛡️ ЗАЩИЩЕННЫЕ СТИЛИ - НЕ УДАЛЯТЬ! Эти иконки показывают награды за рекламу */
.reward-badge {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  background: transparent !important;
  border: 2px solid #ffffff !important;
  color: #ffffff !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  font-family: "Orbitron", monospace !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: none !important;
  z-index: 2 !important;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.5) !important;
  box-shadow:
    0 0 8px rgba(255, 255, 255, 0.3),
    inset 0 0 8px rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  /* Дополнительная защита от скрытия */
  visibility: visible !important;
  opacity: 1 !important;
}

/* Анимация при наведении на кнопку */
.action-button:hover .reward-badge {
  box-shadow:
    0 0 12px rgba(255, 255, 255, 0.5),
    inset 0 0 12px rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-50%) scale(1.05) !important;
}

/* === ИСПРАВЛЕНИЕ ИКОНОК В КНОПКАХ ДРУЗЕЙ === */
/* Принудительное исправление цвета иконок в кнопках друзей */
#refresh-stats-button svg,
#share-app-button svg,
.refresh-history-btn svg,
#copy-referral-button svg {
  color: currentColor !important;
  stroke: currentColor !important;
  fill: none !important;
}

#refresh-stats-button svg path,
#share-app-button svg path,
.refresh-history-btn svg path,
#copy-referral-button svg path,
#copy-referral-button svg rect {
  stroke: currentColor !important;
  fill: none !important;
}

/* === ЦЕНТРИРОВАНИЕ ТЕКСТА В РЕКЛАМНЫХ КНОПКАХ === */
/* Специальные стили для правильного центрирования текста с иконками */
.action-button .button-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  margin: 0 !important;
  position: relative !important;
}

.action-button .button-text {
  font-size: 17px !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 0 0 6px 0 !important;
  color: rgba(0, 0, 0, 0.9) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

/* Декоративная разделительная линия */
.action-button .button-content::after {
  content: "" !important;
  position: absolute !important;
  bottom: 18px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 60px !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.3), transparent) !important;
  opacity: 0.6 !important;
}

.action-button .ad-counter {
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-top: 8px !important;
  line-height: 1.1 !important;
  color: rgba(0, 0, 0, 0.75) !important;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
  letter-spacing: 0.3px !important;
  /* Плавное появление после инициализации */
  opacity: 1 !important;
  animation: fadeInCounter 0.5s ease-in-out !important;
}

/* Анимация появления счетчика */
@keyframes fadeInCounter {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-button .ad-counter.limit-reached {
  color: rgba(150, 30, 30, 0.9) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(255, 100, 100, 0.3) !important;
}

/* Иконка слева остается на месте */
.action-button .button-icon {
  position: absolute !important;
  left: 28px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* Контейнер кнопки для правильного позиционирования */
.action-button {
  position: relative !important;
  justify-content: center !important;
}

/* === ТАБЫ ДЛЯ ПЕРЕКЛЮЧЕНИЯ РАЗДЕЛОВ === */
.withdrawal-tabs {
  display: flex !important;
  gap: 12px !important;
  margin: 20px 0 !important;
  padding: 0 !important;
}

.withdrawal-tab {
  flex: 1 !important;
  padding: 14px 20px !important;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a) !important;
  border: 1px solid rgba(255, 193, 7, 0.5) !important;
  border-radius: 12px !important;
  color: #ffffff !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  font-family: "Inter", sans-serif !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 10px rgba(255, 193, 7, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
  /* Стильный пробегающий блик и мерцание */
  animation: shimmer 4s ease-in-out infinite !important;
}

/* Пробегающий блик для привлечения внимания */
.withdrawal-tab::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 193, 7, 0.2) 25%,
    rgba(255, 215, 0, 0.6) 50%,
    rgba(255, 193, 7, 0.2) 75%,
    transparent 100%
  ) !important;
  animation: shine 3s ease-in-out infinite !important;
  z-index: 1 !important;
  border-radius: 12px !important;
  pointer-events: none !important;
}

.withdrawal-tab span,
.withdrawal-tab svg {
  position: relative !important;
  z-index: 2 !important;
}

/* Дополнительный блик для большей заметности */
.withdrawal-tab::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 50% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  ) !important;
  animation: shine-secondary 5s ease-in-out infinite !important;
  z-index: 1 !important;
  border-radius: 12px !important;
  pointer-events: none !important;
  animation-delay: 1s !important;
}

.withdrawal-tab:hover {
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a) !important;
  border-color: rgba(255, 193, 7, 0.6) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(255, 193, 7, 0.3) !important;
}

.withdrawal-tab:hover::before {
  animation-duration: 1.5s !important;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 193, 7, 0.4) 25%,
    rgba(255, 215, 0, 0.8) 50%,
    rgba(255, 193, 7, 0.4) 75%,
    transparent 100%
  ) !important;
}

.withdrawal-tab.active {
  background: linear-gradient(145deg, #FFD54F, #FFC107) !important;
  border-color: #FFC107 !important;
  color: #1a1a1a !important;
  box-shadow:
    0 4px 16px rgba(255, 193, 7, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    0 0 24px rgba(255, 193, 7, 0.4) !important;
  transform: translateY(-2px) !important;
  animation: none !important;
}

.withdrawal-tab.active::before,
.withdrawal-tab.active::after {
  display: none !important;
}

.withdrawal-tab.active svg {
  color: #1a1a1a !important;
  stroke: #1a1a1a !important;
  fill: #1a1a1a !important;
}

/* === СКРЫТИЕ И ПОКАЗ РАЗДЕЛОВ === */
.calculator-section,
.withdrawal-section {
  transition: all 0.4s ease !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
  max-height: none !important;
  overflow: visible !important;
}

.calculator-section.hidden,
.withdrawal-section.hidden {
  display: none !important;
  opacity: 0 !important;
  transform: translateY(-20px) !important;
  max-height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* === АНИМАЦИИ === */
@keyframes pulse {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
  50% { transform: scale(1.02); box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* === ЗЕЛЁНАЯ КНОПКА "ДОСТУПНО ДЛЯ ВЫВОДА" === */
.withdrawal-button {
  background: linear-gradient(145deg, #00ff88, #00cc6a) !important;
  color: #1a1a1a !important;
  border: none !important;
  padding: 12px 20px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow:
    0 4px 12px rgba(0, 255, 136, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
  width: 100% !important;
  display: block !important;
}

.withdrawal-button:hover {
  background: linear-gradient(145deg, #00cc6a, #00aa55) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 16px rgba(0, 255, 136, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.withdrawal-button:active {
  transform: translateY(0) !important;
  box-shadow:
    0 2px 8px rgba(0, 255, 136, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Стильный пробегающий блик */
@keyframes shine {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* Мерцание границы и свечения */
@keyframes shimmer {
  0%, 100% {
    border-color: rgba(255, 193, 7, 0.5);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 10px rgba(255, 193, 7, 0.2);
  }
  50% {
    border-color: rgba(255, 193, 7, 0.8);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 20px rgba(255, 193, 7, 0.4);
  }
}

/* Вторичный блик */
@keyframes shine-secondary {
  0% {
    left: -50%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  60% {
    left: 100%;
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* === УМНАЯ ССЫЛКА В КАЛЬКУЛЯТОРЕ === */
.status-text.smart-link {
  cursor: pointer !important;
  color: #4CAF50 !important;
  text-decoration: underline !important;
  transition: all 0.3s ease !important;
}

.status-text.smart-link:hover {
  color: #66BB6A !important;
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.5) !important;
}
h3 {
  z-index: 0;
}
#earn-section > div:nth-child(2) > p.hint {
  display: none;
}

/* === CSS ПЕРЕМЕННЫЕ ДЛЯ ВАЛИДАЦИИ === */
:root {
  --cyber-success-color: #4caf50;
  --cyber-success-bg: rgba(76, 175, 80, 0.1);
  --cyber-error-color: #ff6b6b;
  --cyber-error-bg: rgba(255, 107, 107, 0.1);
}

/* === СТИЛИ ДЛЯ СООБЩЕНИЙ ВАЛИДАЦИИ === */
.success-message {
  color: var(--cyber-success-color, #4caf50) !important;
  background-color: var(--cyber-success-bg, rgba(76, 175, 80, 0.1)) !important;
  border: 1px solid var(--cyber-success-color, #4caf50) !important;
  font-weight: bold !important;
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.3) !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
}

/* ИСПРАВЛЕНИЕ: Красивая кнопка перехода с правильными отступами */
.action-status-card {
    margin: 30px 0 !important;
    padding: 20px 24px !important;
    border-radius: 16px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    min-height: 64px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 12px !important;
    position: relative !important;
    overflow: hidden !important;
}

.action-status-card .status-icon {
    width: 24px !important;
    height: 24px !important;
    flex-shrink: 0 !important;
}

.action-status-card .status-icon svg {
    width: 100% !important;
    height: 100% !important;
}

.action-status-card .status-text {
    flex: 1 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
}

.action-status-card.available {
    background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%) !important;
    color: #1a1a1a !important;
    border-color: #00ff88 !important;
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3) !important;
    cursor: pointer !important;
}

.action-status-card.available:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 35px rgba(0, 255, 136, 0.4) !important;
    background: linear-gradient(135deg, #00cc6a 0%, #00aa55 100%) !important;
}

.action-status-card.available:active {
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.3) !important;
}

.action-status-card.neutral {
    background: rgba(102, 102, 102, 0.1) !important;
    color: #666 !important;
    border-color: #444 !important;
}

.action-status-card.insufficient {
    background: rgba(255, 107, 107, 0.1) !important;
    color: #ff6b6b !important;
    border-color: #ff6b6b !important;
}

.action-status-card.warning {
    background: rgba(255, 167, 38, 0.1) !important;
    color: #ffa726 !important;
    border-color: #ffa726 !important;
}

/* Стили для валидации формы выплат */
.validation-card {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid #ff6b6b;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
    color: #ff6b6b;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
}

.validation-card.valid {
    background: rgba(0, 255, 136, 0.1);
    border-color: #00ff88;
    color: #00ff88;
}

.validation-card.invalid {
    background: rgba(255, 107, 107, 0.1);
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.validation-card.warning {
    background: rgba(255, 167, 38, 0.1);
    border-color: #ffa726;
    color: #ffa726;
}

.validation-card.hidden {
    display: none !important;
}

/* 🔒 СТИЛИ ДЛЯ ЗАБЛОКИРОВАННОЙ ФОРМЫ ВЫПЛАТ */
.withdrawal-form.blocked {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.withdrawal-form.blocked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  z-index: 1;
  pointer-events: none;
}

.withdrawal-form.blocked input,
.withdrawal-form.blocked select,
.withdrawal-form.blocked button {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

/* Сообщение о блокировке */
.blocked-message {
  background: #fff3cd !important;
  color: #856404 !important;
  border: 1px solid #ffeaa7 !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin-top: 10px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

.blocked-message strong {
  color: #856404 !important;
  font-weight: 600 !important;
}

/* Стили для статусных сообщений */
.status-message.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}