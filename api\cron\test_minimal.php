<?php
echo "Начало скрипта\n";

// Логирование начала работы скрипта
$logFile = __DIR__ . '/notifications.log';
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

logMessage("=== ТЕСТ МИНИМАЛЬНОГО СКРИПТА ===");

try {
    // Подключение зависимостей
    require_once __DIR__ . '/../config.php';
    logMessage("config.php подключен");
    
    require_once __DIR__ . '/../functions.php';
    logMessage("functions.php подключен");
    
    // Проверяем наличие файла данных пользователей
    if (!defined('USER_DATA_FILE') || !file_exists(USER_DATA_FILE)) {
        logMessage("ОШИБКА: Файл данных пользователей не найден");
        exit(1);
    }
    
    logMessage("Файл данных пользователей найден: " . USER_DATA_FILE);
    
    // Загружаем данные пользователей из JSON
    $userData = loadUserData();
    if (!$userData || !is_array($userData)) {
        logMessage("ОШИБКА: Не удалось загрузить данные пользователей");
        exit(1);
    }
    
    logMessage("Загружено пользователей: " . count($userData));
    
    // === РОТАЦИЯ ЛОГОВ РЕКЛАМНОЙ СТАТИСТИКИ ===
    logMessage("=== НАЧАЛО РОТАЦИИ ЛОГОВ ===");
    
    // Запускаем ротацию логов
    $rotationScript = __DIR__ . '/log_rotation.php';
    
    if (file_exists($rotationScript)) {
        // Выполняем скрипт ротации
        ob_start();
        include $rotationScript;
        $rotationResult = ob_get_clean();
        
        // Парсим результат
        $result = json_decode($rotationResult, true);
        
        if ($result && $result['success']) {
            logMessage("Ротация логов выполнена успешно:");
            logMessage("- Ротация по размеру: " . ($result['rotated_by_size'] ? 'Да' : 'Нет'));
            logMessage("- Ротация по дате: " . ($result['rotated_by_date'] ? 'Да' : 'Нет'));
            logMessage("- Удалено архивов: " . $result['deleted_archives']);
            logMessage("- Всего архивов: " . $result['archive_stats']['count']);
            logMessage("- Размер архивов: " . round($result['archive_stats']['total_size'] / 1024 / 1024, 2) . " МБ");
        } else {
            $error = $result ? $result['error'] : 'Неизвестная ошибка';
            logMessage("ОШИБКА ротации логов: " . $error);
        }
    } else {
        logMessage("ПРЕДУПРЕЖДЕНИЕ: Скрипт ротации логов не найден: " . $rotationScript);
    }
    
    logMessage("=== ЗАВЕРШЕНИЕ РОТАЦИИ ЛОГОВ ===");
    logMessage("=== ТЕСТ ЗАВЕРШЕН УСПЕШНО ===");
    
} catch (Exception $e) {
    logMessage("КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
