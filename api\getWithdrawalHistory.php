<?php
/**
 * api/getWithdrawalHistory.php
 * API эндпоинт для получения истории выплат пользователя.
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in getWithdrawalHistory.php'); 
    echo json_encode(['error'=>'Ошибка сервера: CFG']); 
    exit; 
}
if (!(@require_once __DIR__ . '/validate_initdata.php')) {
    http_response_code(500);
    error_log('FATAL: validate_initdata.php not found in getWithdrawalHistory.php');
    echo json_encode(['error'=>'Ошибка сервера: VID']);
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in getWithdrawalHistory.php'); 
    echo json_encode(['error'=>'Ошибка сервера: DBM']); 
    exit; 
}
// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
require_once __DIR__ . '/admin/auth.php';
session_start();

// ... (остальной код)

$isAdmin = isAuthenticated();

if ($isAdmin) {
    // Логика для администратора: возвращаем все выплаты
    $allWithdrawals = [];
    $userData = loadUserData();
    foreach ($userData as $userId => $user) {
        if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
            foreach ($user['withdrawals'] as $withdrawal) {
                $withdrawal['user_id'] = $userId; // Добавляем ID пользователя
                $allWithdrawals[] = $withdrawal;
            }
        }
    }

    // Сортируем все выплаты по времени
    usort($allWithdrawals, function($a, $b) {
        return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
    });

    echo json_encode(['withdrawals' => $allWithdrawals, 'total_count' => count($allWithdrawals)]);

} else {
    // Существующая логика для обычных пользователей
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Ошибка запроса: Нет данных']);
        exit;
    }

    $validatedData = validateTelegramInitData($input['initData']);
    if ($validatedData === false) {
        http_response_code(401);
        echo json_encode(['error' => 'Ошибка аутентификации']);
        exit;
    }

    $userId = intval($validatedData['user']['id']);
    $userData = loadUserData();

    if (!isset($userData[$userId])) {
        http_response_code(404);
        echo json_encode(['error' => 'Ошибка: Пользователь не найден']);
        exit;
    }

    $withdrawals = isset($userData[$userId]['withdrawals']) ? $userData[$userId]['withdrawals'] : [];

    // --- Автоматическое обновление статусов перед отправкой ---
    require_once __DIR__ . '/NOWPaymentsAPI.php';
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    $dataWasModified = false;

    foreach ($withdrawals as $index => &$withdrawal) {
        $currentStatus = strtolower($withdrawal['status'] ?? '');
        $payoutId = $withdrawal['payout_id'] ?? null;

        // Проверяем только те статусы, которые могут измениться
        if ($payoutId && in_array($currentStatus, ['waiting', 'processing', 'sending', 'pending'])) {
            try {
                $statusResponse = $api->getPayoutStatus($payoutId);
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = strtolower($statusResponse['status']);
                    if ($newStatus !== $currentStatus) {
                        $withdrawals[$index]['status'] = $newStatus;
                        $withdrawals[$index]['updated_at'] = date('Y-m-d H:i:s');
                        if (isset($statusResponse['hash'])) {
                            $withdrawals[$index]['transaction_hash'] = $statusResponse['hash'];
                        }
                        $dataWasModified = true;
                    }
                }
            } catch (Exception $e) {
                error_log("Error updating status for payout {$payoutId}: " . $e->getMessage());
            }
        }
    }

    if ($dataWasModified) {
        $userData[$userId]['withdrawals'] = $withdrawals;
        saveUserData($userData);
    }
    // --- Конец автоматического обновления статусов ---

    usort($withdrawals, function($a, $b) {
        return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
    });

    echo json_encode(['withdrawals' => $withdrawals, 'total_count' => count($withdrawals)]);
}
?>
