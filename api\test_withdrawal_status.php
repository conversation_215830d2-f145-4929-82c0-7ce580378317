<?php
/**
 * Тестирование системы получения и обновления статусов выплат
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔄 ТЕСТИРОВАНИЕ СИСТЕМЫ СТАТУСОВ ВЫПЛАТ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые данные выплат с различными статусами
$testWithdrawals = [
    [
        'id' => 'test_waiting_001',
        'payout_id' => 'test_waiting_001',
        'status' => 'waiting',
        'coins_amount' => 1000,
        'crypto_amount' => 0.0003,
        'currency' => 'eth',
        'address' => '******************************************',
        'created_at' => date('Y-m-d H:i:s', time() - 3600), // 1 час назад
        'updated_at' => date('Y-m-d H:i:s', time() - 3600)
    ],
    [
        'id' => 'test_processing_002',
        'payout_id' => 'test_processing_002', 
        'status' => 'processing',
        'coins_amount' => 2500,
        'crypto_amount' => 0.000025,
        'currency' => 'btc',
        'address' => '**********************************',
        'created_at' => date('Y-m-d H:i:s', time() - 1800), // 30 минут назад
        'updated_at' => date('Y-m-d H:i:s', time() - 900)   // 15 минут назад
    ],
    [
        'id' => 'test_sending_003',
        'payout_id' => 'test_sending_003',
        'status' => 'sending',
        'coins_amount' => 5000,
        'crypto_amount' => 1.5,
        'currency' => 'ton',
        'address' => 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t',
        'created_at' => date('Y-m-d H:i:s', time() - 600),  // 10 минут назад
        'updated_at' => date('Y-m-d H:i:s', time() - 300)   // 5 минут назад
    ],
    [
        'id' => 'test_finished_004',
        'payout_id' => 'test_finished_004',
        'status' => 'finished',
        'coins_amount' => 10000,
        'crypto_amount' => 8.5,
        'currency' => 'usdttrc20',
        'address' => 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7',
        'transaction_hash' => '******************************************',
        'created_at' => date('Y-m-d H:i:s', time() - 7200), // 2 часа назад
        'updated_at' => date('Y-m-d H:i:s', time() - 1800)  // 30 минут назад
    ],
    [
        'id' => 'test_failed_005',
        'payout_id' => 'test_failed_005',
        'status' => 'failed',
        'coins_amount' => 500,
        'crypto_amount' => 0.0001,
        'currency' => 'eth',
        'address' => '******************************************',
        'error_message' => 'Insufficient balance',
        'created_at' => date('Y-m-d H:i:s', time() - 5400), // 1.5 часа назад
        'updated_at' => date('Y-m-d H:i:s', time() - 3600)  // 1 час назад
    ]
];

echo "🎯 ТЕСТ 1: ПРОВЕРКА ФУНКЦИЙ ПОЛУЧЕНИЯ СТАТУСОВ\n";
echo str_repeat("-", 40) . "\n\n";

// Тестируем функцию получения статуса текста
echo "📝 Тестирование функции getStatusText():\n";
$statusTests = [
    'waiting' => 'Ожидание обработки',
    'processing' => 'Обрабатывается', 
    'sending' => 'Отправляется на кошелек',
    'finished' => 'Отправлено на кошелек',
    'completed' => 'Отправлено на кошелек',
    'confirmed' => 'Подтверждено в блокчейне',
    'failed' => 'Ошибка выплаты',
    'rejected' => 'Отклонено системой',
    'cancelled' => 'Отменено',
    'expired' => 'Истекло',
    'unknown_status' => 'unknown_status'
];

foreach ($statusTests as $status => $expectedText) {
    $actualText = getStatusText($status);
    $isCorrect = ($status === 'unknown_status') ? 
        ($actualText === $status) : 
        ($actualText === $expectedText);
    
    echo "   " . ($isCorrect ? "✅" : "❌") . " {$status} → {$actualText}\n";
}

echo "\n🎯 ТЕСТ 2: ИМИТАЦИЯ ПОЛУЧЕНИЯ СТАТУСОВ ИЗ API\n";
echo str_repeat("-", 40) . "\n\n";

foreach ($testWithdrawals as $i => $withdrawal) {
    echo "📊 Выплата " . ($i + 1) . ": {$withdrawal['id']}\n";
    echo "   💰 Сумма: {$withdrawal['crypto_amount']} {$withdrawal['currency']}\n";
    echo "   📊 Текущий статус: {$withdrawal['status']}\n";
    
    // Имитируем получение статуса из API
    $apiStatus = simulateAPIStatusCheck($withdrawal);
    
    echo "   🔄 Статус из API: {$apiStatus['status']}\n";
    echo "   📝 Описание: " . getStatusText($apiStatus['status']) . "\n";
    
    // Проверяем изменился ли статус
    if ($apiStatus['status'] !== $withdrawal['status']) {
        echo "   🔄 Статус изменился: {$withdrawal['status']} → {$apiStatus['status']}\n";
        
        // Имитируем обновление в базе данных
        $updateResult = simulateStatusUpdate($withdrawal['id'], $apiStatus);
        echo "   " . ($updateResult ? "✅" : "❌") . " Обновление в БД: " . 
             ($updateResult ? "успешно" : "ошибка") . "\n";
    } else {
        echo "   ℹ️ Статус не изменился\n";
    }
    
    // Дополнительная информация для завершенных выплат
    if (in_array($apiStatus['status'], ['finished', 'completed', 'confirmed'])) {
        if (isset($apiStatus['transaction_hash'])) {
            echo "   🔗 Hash транзакции: {$apiStatus['transaction_hash']}\n";
        }
        if (isset($apiStatus['block_height'])) {
            echo "   📦 Блок: {$apiStatus['block_height']}\n";
        }
    }
    
    // Информация об ошибках
    if (in_array($apiStatus['status'], ['failed', 'rejected'])) {
        if (isset($apiStatus['error_message'])) {
            echo "   ❌ Ошибка: {$apiStatus['error_message']}\n";
        }
    }
    
    echo "\n";
}

echo "🎯 ТЕСТ 3: ПРОВЕРКА ЛОГИКИ ОБНОВЛЕНИЯ СТАТУСОВ\n";
echo str_repeat("-", 40) . "\n\n";

// Тестируем различные сценарии обновления
$updateScenarios = [
    ['from' => 'waiting', 'to' => 'processing', 'valid' => true],
    ['from' => 'processing', 'to' => 'sending', 'valid' => true],
    ['from' => 'sending', 'to' => 'finished', 'valid' => true],
    ['from' => 'finished', 'to' => 'confirmed', 'valid' => true],
    ['from' => 'processing', 'to' => 'failed', 'valid' => true],
    ['from' => 'finished', 'to' => 'waiting', 'valid' => false], // Невозможный переход
    ['from' => 'confirmed', 'to' => 'processing', 'valid' => false], // Невозможный переход
];

echo "📝 Тестирование логики переходов статусов:\n";
foreach ($updateScenarios as $scenario) {
    $isValidTransition = isValidStatusTransition($scenario['from'], $scenario['to']);
    $expectedResult = $scenario['valid'];
    $testPassed = ($isValidTransition === $expectedResult);
    
    echo "   " . ($testPassed ? "✅" : "❌") . 
         " {$scenario['from']} → {$scenario['to']}: " . 
         ($isValidTransition ? "разрешен" : "запрещен") . "\n";
}

echo "\n🎯 ТЕСТ 4: ПРОИЗВОДИТЕЛЬНОСТЬ ОБНОВЛЕНИЯ СТАТУСОВ\n";
echo str_repeat("-", 40) . "\n\n";

$startTime = microtime(true);
$batchSize = 100;

echo "📊 Имитация обновления {$batchSize} выплат...\n";

for ($i = 0; $i < $batchSize; $i++) {
    $testWithdrawal = [
        'id' => 'batch_test_' . $i,
        'status' => 'waiting',
        'currency' => 'eth'
    ];
    
    $apiStatus = simulateAPIStatusCheck($testWithdrawal);
    simulateStatusUpdate($testWithdrawal['id'], $apiStatus);
}

$endTime = microtime(true);
$totalTime = $endTime - $startTime;
$avgTime = $totalTime / $batchSize;

echo "⏱️ Общее время: " . number_format($totalTime, 3) . " сек\n";
echo "📊 Среднее время на выплату: " . number_format($avgTime * 1000, 2) . " мс\n";
echo "🚀 Выплат в секунду: " . number_format($batchSize / $totalTime, 0) . "\n";

if ($avgTime < 0.01) {
    echo "✅ Производительность отличная\n";
} elseif ($avgTime < 0.05) {
    echo "✅ Производительность хорошая\n";
} else {
    echo "⚠️ Производительность требует оптимизации\n";
}

echo "\n🎉 ТЕСТИРОВАНИЕ СТАТУСОВ ЗАВЕРШЕНО!\n";

/**
 * Имитирует проверку статуса через API NOWPayments
 */
function simulateAPIStatusCheck($withdrawal) {
    // Имитируем различные сценарии изменения статусов
    $currentStatus = $withdrawal['status'];
    $currency = $withdrawal['currency'];
    
    // Логика прогрессии статусов
    $statusProgression = [
        'waiting' => ['processing', 'failed'],
        'processing' => ['sending', 'failed'],
        'sending' => ['finished', 'failed'],
        'finished' => ['confirmed'],
        'confirmed' => ['confirmed'], // Финальный статус
        'failed' => ['failed'],      // Финальный статус
        'rejected' => ['rejected'],  // Финальный статус
        'cancelled' => ['cancelled'] // Финальный статус
    ];
    
    $possibleStatuses = $statusProgression[$currentStatus] ?? [$currentStatus];
    
    // Случайно выбираем следующий статус (с весами)
    if (count($possibleStatuses) > 1) {
        // 70% шанс прогрессии, 30% шанс остаться на месте или ошибка
        $newStatus = (rand(1, 100) <= 70) ? $possibleStatuses[0] : $currentStatus;
    } else {
        $newStatus = $possibleStatuses[0];
    }
    
    $result = [
        'status' => $newStatus,
        'updated_at' => date('Y-m-d H:i:s'),
        'checked_at' => date('Y-m-d H:i:s')
    ];
    
    // Добавляем дополнительные данные для завершенных статусов
    if (in_array($newStatus, ['finished', 'completed', 'confirmed'])) {
        $result['transaction_hash'] = '0x' . bin2hex(random_bytes(32));
        $result['block_height'] = rand(18000000, 19000000);
        $result['confirmations'] = rand(1, 12);
    }
    
    // Добавляем сообщения об ошибках для неудачных статусов
    if (in_array($newStatus, ['failed', 'rejected'])) {
        $errorMessages = [
            'Insufficient balance',
            'Invalid address',
            'Network congestion',
            'Transaction timeout',
            'Rejected by network'
        ];
        $result['error_message'] = $errorMessages[array_rand($errorMessages)];
    }
    
    return $result;
}

/**
 * Имитирует обновление статуса в базе данных
 */
function simulateStatusUpdate($withdrawalId, $statusData) {
    // Имитируем успешное обновление (в реальности здесь был бы запрос к БД)
    $success = rand(1, 100) <= 95; // 95% успешных обновлений
    
    if ($success) {
        // Логируем обновление
        error_log("Status updated for withdrawal {$withdrawalId}: {$statusData['status']}");
    }
    
    return $success;
}

/**
 * Проверяет валидность перехода между статусами
 */
function isValidStatusTransition($fromStatus, $toStatus) {
    $validTransitions = [
        'waiting' => ['processing', 'failed', 'cancelled'],
        'processing' => ['sending', 'failed', 'cancelled'],
        'sending' => ['finished', 'completed', 'failed'],
        'finished' => ['confirmed'],
        'completed' => ['confirmed'],
        'confirmed' => [], // Финальный статус
        'failed' => [],    // Финальный статус
        'rejected' => [],  // Финальный статус
        'cancelled' => [], // Финальный статус
        'expired' => []    // Финальный статус
    ];
    
    return in_array($toStatus, $validTransitions[$fromStatus] ?? []);
}

/**
 * Получает текст статуса на русском языке
 */
function getStatusText($status) {
    $statusMap = [
        'waiting' => 'Ожидание обработки',
        'processing' => 'Обрабатывается',
        'sending' => 'Отправляется на кошелек',
        'finished' => 'Отправлено на кошелек',
        'completed' => 'Отправлено на кошелек',
        'confirmed' => 'Подтверждено в блокчейне',
        'failed' => 'Ошибка выплаты',
        'rejected' => 'Отклонено системой',
        'pending' => 'В обработке',
        'cancelled' => 'Отменено',
        'expired' => 'Истекло'
    ];
    return $statusMap[$status] ?? $status;
}
?>
