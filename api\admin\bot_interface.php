<?php
/**
 * api/admin/bot_interface.php
 * Страница настройки интерфейса Telegram бота
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🤖 Настройка интерфейса Telegram бота</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button onclick="setupAll()" class="btn btn-sm btn-primary me-2">
                            <i class="bi bi-gear"></i> Настроить всё
                        </button>
                    </div>
                </div>
            </div>

            <!-- Статус уведомления -->
            <div id="status-alert" class="alert alert-info d-none" role="alert">
                <div id="status-message"></div>
            </div>



            <!-- Полный редактор настроек бота -->
            <div id="bot-texts-editor" class="row mb-4">
                <div class="col-md-12">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-robot me-2"></i>
                                Полное управление текстами бота
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Переключатель языков -->
                            <div class="mb-4">
                                <div class="btn-group" role="group" aria-label="Language selector">
                                    <input type="radio" class="btn-check" name="bot-language" id="bot-lang-ru" value="ru" checked>
                                    <label class="btn btn-outline-primary" for="bot-lang-ru">🇷🇺 Русский</label>

                                    <input type="radio" class="btn-check" name="bot-language" id="bot-lang-en" value="en">
                                    <label class="btn btn-outline-primary" for="bot-lang-en">🇺🇸 English</label>
                                </div>
                                <small class="text-muted ms-3">Выберите язык для редактирования</small>
                            </div>

                            <!-- Навигация по разделам -->
                            <div class="mb-4">
                                <ul class="nav nav-pills" id="bot-sections-nav" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="interface-tab" data-bs-toggle="pill" data-bs-target="#interface-section" type="button" role="tab">
                                            🤖 Интерфейс бота
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="commands-tab" data-bs-toggle="pill" data-bs-target="#commands-section" type="button" role="tab">
                                            ⚡ Команды
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="welcome-tab" data-bs-toggle="pill" data-bs-target="#welcome-section" type="button" role="tab">
                                            👋 Приветствие
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="balance-tab" data-bs-toggle="pill" data-bs-target="#balance-section" type="button" role="tab">
                                            💰 Баланс
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="referrals-tab" data-bs-toggle="pill" data-bs-target="#referrals-section" type="button" role="tab">
                                            👥 Рефералы
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="statistics-tab" data-bs-toggle="pill" data-bs-target="#statistics-section" type="button" role="tab">
                                            📊 Статистика
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="help-tab" data-bs-toggle="pill" data-bs-target="#help-section" type="button" role="tab">
                                            ❓ Помощь
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="detailed-info-tab" data-bs-toggle="pill" data-bs-target="#detailed-info-section" type="button" role="tab">
                                            📖 Подробная информация
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="how-it-works-tab" data-bs-toggle="pill" data-bs-target="#how-it-works-section" type="button" role="tab">
                                            ❓ Как это работает
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="buttons-tab" data-bs-toggle="pill" data-bs-target="#buttons-section" type="button" role="tab">
                                            🔘 Кнопки
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications-section" type="button" role="tab">
                                            🔔 Уведомления
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="button-visibility-tab" data-bs-toggle="pill" data-bs-target="#button-visibility-section" type="button" role="tab">
                                            👁️ Видимость кнопок
                                        </button>
                                    </li>
                                </ul>
                            </div>

                            <!-- Контент разделов -->
                            <div class="tab-content" id="bot-sections-content">
                                <!-- Интерфейс бота -->
                                <div class="tab-pane fade show active" id="interface-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">🤖 Основные настройки интерфейса бота</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Текст Menu Button</label>
                                                <input type="text" class="form-control bot-text-input" data-key="bot_interface.menu_button_text" placeholder="🚀 Запустить">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Полное описание бота</label>
                                                <textarea class="form-control bot-text-input" data-key="bot_interface.description_full" rows="6" placeholder="Полное описание бота для настроек"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Короткое описание бота</label>
                                                <input type="text" class="form-control bot-text-input" data-key="bot_interface.description_short" placeholder="Короткое описание">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Команды -->
                                <div class="tab-pane fade" id="commands-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">⚡ Тексты команд бота</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                <strong>Команды бота</strong> - это команды, которые пользователи могут вводить через "/" или выбирать из меню команд.
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /start - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.start_description" placeholder="Запустить приложение">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /balance - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.balance_description" placeholder="Посмотреть баланс">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /stats - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.stats_description" placeholder="Статистика заработка">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /help - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.help_description" placeholder="Помощь и инструкции">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /friends - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.friends_description" placeholder="Реферальная программа">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Команда /info - Описание</label>
                                                <input type="text" class="form-control bot-text-input" data-key="commands.info_description" placeholder="Подробная информация">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Приветствие -->
                                <div class="tab-pane fade" id="welcome-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">👋 Тексты приветствия</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle me-2"></i>
                                                <strong>Приветствие</strong> - это первые сообщения, которые видит пользователь при запуске бота.
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок приветствия</label>
                                                <input type="text" class="form-control bot-text-input" data-key="welcome.title" placeholder="🎉 Добро пожаловать в UniQPaid!">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Подзаголовок</label>
                                                <input type="text" class="form-control bot-text-input" data-key="welcome.subtitle" placeholder="💰 Просто смотри рекламу сейчас и получай крипту сразу!">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Описание</label>
                                                <textarea class="form-control bot-text-input" data-key="welcome.description" rows="3" placeholder="🚀 Не нужно ждать листинга! Моментальные автовыплаты на кошелёк!"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Приветствие в главном меню</label>
                                                <input type="text" class="form-control bot-text-input" data-key="welcome.main_menu_hello" placeholder="👋 Привет, {firstName}!">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Выберите действие</label>
                                                <input type="text" class="form-control bot-text-input" data-key="welcome.choose_action" placeholder="Выберите действие:">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Сообщение о регистрации</label>
                                                <textarea class="form-control bot-text-input" data-key="welcome.registration_message" rows="4" placeholder="🎉 Поздравляем! Вы успешно зарегистрированы в UniQPaid!\n\n💰 Начните зарабатывать криптовалюту прямо сейчас!\n🚀 Нажмите кнопку ниже, чтобы открыть приложение."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Превью сообщение (полное описание бота)</label>
                                                <textarea class="form-control bot-text-input" data-key="bot_interface.description_full" rows="6" placeholder="🎉 UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!\n\n🚀 Моментальные выплаты на кошелёк\n👥 Реферальная программа 10%\n💎 Курс: 1000 монет = $1.00 USD\n\n📱 Нажмите кнопку ниже, чтобы запустить приложение!"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Короткое описание бота</label>
                                                <input type="text" class="form-control bot-text-input" data-key="bot_interface.description_short" placeholder="💰 Зарабатывайте криптовалюту за просмотр рекламы!">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Баланс -->
                                <div class="tab-pane fade" id="balance-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">💰 Тексты раздела баланса</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок раздела</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.title" placeholder="Заголовок баланса">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Текущий баланс</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.current_balance" placeholder="Текущий баланс: {balance} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Баланс в USD</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.balance_usd" placeholder="В долларах: ${amount} USD">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Всего заработано</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.total_earned" placeholder="Всего заработано: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">От рефералов</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.from_referrals" placeholder="От рефералов: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Количество выводов</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.withdrawals_count" placeholder="Выводов: {count}">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Призыв к выводу</label>
                                                <input type="text" class="form-control bot-text-input" data-key="balance.open_app_withdraw" placeholder="Откройте приложение для вывода">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Рефералы -->
                                <div class="tab-pane fade" id="referrals-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">👥 Тексты реферальной программы</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок программы</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.program_title" placeholder="Реферальная программа">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Описание бонуса</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.bonus_text" placeholder="Получайте {percent}% от заработка">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок статистики</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.stats_title" placeholder="Ваша статистика:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Приглашено друзей</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.invited_friends" placeholder="Приглашено друзей: {count}">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заработано с рефералов</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.earned_from_referrals" placeholder="Заработано с рефералов: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Ваша реферальная ссылка</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.your_referral_link" placeholder="Ваша реферальная ссылка:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Поделиться с друзьями</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.share_with_friends" placeholder="Поделитесь ссылкой с друзьями">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Текст для шаринга</label>
                                                <input type="text" class="form-control bot-text-input" data-key="referrals.share_text" placeholder="Просто смотри рекламу и получай крипту">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Статистика -->
                                <div class="tab-pane fade" id="statistics-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">📊 Тексты раздела статистики</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.title" placeholder="Ваша статистика">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Текущий баланс</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.current_balance" placeholder="Текущий баланс: {balance} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Всего заработано</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.total_earned" placeholder="Всего заработано: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">От рефералов</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.from_referrals" placeholder="От рефералов: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Количество выводов</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.withdrawals_count" placeholder="Выводов сделано: {count}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Дней в системе</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.days_active" placeholder="Дней в системе: {days}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Среднее в день</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.average_per_day" placeholder="Среднее в день: {amount} монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Приглашено друзей</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.invited_friends" placeholder="Приглашено друзей: {count}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Дата регистрации</label>
                                                <input type="text" class="form-control bot-text-input" data-key="statistics.registration_date" placeholder="Дата регистрации: {date}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Помощь -->
                                <div class="tab-pane fade" id="help-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">❓ Тексты раздела помощи</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.title" placeholder="Помощь и инструкции">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Описание платформы</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.description" placeholder="UniQPaid - это платформа...">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок инструкции</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.how_to_use" placeholder="Как пользоваться:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 1</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.step1" placeholder="1. Нажмите 'Запустить приложение'">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.step2" placeholder="2. Смотрите рекламу и получайте монеты">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 3</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.step3" placeholder="3. Приглашайте друзей">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 4</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.step4" placeholder="4. Выводите средства">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Информация о курсе</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.rate_info" placeholder="10 монет = $0.01 USD">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Реферальный бонус</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.referral_bonus" placeholder="Реферальный бонус: 10%">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Моментальные выплаты</label>
                                                <input type="text" class="form-control bot-text-input" data-key="help.instant_payouts" placeholder="Моментальные выплаты">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Подробная информация -->
                                <div class="tab-pane fade" id="detailed-info-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">📖 Тексты раздела "Подробная информация"</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок раздела</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.title" placeholder="📋 ПОДРОБНАЯ ИНФОРМАЦИЯ О UniQPaid">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок ограничений</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.limitations_title" placeholder="⚠️ ВАЖНЫЕ ОГРАНИЧЕНИЯ:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Ограничение по ссылкам</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.limitations_links" placeholder="• Каждый день доступно максимум 20 переходов по ссылкам">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Ограничение по баннерам</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.limitations_banners" placeholder="• Максимум 20 баннерных объявлений">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Ограничение по видео</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.limitations_videos" placeholder="• Максимум 20 видеорекламных роликов">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок вознаграждений</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.rewards_title" placeholder="💰 СИСТЕМА ВОЗНАГРАЖДЕНИЙ:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Награда за баннер</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.rewards_banner" placeholder="• Клик по баннеру: +10 монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Награда за видео</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.rewards_video" placeholder="• Просмотр видео: +1 монета">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Награда за ссылку</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.rewards_link" placeholder="• Переход по ссылке: +10 монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок реферальной программы</label>
                                                <input type="text" class="form-control bot-text-input" data-key="detailed_info.referral_title" placeholder="👥 РЕФЕРАЛЬНАЯ ПРОГРАММА:">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Описание реферальной программы</label>
                                                <textarea class="form-control bot-text-input" data-key="detailed_info.referral_description" rows="3" placeholder="Приглашайте друзей и получайте 10% от их заработка..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Предупреждение о блокчейне</label>
                                                <textarea class="form-control bot-text-input" data-key="detailed_info.blockchain_warning_text" rows="3" placeholder="Будьте внимательны к размеру выводимой суммы..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Рекомендация по документации</label>
                                                <textarea class="form-control bot-text-input" data-key="detailed_info.blockchain_warning_docs" rows="2" placeholder="Для получения наиболее точной информации..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Как это работает -->
                                <div class="tab-pane fade" id="how-it-works-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">❓ Тексты раздела "Как это работает"</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок раздела</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.title" placeholder="❓ КАК ЭТО РАБОТАЕТ">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок шага 1</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step1_title" placeholder="🎯 ШАГ 1: РЕГИСТРАЦИЯ">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 1: Старт</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step1_start" placeholder="• Нажмите /start в этом боте">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 1: Автоматическая регистрация</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step1_auto" placeholder="• Автоматическая регистрация в системе">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 1: Стартовый баланс</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step1_balance" placeholder="• Получите стартовый баланс">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок шага 2</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_title" placeholder="🚀 ШАГ 2: ЗАРАБОТОК">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2: Открыть приложение</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_open" placeholder="• Откройте приложение через кнопку">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2: Выбор рекламы</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_choose" placeholder="• Выберите тип рекламы:">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2: Push and Earn</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_push" placeholder="  - 🎯 Push and Earn (+10 монет)">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2: Watch and Earn</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_watch" placeholder="  - 📺 Watch and Earn (+1 монета)">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 2: Tap and Earn</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step2_tap" placeholder="  - 👆 Tap and Earn (+10 монет)">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок шага 3</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step3_title" placeholder="👥 ШАГ 3: ПРИГЛАШЕНИЕ ДРУЗЕЙ">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 3: Реферальная ссылка</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step3_referral" placeholder="• Получите реферальную ссылку">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 3: Поделиться</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step3_share" placeholder="• Поделитесь с друзьями">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 3: Бонус</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step3_bonus" placeholder="• Получайте 10% от их заработка">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок шага 4</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step4_title" placeholder="💎 ШАГ 4: ВЫВОД СРЕДСТВ">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 4: Минимум</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step4_minimum" placeholder="• Минимум для вывода: 100 монет">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 4: Криптовалюты</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step4_crypto" placeholder="• Поддерживаемые валюты: BTC, ETH, USDT, TON">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Шаг 4: Мгновенные выплаты</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.step4_instant" placeholder="• Мгновенные автоматические выплаты">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Курс обмена</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.exchange_rate" placeholder="💰 Курс: 1000 монет = $1.00 USD">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Призыв к действию</label>
                                                <input type="text" class="form-control bot-text-input" data-key="how_it_works.call_to_action" placeholder="🚀 Начните зарабатывать прямо сейчас!">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Кнопки -->
                                <div class="tab-pane fade" id="buttons-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">🔘 Тексты кнопок</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Запустить приложение</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.launch_app" placeholder="🚀 Запустить приложение">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Открыть приложение</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.open_app" placeholder="🚀 Открыть приложение">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Друзья</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.friends" placeholder="👥 Друзья">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Мой баланс</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.my_balance" placeholder="💰 Мой баланс">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Баланс</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.balance" placeholder="💰 Баланс">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Статистика</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.statistics" placeholder="📊 Статистика">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Помощь</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.help" placeholder="❓ Помощь">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Назад</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.back" placeholder="🔙 Назад">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Поделиться ссылкой</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.share_link" placeholder="📤 Поделиться ссылкой">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Подробная информация</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.detailed_info" placeholder="📖 Подробная информация">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Как это работает</label>
                                                <input type="text" class="form-control bot-text-input" data-key="buttons.how_it_works" placeholder="❓ Как это работает">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Уведомления -->
                                <div class="tab-pane fade" id="notifications-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">🔔 Тексты уведомлений</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок уведомления о неактивности</label>
                                                <input type="text" class="form-control bot-text-input" data-key="notifications.inactive_title" placeholder="Не забывайте зарабатывать!">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Заголовок тестового уведомления</label>
                                                <input type="text" class="form-control bot-text-input" data-key="notifications.test_title" placeholder="Тестовое уведомление">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Текст уведомления о неактивности</label>
                                                <textarea class="form-control bot-text-input" data-key="notifications.inactive_text" rows="4" placeholder="Привет! Вы давно не заходили..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                <label class="form-label">Текст тестового уведомления</label>
                                                <textarea class="form-control bot-text-input" data-key="notifications.test_text" rows="4" placeholder="Это тестовое уведомление..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Видимость кнопок -->
                                <div class="tab-pane fade" id="button-visibility-section" role="tabpanel">
                                    <h6 class="text-primary mb-3">👁️ Настройки видимости кнопок в боте</h6>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        Отметьте галочками кнопки, которые должны отображаться в боте. Снимите галочки с кнопок, которые нужно скрыть.
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">🚀 Основные кнопки</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="launch_app" id="btn-launch-app" checked>
                                                        <label class="form-check-label" for="btn-launch-app">
                                                            🚀 Запустить приложение
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="detailed_info" id="btn-detailed-info" checked>
                                                        <label class="form-check-label" for="btn-detailed-info">
                                                            📖 Подробная информация
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="how_it_works" id="btn-how-it-works" checked>
                                                        <label class="form-check-label" for="btn-how-it-works">
                                                            ❓ Как это работает
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">💰 Пользовательские кнопки</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="my_balance" id="btn-my-balance" checked>
                                                        <label class="form-check-label" for="btn-my-balance">
                                                            💰 Мой баланс
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="friends" id="btn-friends" checked>
                                                        <label class="form-check-label" for="btn-friends">
                                                            👥 Друзья
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="statistics" id="btn-statistics" checked>
                                                        <label class="form-check-label" for="btn-statistics">
                                                            📊 Статистика
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input button-visibility-checkbox" type="checkbox" data-button="help" id="btn-help" checked>
                                                        <label class="form-check-label" for="btn-help">
                                                            ❓ Помощь
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle me-2"></i>
                                            <strong>Важно:</strong> Кнопка "🚀 Запустить приложение" всегда отображается, так как она является основной функцией бота.
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i>
                                            <strong>Настройки видимости кнопок сохраняются автоматически</strong> при нажатии главной кнопки "Сохранить и применить к боту" внизу страницы.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Кнопки управления -->
                            <div class="text-center mt-4">
                                <button onclick="loadBotTexts()" class="btn btn-outline-primary me-2">
                                    <i class="bi bi-arrow-clockwise"></i> Загрузить тексты
                                </button>
                                <button onclick="saveAndApplyAll()" class="btn btn-success btn-lg">
                                    <i class="bi bi-check-circle"></i> Сохранить и применить к боту
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Логи настройки -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-terminal me-2"></i>
                                Лог настройки
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="setup-log" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
                                <div class="text-muted">Готов к настройке...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Функция для отображения статуса
function showStatus(message, type = 'info') {
    const alert = document.getElementById('status-alert');
    const messageDiv = document.getElementById('status-message');
    
    alert.className = `alert alert-${type}`;
    alert.classList.remove('d-none');
    messageDiv.innerHTML = message;
    
    // Автоскрытие через 5 секунд для успешных операций
    if (type === 'success') {
        setTimeout(() => {
            alert.classList.add('d-none');
        }, 5000);
    }
}

// Функция для добавления в лог
function addToLog(message, type = 'info') {
    const log = document.getElementById('setup-log');
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        'info': 'text-info',
        'success': 'text-success', 
        'error': 'text-danger',
        'warning': 'text-warning'
    };
    
    const logEntry = document.createElement('div');
    logEntry.className = colors[type] || 'text-light';
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    log.appendChild(logEntry);
    log.scrollTop = log.scrollHeight;
}

// Настройка Menu Button
async function setupMenuButton() {
    addToLog('🔧 Настройка Menu Button...', 'info');
    
    try {
        const response = await fetch('setup_bot_interface.php?action=setup_menu_button');
        const result = await response.json();
        
        if (result.success) {
            addToLog('✅ Menu Button настроена успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Menu Button настроена успешно!', 'success');
        } else {
            addToLog('❌ Ошибка настройки Menu Button: ' + (result.error || 'Неизвестная ошибка'), 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка настройки Menu Button', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Настройка Bot Commands
async function setupCommands() {
    addToLog('🔧 Настройка Bot Commands...', 'info');
    
    try {
        const response = await fetch('setup_bot_interface.php?action=setup_commands');
        const result = await response.json();
        
        if (result.success) {
            addToLog('✅ Bot Commands настроены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Bot Commands настроены успешно!', 'success');
        } else {
            addToLog('❌ Ошибка настройки Bot Commands: ' + (result.error || 'Неизвестная ошибка'), 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка настройки Bot Commands', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Настройка описаний бота
async function setupDescriptions() {
    addToLog('🔧 Настройка описаний бота...', 'info');
    
    try {
        const response = await fetch('setup_bot_interface.php?action=setup_descriptions');
        const result = await response.json();
        
        if (result.success) {
            addToLog('✅ Описания бота настроены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Описания бота настроены успешно!', 'success');
        } else {
            addToLog('❌ Ошибка настройки описаний: ' + (result.error || 'Неизвестная ошибка'), 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка настройки описаний', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Настройка всего сразу
async function setupAll() {
    addToLog('🚀 Запуск полной настройки бота...', 'info');
    showStatus('<i class="bi bi-gear"></i> Выполняется настройка всех компонентов...', 'info');
    
    try {
        const response = await fetch('setup_bot_interface.php?action=setup_all');
        const result = await response.json();
        
        if (result.success) {
            addToLog('🎉 Все настройки выполнены успешно!', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Все настройки бота выполнены успешно!', 'success');
        } else {
            addToLog('⚠️ Некоторые настройки не удалось выполнить', 'warning');
            showStatus('<i class="bi bi-exclamation-triangle"></i> Некоторые настройки не удалось выполнить', 'warning');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// === ФУНКЦИИ ДЛЯ РАБОТЫ С ТЕКСТАМИ БОТА ===

let currentBotTexts = {};
let currentBotLanguage = 'ru';
let buttonVisibilitySettings = {};

// Загрузка текстов бота
async function loadBotTexts() {
    try {
        addToLog('📥 Загрузка текстов бота...', 'info');
        const response = await fetch('bot_texts_api.php?action=get_all');
        const result = await response.json();

        if (result.success) {
            currentBotTexts = result.texts;
            populateBotTexts();
            addToLog('✅ Тексты бота загружены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Тексты загружены успешно!', 'success');
        } else {
            addToLog('❌ Ошибка загрузки: ' + result.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка загрузки текстов', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Заполнение полей текстами
function populateBotTexts() {
    const lang = currentBotLanguage;

    if (!currentBotTexts[lang]) {
        addToLog('⚠️ Тексты для языка ' + lang + ' не найдены', 'warning');
        return;
    }

    const texts = currentBotTexts[lang];

    // Заполняем все поля
    document.querySelectorAll('.bot-text-input').forEach(input => {
        const key = input.dataset.key;
        if (key) {
            const value = getNestedValue(texts, key);
            if (value !== undefined) {
                input.value = value;
            }
        }
    });

    addToLog('📝 Поля заполнены текстами для языка: ' + lang, 'info');
}

// Получение вложенного значения по ключу
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, prop) => current && current[prop], obj);
}

// Установка вложенного значения по ключу
function setNestedValue(obj, key, value) {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
}

// Глубокое объединение объектов
function deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
    }

    return result;
}

// Сбор данных из полей
function collectBotTexts() {
    const lang = currentBotLanguage;

    if (!currentBotTexts[lang]) {
        currentBotTexts[lang] = {};
    }

    document.querySelectorAll('.bot-text-input').forEach(input => {
        const key = input.dataset.key;
        const value = input.value;

        if (key && value) {
            setNestedValue(currentBotTexts[lang], key, value);
        }
    });

    addToLog('📝 Собраны тексты для языка: ' + lang, 'info');
}

// === ФУНКЦИИ ДЛЯ РАБОТЫ С ВИДИМОСТЬЮ КНОПОК ===

// Загрузка настроек видимости кнопок
async function loadButtonVisibility() {
    try {
        addToLog('👁️ Загрузка настроек видимости кнопок...', 'info');
        const response = await fetch('bot_texts_api.php?action=get_button_visibility');
        addToLog('📥 Получен ответ загрузки, статус: ' + response.status, 'info');

        const result = await response.json();
        addToLog('📋 Данные загрузки: ' + JSON.stringify(result), 'info');

        if (result.success) {
            buttonVisibilitySettings = result.settings;
            addToLog('💾 Настройки сохранены в переменную: ' + JSON.stringify(buttonVisibilitySettings), 'info');
            populateButtonVisibility();
            addToLog('✅ Настройки видимости кнопок загружены успешно', 'success');
        } else {
            addToLog('❌ Ошибка загрузки настроек видимости: ' + result.error, 'error');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка при загрузке видимости: ' + error.message, 'error');
    }
}

// Заполнение чекбоксов настройками видимости
function populateButtonVisibility() {
    const checkboxes = document.querySelectorAll('.button-visibility-checkbox');
    addToLog(`🔍 Заполнение чекбоксов, найдено: ${checkboxes.length}`, 'info');
    addToLog('📋 Текущие настройки: ' + JSON.stringify(buttonVisibilitySettings), 'info');

    checkboxes.forEach(checkbox => {
        const buttonKey = checkbox.dataset.button;
        if (buttonVisibilitySettings.hasOwnProperty(buttonKey)) {
            const shouldBeChecked = buttonVisibilitySettings[buttonKey];
            checkbox.checked = shouldBeChecked;
            addToLog(`✅ ${buttonKey}: установлен в ${shouldBeChecked}`, 'info');
        } else {
            addToLog(`⚠️ ${buttonKey}: настройка не найдена`, 'warning');
        }
    });
    addToLog('👁️ Чекбоксы видимости кнопок обновлены', 'info');
}

// Сбор настроек видимости из чекбоксов
function collectButtonVisibility() {
    const checkboxes = document.querySelectorAll('.button-visibility-checkbox');
    addToLog(`🔍 Найдено чекбоксов: ${checkboxes.length}`, 'info');

    checkboxes.forEach(checkbox => {
        const buttonKey = checkbox.dataset.button;
        const isChecked = checkbox.checked;
        buttonVisibilitySettings[buttonKey] = isChecked;
        addToLog(`📋 ${buttonKey}: ${isChecked ? 'включена' : 'выключена'}`, 'info');
    });

    addToLog('👁️ Собраны настройки видимости кнопок: ' + JSON.stringify(buttonVisibilitySettings), 'info');
}

// Сохранение настроек видимости кнопок
async function saveButtonVisibility() {
    try {
        addToLog('💾 Сохранение настроек видимости кнопок...', 'info');

        // Собираем настройки из чекбоксов
        collectButtonVisibility();

        addToLog('📤 Отправляем данные: ' + JSON.stringify({
            action: 'save_button_visibility',
            settings: buttonVisibilitySettings
        }), 'info');

        const response = await fetch('bot_texts_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'save_button_visibility',
                settings: buttonVisibilitySettings
            })
        });

        addToLog('📥 Получен ответ от сервера, статус: ' + response.status, 'info');

        const result = await response.json();
        addToLog('📋 Ответ сервера: ' + JSON.stringify(result), 'info');

        if (result.success) {
            addToLog('✅ Настройки видимости кнопок сохранены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Настройки видимости сохранены!', 'success');
        } else {
            addToLog('❌ Ошибка сохранения настроек видимости: ' + result.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка сохранения настроек видимости', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка при сохранении видимости: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Сохранение текстов бота
async function saveBotTexts() {
    try {
        addToLog('💾 Сохранение текстов бота...', 'info');
        showStatus('<i class="bi bi-save"></i> Сохранение текстов...', 'info');

        // Собираем данные из полей
        collectBotTexts();

        // ИСПРАВЛЕНИЕ: Загружаем полные тексты перед сохранением
        addToLog('📥 Загрузка полных текстов для безопасного сохранения...', 'info');
        const loadResponse = await fetch('bot_texts_api.php?action=get_all');
        const loadResult = await loadResponse.json();

        if (!loadResult.success) {
            addToLog('❌ Ошибка загрузки полных текстов: ' + loadResult.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка загрузки', 'danger');
            return;
        }

        // Объединяем загруженные тексты с отредактированными
        const fullTexts = loadResult.texts;
        const lang = currentBotLanguage;

        // Обновляем только текущий язык (глубокое объединение)
        if (currentBotTexts[lang]) {
            fullTexts[lang] = deepMerge(fullTexts[lang] || {}, currentBotTexts[lang]);
            addToLog('🔄 Объединены тексты для языка: ' + lang, 'info');
        }

        const response = await fetch('bot_texts_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_all',
                texts: fullTexts
            })
        });

        const result = await response.json();

        if (result.success) {
            // Обновляем currentBotTexts полными данными
            currentBotTexts = fullTexts;
            addToLog('✅ Тексты бота сохранены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Тексты сохранены успешно!', 'success');
        } else {
            addToLog('❌ Ошибка сохранения: ' + result.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка сохранения', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Применение настроек к боту
async function applyBotSettings() {
    try {
        addToLog('🤖 Применение настроек к боту...', 'info');
        showStatus('<i class="bi bi-robot"></i> Применение настроек к боту...', 'info');

        // Сначала сохраняем тексты
        await saveBotTexts();

        // Затем применяем настройки через отдельный API
        const response = await fetch('bot_texts_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'apply_to_bot'
            })
        });

        const result = await response.json();

        if (result.success) {
            addToLog('✅ Настройки бота применены успешно', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Настройки бота применены успешно!', 'success');
        } else {
            addToLog('❌ Ошибка применения настроек: ' + (result.error || 'Неизвестная ошибка'), 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка применения настроек', 'danger');
        }
    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Сохранение и применение всех настроек одной кнопкой
async function saveAndApplyAll() {
    try {
        addToLog('🚀 Запуск полного обновления бота...', 'info');
        showStatus('<i class="bi bi-gear"></i> Сохранение и применение всех настроек...', 'info');

        // 1. Собираем данные из полей
        collectBotTexts();

        // 2. Загружаем полные тексты для безопасного сохранения
        addToLog('📥 Загрузка полных текстов...', 'info');
        const loadResponse = await fetch('bot_texts_api.php?action=get_all');
        const loadResult = await loadResponse.json();

        if (!loadResult.success) {
            addToLog('❌ Ошибка загрузки полных текстов: ' + loadResult.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка загрузки', 'danger');
            return;
        }

        // Объединяем тексты (глубокое объединение)
        const fullTexts = loadResult.texts;
        const lang = currentBotLanguage;
        if (currentBotTexts[lang]) {
            fullTexts[lang] = deepMerge(fullTexts[lang] || {}, currentBotTexts[lang]);
        }

        // 3. Сохраняем тексты
        addToLog('💾 Сохранение текстов...', 'info');
        const saveResponse = await fetch('bot_texts_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_all',
                texts: fullTexts
            })
        });

        const saveResult = await saveResponse.json();
        if (!saveResult.success) {
            addToLog('❌ Ошибка сохранения: ' + saveResult.error, 'error');
            showStatus('<i class="bi bi-x-circle"></i> Ошибка сохранения текстов', 'danger');
            return;
        }

        // Обновляем currentBotTexts полными данными
        currentBotTexts = fullTexts;
        addToLog('✅ Тексты сохранены успешно', 'success');

        // 4. Сохраняем настройки видимости кнопок
        addToLog('👁️ Сохранение настроек видимости кнопок...', 'info');
        await saveButtonVisibility();

        // 5. Применяем к боту
        addToLog('🤖 Применение настроек к боту...', 'info');
        const applyResponse = await fetch('bot_texts_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'apply_to_bot'
            })
        });

        const applyResult = await applyResponse.json();
        if (applyResult.success) {
            addToLog('🎉 Все настройки применены успешно!', 'success');
            showStatus('<i class="bi bi-check-circle"></i> Все настройки сохранены и применены к боту!', 'success');
        } else {
            addToLog('⚠️ Настройки сохранены, но не все удалось применить к боту', 'warning');
            showStatus('<i class="bi bi-exclamation-triangle"></i> Настройки сохранены, но есть проблемы с применением к боту', 'warning');
        }

    } catch (error) {
        addToLog('❌ Критическая ошибка: ' + error.message, 'error');
        showStatus('<i class="bi bi-bug"></i> Критическая ошибка: ' + error.message, 'danger');
    }
}

// Обработчики событий
document.addEventListener('DOMContentLoaded', function() {
    // Загружаем тексты при загрузке страницы
    setTimeout(() => {
        loadBotTexts();
        loadButtonVisibility();
    }, 500);

    // Обработчик смены языка для бота
    document.querySelectorAll('input[name="bot-language"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                currentBotLanguage = this.value;
                addToLog(`🌐 Переключен язык бота: ${currentBotLanguage}`, 'info');
                populateBotTexts();
            }
        });
    });

    // Обработчики изменения полей
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('bot-text-input')) {
            const key = e.target.dataset.key;
            const value = e.target.value;
            addToLog(`📝 Изменен текст: ${key} = "${value.substring(0, 30)}${value.length > 30 ? '...' : ''}"`, 'info');
        }
    });
});
</script>

<?php include 'templates/footer.php'; ?>
