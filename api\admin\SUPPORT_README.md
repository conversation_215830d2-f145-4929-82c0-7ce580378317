# Система поддержки UniQPaid

## Описание

Система поддержки позволяет пользователям обращаться за помощью через отдельного Telegram бота (@uniqpaid_support_bot), а администраторам отвечать на обращения через веб-интерфейс в админке.

## Компоненты системы

### 1. Бот поддержки
- **Токен:** `7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M`
- **Username:** `@uniqpaid_support_bot`
- **Webhook URL:** `https://app.uniqpaid.com/test3/api/admin/support_webhook.php`

### 2. Файлы системы
- `support_config.php` - конфигурация бота поддержки
- `support_data.php` - функции для работы с данными
- `support_webhook.php` - обработчик webhook от Telegram
- `support.php` - главная страница поддержки в админке
- `support_chat.php` - страница отдельного чата
- `send_support_message.php` - API для отправки сообщений
- `setup_support_webhook.php` - скрипт установки webhook

### 3. Файлы данных
- `support_chats.json` - информация о чатах поддержки
- `support_messages.json` - все сообщения поддержки
- `support_bot.log` - логи бота поддержки

## Установка и настройка

### 1. Установка webhook
Откройте в браузере:
```
https://app.uniqpaid.com/test3/api/admin/setup_support_webhook.php
```

### 2. Проверка работы
1. Найдите бота @uniqpaid_support_bot в Telegram
2. Отправьте команду `/start`
3. Напишите тестовое сообщение
4. Зайдите в админку → Поддержка
5. Проверьте, что сообщение появилось в списке чатов

### 3. Ответ на обращение
1. В админке откройте раздел "Поддержка"
2. Выберите чат с пользователем
3. Введите ответ в форму внизу страницы
4. Нажмите "Отправить сообщение"
5. Сообщение будет доставлено пользователю через бота

## Функционал

### Для пользователей
- Обращение к боту @uniqpaid_support_bot
- Автоматическое определение языка (русский/английский)
- Сообщение о том, что ответит первый освободившийся оператор
- Отправка вопросов и получение ответов
- Кнопки быстрого доступа (FAQ, помощь с выводом)
- Подробные инструкции по выводу средств

### Для администраторов
- Просмотр всех обращений в едином интерфейсе
- Статистика по чатам (всего, непрочитанные, активные)
- Отображение языка каждого пользователя (🇷🇺/🇺🇸)
- Просмотр истории переписки с каждым пользователем
- Отправка ответов через веб-интерфейс (AJAX)
- Автоматическое обновление чатов
- Управление webhook (проверка, настройка, удаление)
- Подробная информация о боте и webhook

## Безопасность

### Защита файлов данных
- JSON файлы защищены от прямого доступа через .htaccess
- Логи бота недоступны извне
- Webhook доступен только с IP адресов Telegram

### Аутентификация
- Все страницы админки требуют авторизации
- API endpoints проверяют сессию администратора

## Структура данных

### Чат поддержки (support_chats.json)
```json
{
  "chat_12345_1640995200": {
    "user_id": 12345,
    "username": "user123",
    "first_name": "Иван",
    "last_name": "Петров",
    "language": "ru",
    "created_at": "2023-12-31 12:00:00",
    "updated_at": "2023-12-31 12:30:00"
  }
}
```

### Сообщение (support_messages.json)
```json
[
  {
    "chat_id": "chat_12345_1640995200",
    "message_id": 123,
    "from_user": true,
    "text": "Привет, у меня проблема с выводом",
    "created_at": "2023-12-31 12:00:00"
  }
]
```

## Логирование

Все действия бота записываются в `support_bot.log`:
- Входящие сообщения от пользователей
- Отправленные ответы
- Ошибки API Telegram
- Действия администраторов

## Новые возможности

### Многоязычность
- Автоматическое определение языка пользователя по Telegram
- Поддержка русского и английского языков
- Сохранение языка пользователя в данных чата
- Отображение флага страны в админке

### Улучшенный интерфейс
- AJAX отправка сообщений без перезагрузки страницы
- Управление webhook прямо из админки
- Модальные окна для операций с webhook
- Подробная статистика и информация о пользователях

### FAQ и помощь
- Встроенные часто задаваемые вопросы
- Пошаговая инструкция по выводу средств
- Кнопки быстрого доступа к информации

## Управление Webhook

### Из админки
1. Перейдите в раздел "Поддержка"
2. Используйте кнопки:
   - "Проверить Webhook" - показывает статус
   - "Настроить Webhook" - устанавливает webhook
   - "Удалить Webhook" - удаляет webhook

### Через браузер
```
https://app.uniqpaid.com/test3/api/admin/setup_support_webhook.php
```

## Мониторинг

### Проверка работы бота
```bash
tail -f api/admin/support_bot.log
```

### API для проверки webhook
```
https://app.uniqpaid.com/test3/api/admin/webhook_manager.php?action=check
```

## Возможные проблемы

### Webhook не работает
1. Проверьте, что URL доступен извне
2. Убедитесь, что сервер возвращает HTTP 200
3. Проверьте логи на ошибки PHP

### Сообщения не отправляются
1. Проверьте токен бота
2. Убедитесь, что пользователь не заблокировал бота
3. Проверьте логи на ошибки API

### Данные не сохраняются
1. Проверьте права на запись в папку admin
2. Убедитесь, что JSON файлы существуют
3. Проверьте синтаксис JSON файлов
