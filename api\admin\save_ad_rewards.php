<?php
/**
 * api/admin/save_ad_rewards.php
 * API для сохранения настроек наград за рекламу
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    echo json_encode([
        'success' => false,
        'error' => 'Не авторизован'
    ]);
    exit;
}

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'error' => 'Метод не поддерживается'
    ]);
    exit;
}

try {
    // Получаем данные из POST
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Если данные не в JSON, пробуем $_POST
    if (!$input) {
        $input = $_POST;
    }
    
    // Проверяем наличие данных
    if (!$input || !isset($input['rewards'])) {
        echo json_encode([
            'success' => false,
            'error' => 'Отсутствуют данные наград'
        ]);
        exit;
    }
    
    $rewards = $input['rewards'];
    
    // Валидация данных
    $validRewards = [];
    if (isset($rewards['native_banner']) && is_numeric($rewards['native_banner'])) {
        $validRewards['native_banner'] = intval($rewards['native_banner']);
    }
    if (isset($rewards['interstitial']) && is_numeric($rewards['interstitial'])) {
        $validRewards['interstitial'] = intval($rewards['interstitial']);
    }
    if (isset($rewards['rewarded_video']) && is_numeric($rewards['rewarded_video'])) {
        $validRewards['rewarded_video'] = intval($rewards['rewarded_video']);
    }
    
    if (empty($validRewards)) {
        echo json_encode([
            'success' => false,
            'error' => 'Нет валидных данных для сохранения'
        ]);
        exit;
    }
    
    // Читаем config.php
    $configFile = __DIR__ . '/../config.php';
    $configContent = file_get_contents($configFile);
    
    if ($configContent === false) {
        throw new Exception('Не удалось прочитать config.php');
    }
    
    $updated = false;
    
    // Обновляем награды
    if (isset($validRewards['native_banner'])) {
        $value = $validRewards['native_banner'];
        $pattern = "/define\('AD_REWARD_NATIVE_BANNER',\s*\d+\);/";
        $replacement = "define('AD_REWARD_NATIVE_BANNER', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
    }
    
    if (isset($validRewards['interstitial'])) {
        $value = $validRewards['interstitial'];
        $pattern = "/define\('AD_REWARD_INTERSTITIAL',\s*\d+\);/";
        $replacement = "define('AD_REWARD_INTERSTITIAL', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
    }
    
    if (isset($validRewards['rewarded_video'])) {
        $value = $validRewards['rewarded_video'];
        $pattern = "/define\('AD_REWARD_REWARDED_VIDEO',\s*\d+\);/";
        $replacement = "define('AD_REWARD_REWARDED_VIDEO', $value);";
        $configContent = preg_replace($pattern, $replacement, $configContent);
        $updated = true;
    }
    
    // Сохраняем файл
    if ($updated) {
        if (file_put_contents($configFile, $configContent) !== false) {
            // Логируем изменения
            error_log('Награды за рекламу обновлены: ' . json_encode($validRewards));
            
            echo json_encode([
                'success' => true,
                'message' => 'Настройки наград успешно сохранены',
                'rewards' => $validRewards
            ]);
        } else {
            throw new Exception('Не удалось сохранить config.php');
        }
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Нет изменений для сохранения'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Ошибка в save_ad_rewards.php: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера: ' . $e->getMessage()
    ]);
}
?>
