<?php
/**
 * api/server-diagnostic.php
 * Диагностика состояния сервера и файлов API
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>🔧 Диагностика сервера</title>
    <style>
        body { font-family: monospace; background: #f5f5f5; padding: 20px; }
        .ok { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .section { margin: 20px 0; padding: 15px; background: white; border-radius: 5px; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🔧 Диагностика сервера</h1>";
echo "<p><strong>Время:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Сервер:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";

// Проверяем основные файлы
echo "<div class='section'>";
echo "<h2>📁 Проверка файлов API</h2>";

$requiredFiles = [
    'check-block-status.php',
    'checkUserWithdrawals.php', 
    'getWithdrawalHistory.php',
    'forceUpdateWithdrawalStatuses.php',
    'config.php',
    'functions.php',
    'validate_initdata.php'
];

foreach ($requiredFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "<div class='ok'>✅ $file - существует (" . filesize($path) . " байт)</div>";
        
        // Проверяем синтаксис PHP
        $output = [];
        $return_var = 0;
        exec("php -l \"$path\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='ok'>   ✅ Синтаксис корректен</div>";
        } else {
            echo "<div class='error'>   ❌ Синтаксическая ошибка:</div>";
            echo "<pre>" . implode("\n", $output) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ $file - НЕ НАЙДЕН</div>";
    }
}

echo "</div>";

// Проверяем папки
echo "<div class='section'>";
echo "<h2>📂 Проверка папок</h2>";

$requiredDirs = [
    '../database',
    '../js',
    '../css'
];

foreach ($requiredDirs as $dir) {
    $path = __DIR__ . '/' . $dir;
    if (is_dir($path)) {
        $files = scandir($path);
        $fileCount = count($files) - 2; // исключаем . и ..
        echo "<div class='ok'>✅ $dir - существует ($fileCount файлов)</div>";
    } else {
        echo "<div class='error'>❌ $dir - НЕ НАЙДЕНА</div>";
    }
}

echo "</div>";

// Проверяем права доступа
echo "<div class='section'>";
echo "<h2>🔐 Проверка прав доступа</h2>";

$testFiles = ['check-block-status.php', 'config.php'];
foreach ($testFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        $perms = fileperms($path);
        $readable = is_readable($path) ? '✅' : '❌';
        $writable = is_writable($path) ? '✅' : '❌';
        echo "<div>$file: Чтение $readable Запись $writable (права: " . substr(sprintf('%o', $perms), -4) . ")</div>";
    }
}

echo "</div>";

// Тестируем простой API вызов
echo "<div class='section'>";
echo "<h2>🧪 Тест API</h2>";

try {
    // Тест 1: check-block-status.php
    echo "<h3>Тест check-block-status.php:</h3>";
    
    if (file_exists(__DIR__ . '/check-block-status.php')) {
        ob_start();
        $_POST = [];
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_SERVER['CONTENT_TYPE'] = 'application/json';
        
        // Эмулируем входные данные
        $GLOBALS['HTTP_RAW_POST_DATA'] = json_encode(['test' => true]);
        
        include __DIR__ . '/check-block-status.php';
        $output = ob_get_clean();
        
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        // Проверяем, является ли вывод JSON
        $json = json_decode($output, true);
        if ($json !== null) {
            echo "<div class='ok'>✅ Возвращает валидный JSON</div>";
        } else {
            echo "<div class='error'>❌ Не возвращает валидный JSON</div>";
        }
    } else {
        echo "<div class='error'>❌ Файл check-block-status.php не найден</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка тестирования: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Информация о PHP
echo "<div class='section'>";
echo "<h2>🐘 Информация о PHP</h2>";
echo "<div>Версия PHP: " . phpversion() . "</div>";
echo "<div>Память: " . ini_get('memory_limit') . "</div>";
echo "<div>Максимальное время выполнения: " . ini_get('max_execution_time') . "s</div>";
echo "<div>Загрузка файлов: " . (ini_get('file_uploads') ? 'включена' : 'отключена') . "</div>";
echo "<div>Максимальный размер POST: " . ini_get('post_max_size') . "</div>";

// Проверяем расширения
$requiredExtensions = ['json', 'curl', 'openssl'];
echo "<h3>Расширения PHP:</h3>";
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext) ? '✅' : '❌';
    echo "<div>$loaded $ext</div>";
}

echo "</div>";

// Логи ошибок
echo "<div class='section'>";
echo "<h2>📋 Последние ошибки</h2>";

$errorLogs = [
    __DIR__ . '/error.log',
    __DIR__ . '/php-error.log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log'
];

$foundLogs = false;
foreach ($errorLogs as $logFile) {
    if (file_exists($logFile) && is_readable($logFile)) {
        $foundLogs = true;
        echo "<h3>" . basename($logFile) . ":</h3>";
        $lines = file($logFile);
        $lastLines = array_slice($lines, -10);
        echo "<pre>" . htmlspecialchars(implode('', $lastLines)) . "</pre>";
        break;
    }
}

if (!$foundLogs) {
    echo "<div class='warning'>⚠️ Логи ошибок не найдены или недоступны</div>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>🔄 Рекомендации</h2>";
echo "<ol>";
echo "<li>Если файлы отсутствуют - загрузите их на сервер</li>";
echo "<li>Если есть синтаксические ошибки - исправьте их</li>";
echo "<li>Если права доступа неверные - установите 644 для файлов</li>";
echo "<li>Если API не возвращает JSON - проверьте логи ошибок</li>";
echo "<li>Очистите кэш браузера и попробуйте снова</li>";
echo "</ol>";
echo "</div>";

echo "</body></html>";
?>
