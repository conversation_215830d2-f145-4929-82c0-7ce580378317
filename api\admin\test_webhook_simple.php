<?php
/**
 * Простой тест webhook
 */

require_once __DIR__ . '/support_config.php';

echo "<h1>Тест webhook бота поддержки</h1>";

// Проверяем информацию о боте
echo "<h2>1. Проверка бота</h2>";
$botInfo = supportTelegramRequest('getMe', []);
if ($botInfo) {
    echo "<p style='color: green;'>✅ Бот работает: " . $botInfo['result']['first_name'] . " (@" . $botInfo['result']['username'] . ")</p>";
} else {
    echo "<p style='color: red;'>❌ Бот не отвечает</p>";
}

// Проверяем webhook
echo "<h2>2. Проверка webhook</h2>";
$webhookInfo = supportTelegramRequest('getWebhookInfo', []);
if ($webhookInfo) {
    $webhook = $webhookInfo['result'];
    echo "<p><strong>URL:</strong> " . ($webhook['url'] ?: 'НЕ УСТАНОВЛЕН') . "</p>";
    echo "<p><strong>Ожидающих обновлений:</strong> " . $webhook['pending_update_count'] . "</p>";
    
    if (isset($webhook['last_error_date'])) {
        echo "<p style='color: red;'><strong>Последняя ошибка:</strong> " . date('d.m.Y H:i:s', $webhook['last_error_date']) . "</p>";
        echo "<p style='color: red;'><strong>Сообщение ошибки:</strong> " . $webhook['last_error_message'] . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Не удалось получить информацию о webhook</p>";
}

// Устанавливаем webhook
echo "<h2>3. Установка webhook</h2>";
$setupResult = supportTelegramRequest('setWebhook', [
    'url' => SUPPORT_WEBHOOK_URL,
    'drop_pending_updates' => true
]);

if ($setupResult) {
    echo "<p style='color: green;'>✅ Webhook установлен успешно!</p>";
    echo "<p><strong>URL:</strong> " . SUPPORT_WEBHOOK_URL . "</p>";
} else {
    echo "<p style='color: red;'>❌ Ошибка установки webhook</p>";
}

// Проверяем файлы
echo "<h2>4. Проверка файлов</h2>";
$files = [
    'support_chats.json' => SUPPORT_CHATS_FILE,
    'support_messages.json' => SUPPORT_MESSAGES_FILE,
    'support_bot.log' => SUPPORT_LOG_FILE
];

foreach ($files as $name => $path) {
    if (file_exists($path)) {
        echo "<p style='color: green;'>✅ {$name} существует</p>";
        if (is_writable($path)) {
            echo "<p style='color: green;'>✅ {$name} доступен для записи</p>";
        } else {
            echo "<p style='color: red;'>❌ {$name} НЕ доступен для записи</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ {$name} НЕ существует</p>";
    }
}

// Проверяем права на папку
echo "<h2>5. Проверка прав</h2>";
$adminDir = __DIR__;
if (is_writable($adminDir)) {
    echo "<p style='color: green;'>✅ Папка admin доступна для записи</p>";
} else {
    echo "<p style='color: red;'>❌ Папка admin НЕ доступна для записи</p>";
}

// Тестируем логирование
echo "<h2>6. Тест логирования</h2>";
supportBotLog("TEST: Тестовое сообщение от " . date('Y-m-d H:i:s'));
if (file_exists(SUPPORT_LOG_FILE)) {
    $logContent = file_get_contents(SUPPORT_LOG_FILE);
    $lines = explode("\n", trim($logContent));
    $lastLine = end($lines);
    echo "<p style='color: green;'>✅ Последняя запись в логе: " . htmlspecialchars($lastLine) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Лог файл не создался</p>";
}

echo "<hr>";
echo "<p><strong>Следующие шаги:</strong></p>";
echo "<ol>";
echo "<li>Напишите боту @uniqpaid_support_bot команду /start</li>";
echo "<li>Отправьте любое сообщение</li>";
echo "<li>Проверьте лог файл: <a href='support_bot.log' target='_blank'>support_bot.log</a></li>";
echo "<li>Проверьте админку: <a href='support.php'>support.php</a></li>";
echo "</ol>";
?>
