<?php
/**
 * api/admin/export_users.php
 * Экспорт данных пользователей в CSV
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    die('Доступ запрещен');
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in admin/export_users.php');
    die('Ошибка: Не удалось загрузить config.php');
}
if (!(@require_once __DIR__ . '/../db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in admin/export_users.php');
    die('Ошибка: Не удалось загрузить db_mock.php');
}

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    http_response_code(500);
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Получаем список ID пользователей для экспорта
$userIds = [];
if (isset($_POST['user_ids']) && is_array($_POST['user_ids'])) {
    $userIds = array_map('intval', $_POST['user_ids']);
} elseif (isset($_GET['user_ids'])) {
    $userIds = array_map('intval', explode(',', $_GET['user_ids']));
} else {
    // Экспортируем всех пользователей
    $userIds = array_keys($userData);
}

// Фильтруем только существующих пользователей
$userIds = array_filter($userIds, function($id) use ($userData) {
    return isset($userData[$id]);
});

if (empty($userIds)) {
    http_response_code(400);
    die('Ошибка: Нет пользователей для экспорта');
}

// Функция для получения статистики выплат пользователя
function getUserWithdrawalStats($userId, $userData) {
    $stats = [
        'total_withdrawals' => 0,
        'successful_withdrawals' => 0,
        'failed_withdrawals' => 0,
        'pending_withdrawals' => 0,
        'total_withdrawn' => 0,
        'total_refunded' => 0
    ];
    
    if (!isset($userData[$userId]['withdrawals']) || !is_array($userData[$userId]['withdrawals'])) {
        return $stats;
    }
    
    $withdrawals = $userData[$userId]['withdrawals'];
    $stats['total_withdrawals'] = count($withdrawals);
    
    foreach ($withdrawals as $withdrawal) {
        $status = strtolower($withdrawal['status'] ?? 'unknown');
        $amount = $withdrawal['coins_amount'] ?? 0;
        
        if (in_array($status, ['finished', 'confirmed', 'completed'])) {
            $stats['successful_withdrawals']++;
            $stats['total_withdrawn'] += $amount;
        } elseif (in_array($status, ['failed', 'rejected', 'cancelled', 'expired'])) {
            $stats['failed_withdrawals']++;
            if (isset($withdrawal['refunded']) && $withdrawal['refunded']) {
                $stats['total_refunded'] += $withdrawal['refund_amount'] ?? $amount;
            }
        } else {
            $stats['pending_withdrawals']++;
        }
    }
    
    return $stats;
}

// Подготавливаем данные для экспорта
$exportData = [];
$exportData[] = [
    'ID',
    'Username',
    'Имя',
    'Фамилия',
    'Баланс',
    'Дата регистрации',
    'Статус',
    'Рефералы',
    'Заработано на рефералах',
    'Всего заработано',
    'Подозрительная активность',
    'Всего выплат',
    'Успешных выплат',
    'Неудачных выплат',
    'В обработке',
    'Выведено монет',
    'Возвращено монет'
];

foreach ($userIds as $userId) {
    $user = $userData[$userId];
    $stats = getUserWithdrawalStats($userId, $userData);
    
    $exportData[] = [
        $userId,
        $user['username'] ?? '',
        $user['first_name'] ?? '',
        $user['last_name'] ?? '',
        $user['balance'] ?? 0,
        isset($user['joined']) ? date('Y-m-d H:i:s', $user['joined']) : '',
        (isset($user['blocked']) && $user['blocked']) ? 'Заблокирован' : 'Активен',
        $user['referrals_count'] ?? 0,
        $user['referral_earnings'] ?? 0,
        $user['total_earned'] ?? 0,
        $user['suspicious_activity'] ?? 0,
        $stats['total_withdrawals'],
        $stats['successful_withdrawals'],
        $stats['failed_withdrawals'],
        $stats['pending_withdrawals'],
        $stats['total_withdrawn'],
        $stats['total_refunded']
    ];
}

// Устанавливаем заголовки для скачивания CSV
$filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Открываем поток вывода
$output = fopen('php://output', 'w');

// Добавляем BOM для корректного отображения UTF-8 в Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Записываем данные в CSV
foreach ($exportData as $row) {
    fputcsv($output, $row, ';'); // Используем точку с запятой как разделитель для Excel
}

// Закрываем поток
fclose($output);

// Логируем экспорт
error_log("ADMIN EXPORT: Пользователь " . ($_SESSION['admin_username'] ?? 'unknown') . " экспортировал " . count($userIds) . " пользователей");

exit;
?>
