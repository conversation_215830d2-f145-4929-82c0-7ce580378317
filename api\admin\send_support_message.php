<?php
/**
 * api/admin/send_support_message.php
 * API для отправки сообщений поддержки из админки
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Не авторизован']);
    exit;
}

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Метод не разрешен']);
    exit;
}

// Получаем данные из запроса
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    // Пробуем получить данные из POST
    $chatId = $_POST['chat_id'] ?? '';
    $messageText = $_POST['message_text'] ?? '';
} else {
    $chatId = $input['chat_id'] ?? '';
    $messageText = $input['message_text'] ?? '';
}

// Валидация данных
if (empty($chatId) || empty($messageText)) {
    http_response_code(400);
    echo json_encode(['error' => 'Не указан chat_id или message_text']);
    exit;
}

// Получаем информацию о чате
$chatInfo = getSupportChatInfo($chatId);
if (!$chatInfo) {
    http_response_code(404);
    echo json_encode(['error' => 'Чат не найден']);
    exit;
}

try {
    // Отправляем сообщение пользователю через бота поддержки
    $result = sendSupportMessage($chatInfo['user_id'], $messageText);
    
    if ($result && isset($result['result']['message_id'])) {
        // Сохраняем сообщение в базе данных
        $saved = addSupportMessage($chatId, $result['result']['message_id'], false, $messageText);
        
        if ($saved) {
            // Логируем отправку
            supportBotLog("INFO: Сообщение отправлено из админки в чат {$chatId}: {$messageText}");
            
            echo json_encode([
                'success' => true,
                'message_id' => $result['result']['message_id'],
                'message' => 'Сообщение успешно отправлено'
            ]);
        } else {
            throw new Exception('Ошибка сохранения сообщения в базе данных');
        }
    } else {
        throw new Exception('Ошибка отправки сообщения через Telegram API');
    }
    
} catch (Exception $e) {
    supportBotLog("ERROR: Ошибка отправки сообщения из админки: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Ошибка отправки сообщения: ' . $e->getMessage()
    ]);
}
?>
