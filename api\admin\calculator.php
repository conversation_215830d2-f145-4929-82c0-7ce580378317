<?php
/**
 * api/admin/calculator.php
 * Калькулятор NOWPayments в админке
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../NOWPaymentsAPI.php';

// Инициализация API
$nowPayments = new NOWPaymentsAPI(
    NOWPAYMENTS_API_KEY,
    NOWPAYMENTS_PUBLIC_KEY,
    NOWPAYMENTS_IPN_SECRET,
    NOWPAYMENTS_API_URL
);

$calculationResult = null;
$minimumAmounts = null;
$availableCurrencies = null;
$errorMessage = '';

// Обработка запросов
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'calculate':
            $amount = floatval($_POST['amount'] ?? 0);
            $fromCurrency = $_POST['from_currency'] ?? '';
            $toCurrency = $_POST['to_currency'] ?? '';
            
            if ($amount > 0 && $fromCurrency && $toCurrency) {
                $calculationResult = $nowPayments->getEstimateAmount($amount, $fromCurrency, $toCurrency);
                if (!$calculationResult) {
                    $errorMessage = 'Ошибка при расчете. Проверьте валюты и сумму.';
                }
            } else {
                $errorMessage = 'Заполните все поля для расчета.';
            }
            break;
            
        case 'get_minimums':
            $currency = $_POST['currency'] ?? '';
            if ($currency) {
                $minimumAmounts = $nowPayments->getMinWithdrawalAmount($currency);
                if (!$minimumAmounts) {
                    $errorMessage = 'Не удалось получить минимальные суммы для ' . $currency;
                }
            } else {
                $errorMessage = 'Выберите валюту для получения минимальных сумм.';
            }
            break;
            
        case 'get_all_currencies':
            $availableCurrencies = $nowPayments->getAvailableCurrencies();
            if (!$availableCurrencies) {
                $errorMessage = 'Не удалось получить список валют.';
            }
            break;

        case 'calculate_fees':
            $amount = floatval($_POST['fee_amount'] ?? 0);
            $currency = $_POST['fee_currency'] ?? '';

            if ($amount > 0 && $currency) {
                // Получаем детальную информацию о комиссиях
                $feeEstimate = $nowPayments->getWithdrawalFeeEstimate($currency, $amount);
                $minAmount = $nowPayments->getMinWithdrawalAmount($currency);

                // Получаем курс валюты к USD для дополнительных расчетов
                $usdEstimate = $nowPayments->getEstimateAmount($amount, $currency, 'usd');

                $feeCalculation = [
                    'input_amount' => $amount,
                    'currency' => $currency,
                    'fee_estimate' => $feeEstimate,
                    'min_amount' => $minAmount,
                    'usd_equivalent' => $usdEstimate
                ];
            } else {
                $errorMessage = 'Введите сумму и выберите валюту для расчета комиссий.';
            }
            break;
    }
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🧮 Калькулятор NOWPayments</h1>
            </div>

            <?php if ($errorMessage): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($errorMessage); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Калькулятор конвертации -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">💱 Калькулятор конвертации валют (Текущий курс)</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="action" value="calculate">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="amount" class="form-label">Сумма</label>
                                        <input type="text" class="form-control" id="amount" name="amount"
                                               placeholder="Введите сумму (например: 0.00000001)"
                                               value="<?php echo $_POST['amount'] ?? ''; ?>"
                                               autocomplete="off" spellcheck="false">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="from_currency" class="form-label">Из валюты</label>
                                        <select class="form-select" id="from_currency" name="from_currency">
                                            <option value="">Выберите валюту</option>
                                            <option value="usd" <?php echo ($_POST['from_currency'] ?? '') === 'usd' ? 'selected' : ''; ?>>USD</option>
                                            <option value="usdttrc20" <?php echo ($_POST['from_currency'] ?? '') === 'usdttrc20' ? 'selected' : ''; ?>>USDT (TRC20)</option>
                                            <option value="ton" <?php echo ($_POST['from_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>Toncoin (TON)</option>
                                            <option value="btc" <?php echo ($_POST['from_currency'] ?? '') === 'btc' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                            <option value="eth" <?php echo ($_POST['from_currency'] ?? '') === 'eth' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                            <option value="ton" <?php echo ($_POST['from_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>TON (Telegram)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="to_currency" class="form-label">В валюту</label>
                                        <select class="form-select" id="to_currency" name="to_currency">
                                            <option value="">Выберите валюту</option>
                                            <option value="usd" <?php echo ($_POST['to_currency'] ?? '') === 'usd' ? 'selected' : ''; ?>>USD</option>
                                            <option value="usdttrc20" <?php echo ($_POST['to_currency'] ?? '') === 'usdttrc20' ? 'selected' : ''; ?>>USDT (TRC20)</option>
                                            <option value="ton" <?php echo ($_POST['to_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>Toncoin (TON)</option>
                                            <option value="btc" <?php echo ($_POST['to_currency'] ?? '') === 'btc' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                            <option value="eth" <?php echo ($_POST['to_currency'] ?? '') === 'eth' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                            <option value="ton" <?php echo ($_POST['to_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>TON (Telegram)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="bi bi-calculator"></i> Рассчитать
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <?php if ($calculationResult): ?>
                                <div class="mt-4">
                                    <div class="alert alert-success">
                                        <h6><i class="bi bi-check-circle"></i> Результат расчета:</h6>
                                        <?php if (isset($calculationResult['estimated_amount'])): ?>
                                            <p class="mb-1"><strong>Сумма к получению:</strong> 
                                                <?php echo number_format($calculationResult['estimated_amount'], 8); ?> 
                                                <?php echo strtoupper($_POST['to_currency']); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($calculationResult['currency_from'])): ?>
                                            <p class="mb-1"><strong>Исходная валюта:</strong> <?php echo strtoupper($calculationResult['currency_from']); ?></p>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($calculationResult['currency_to'])): ?>
                                            <p class="mb-1"><strong>Целевая валюта:</strong> <?php echo strtoupper($calculationResult['currency_to']); ?></p>
                                        <?php endif; ?>
                                        
                                        <details class="mt-2">
                                            <summary>Подробная информация</summary>
                                            <pre class="mt-2"><?php echo json_encode($calculationResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                        </details>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Минимальные суммы для вывода -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">💰 Минимальные суммы для вывода</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="action" value="get_minimums">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="currency" class="form-label">Валюта</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="">Выберите валюту</option>
                                            <option value="usdttrc20" <?php echo ($_POST['currency'] ?? '') === 'usdttrc20' ? 'selected' : ''; ?>>USDT (TRC20)</option>
                                            <option value="ton" <?php echo ($_POST['currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>Toncoin (TON)</option>
                                            <option value="btc" <?php echo ($_POST['currency'] ?? '') === 'btc' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                            <option value="eth" <?php echo ($_POST['currency'] ?? '') === 'eth' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                            <option value="ton" <?php echo ($_POST['currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>TON (Telegram)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-info w-100">
                                            <i class="bi bi-info-circle"></i> Получить минимальные суммы
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <?php if ($minimumAmounts): ?>
                                <div class="mt-4">
                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-info-circle"></i> Минимальные суммы для <?php echo strtoupper($_POST['currency']); ?>:</h6>

                                        <?php if (is_numeric($minimumAmounts)): ?>
                                            <!-- Если получили простое число -->
                                            <div class="alert alert-success mt-3">
                                                <h5><i class="bi bi-cash-coin"></i> Минимальная сумма для вывода:</h5>
                                                <h4 class="text-success mb-0">
                                                    <?php echo number_format($minimumAmounts, 8); ?> <?php echo strtoupper($_POST['currency']); ?>
                                                </h4>
                                            </div>
                                        <?php elseif (isset($minimumAmounts['min_amount'])): ?>
                                            <!-- Если получили объект с min_amount -->
                                            <div class="alert alert-success mt-3">
                                                <h5><i class="bi bi-cash-coin"></i> Минимальная сумма для вывода:</h5>
                                                <h4 class="text-success mb-0">
                                                    <?php echo number_format($minimumAmounts['min_amount'], 8); ?> <?php echo strtoupper($_POST['currency']); ?>
                                                </h4>
                                            </div>
                                        <?php endif; ?>

                                        <details class="mt-2">
                                            <summary>Подробная информация API</summary>
                                            <pre class="mt-2"><?php echo json_encode($minimumAmounts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                        </details>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Калькулятор комиссий -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">💸 Калькулятор комиссий для вывода</h5>
                            <small class="text-muted">Узнайте точный размер всех комиссий и fee при выводе</small>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="action" value="calculate_fees">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="fee_amount" class="form-label">Сумма для вывода</label>
                                        <input type="text" class="form-control" id="fee_amount" name="fee_amount"
                                               placeholder="Например: 25"
                                               value="<?php echo $_POST['fee_amount'] ?? ''; ?>"
                                               autocomplete="off" spellcheck="false">
                                        <small class="text-muted">Введите сумму, которую хотите вывести</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="fee_currency" class="form-label">Валюта</label>
                                        <select class="form-select" id="fee_currency" name="fee_currency">
                                            <option value="">Выберите валюту</option>
                                            <option value="usdttrc20" <?php echo ($_POST['fee_currency'] ?? '') === 'usdttrc20' ? 'selected' : ''; ?>>USDT (TRC20)</option>
                                            <option value="ton" <?php echo ($_POST['fee_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>Toncoin (TON)</option>
                                            <option value="btc" <?php echo ($_POST['fee_currency'] ?? '') === 'btc' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                            <option value="eth" <?php echo ($_POST['fee_currency'] ?? '') === 'eth' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                            <option value="ton" <?php echo ($_POST['fee_currency'] ?? '') === 'ton' ? 'selected' : ''; ?>>TON (Telegram)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="bi bi-calculator2"></i> Рассчитать комиссии
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <?php if (isset($feeCalculation)): ?>
                                <div class="mt-4">
                                    <div class="alert alert-warning">
                                        <h6><i class="bi bi-exclamation-triangle"></i> Детальный расчет комиссий для <?php echo $feeCalculation['input_amount']; ?> <?php echo strtoupper($feeCalculation['currency']); ?>:</h6>

                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title text-primary">💰 Основная информация</h6>
                                                        <p class="mb-1"><strong>Запрашиваемая сумма:</strong>
                                                            <span class="text-success"><?php echo number_format($feeCalculation['input_amount'], 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?></span>
                                                        </p>

                                                        <?php if ($feeCalculation['usd_equivalent'] && isset($feeCalculation['usd_equivalent']['estimated_amount'])): ?>
                                                            <p class="mb-1"><strong>Эквивалент в USD:</strong>
                                                                <span class="text-info">$<?php echo number_format($feeCalculation['usd_equivalent']['estimated_amount'], 2); ?></span>
                                                            </p>
                                                        <?php endif; ?>

                                                        <?php if (is_numeric($feeCalculation['min_amount'])): ?>
                                                            <p class="mb-1"><strong>Минимальная сумма:</strong>
                                                                <span class="text-warning"><?php echo number_format($feeCalculation['min_amount'], 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?></span>
                                                            </p>

                                                            <?php if ($feeCalculation['input_amount'] < $feeCalculation['min_amount']): ?>
                                                                <div class="alert alert-danger mt-2 mb-0">
                                                                    <small><i class="bi bi-x-circle"></i> Сумма меньше минимальной! Увеличьте до <?php echo number_format($feeCalculation['min_amount'], 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?></small>
                                                                </div>
                                                            <?php else: ?>
                                                                <div class="alert alert-success mt-2 mb-0">
                                                                    <small><i class="bi bi-check-circle"></i> Сумма соответствует минимальным требованиям</small>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title text-danger">💸 Анализ комиссий</h6>

                                                        <?php
                                                        $feeAmount = null;
                                                        $isFallback = false;

                                                        // Проверяем разные форматы ответа API
                                                        if ($feeCalculation['fee_estimate']) {
                                                            if (isset($feeCalculation['fee_estimate']['fee'])) {
                                                                $feeAmount = $feeCalculation['fee_estimate']['fee'];
                                                            } elseif (isset($feeCalculation['fee_estimate']['estimated_fee'])) {
                                                                $feeAmount = $feeCalculation['fee_estimate']['estimated_fee'];
                                                            }

                                                            $isFallback = isset($feeCalculation['fee_estimate']['fallback']) && $feeCalculation['fee_estimate']['fallback'];
                                                        }
                                                        ?>

                                                        <?php if ($feeAmount !== null): ?>
                                                            <p class="mb-1"><strong>Комиссия NOWPayments:</strong>
                                                                <span class="text-danger"><?php echo number_format($feeAmount, 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?></span>
                                                                <?php if ($isFallback): ?>
                                                                    <span class="badge bg-warning ms-1">Примерно</span>
                                                                <?php endif; ?>
                                                            </p>

                                                            <?php
                                                            $feePercentage = ($feeAmount / $feeCalculation['input_amount']) * 100;
                                                            $amountAfterFee = $feeCalculation['input_amount'] - $feeAmount;
                                                            ?>

                                                            <p class="mb-1"><strong>Процент комиссии:</strong>
                                                                <span class="text-warning"><?php echo number_format($feePercentage, 2); ?>%</span>
                                                            </p>

                                                            <p class="mb-1"><strong>К получению:</strong>
                                                                <span class="text-success"><?php echo number_format($amountAfterFee, 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?></span>
                                                            </p>

                                                            <?php if ($isFallback): ?>
                                                                <div class="alert alert-warning mt-2 mb-0">
                                                                    <small><i class="bi bi-exclamation-triangle"></i> <?php echo $feeCalculation['fee_estimate']['note'] ?? 'Примерная комиссия (API недоступен)'; ?></small>
                                                                </div>
                                                            <?php else: ?>
                                                                <div class="alert alert-info mt-2 mb-0">
                                                                    <small><i class="bi bi-info-circle"></i> Комиссия списывается с суммы получателя</small>
                                                                </div>
                                                            <?php endif; ?>

                                                            <?php if (isset($feeCalculation['fee_estimate']['fixed_fee']) && isset($feeCalculation['fee_estimate']['percent_fee'])): ?>
                                                                <div class="mt-2">
                                                                    <small class="text-muted">
                                                                        Структура комиссии: <?php echo number_format($feeCalculation['fee_estimate']['fixed_fee'], 8); ?> <?php echo strtoupper($feeCalculation['currency']); ?> (фиксированная) + <?php echo $feeCalculation['fee_estimate']['percent_fee']; ?>% (процентная)
                                                                    </small>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <div class="alert alert-warning mb-0">
                                                                <small><i class="bi bi-exclamation-triangle"></i> Не удалось получить комиссию от API. Попробуйте позже или обратитесь к администратору.</small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mt-3">
                                            <details>
                                                <summary>Подробная техническая информация</summary>
                                                <div class="row mt-2">
                                                    <div class="col-md-4">
                                                        <h6>Fee Estimate:</h6>
                                                        <pre class="small"><?php echo json_encode($feeCalculation['fee_estimate'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <h6>Min Amount:</h6>
                                                        <pre class="small"><?php echo json_encode($feeCalculation['min_amount'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <h6>USD Equivalent:</h6>
                                                        <pre class="small"><?php echo json_encode($feeCalculation['usd_equivalent'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                                    </div>
                                                </div>
                                            </details>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Список всех валют -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🪙 Доступные валюты</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="action" value="get_all_currencies">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-list"></i> Получить список всех валют
                                </button>
                            </form>

                            <?php if ($availableCurrencies): ?>
                                <div class="mt-4">
                                    <div class="alert alert-success">
                                        <h6><i class="bi bi-list"></i> Доступные валюты:</h6>
                                        <?php if (isset($availableCurrencies['currencies']) && is_array($availableCurrencies['currencies'])): ?>
                                            <div class="row">
                                                <?php foreach ($availableCurrencies['currencies'] as $currency): ?>
                                                    <div class="col-md-3 mb-2">
                                                        <span class="badge bg-primary"><?php echo strtoupper($currency); ?></span>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php else: ?>
                                            <pre><?php echo json_encode($availableCurrencies, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Документация API -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">📚 Документация NOWPayments API</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>🔗 Полезные ссылки:</h6>
                                    <ul class="list-unstyled">
                                        <li><a href="https://documenter.getpostman.com/view/7907941/2s93JusNJt#39b6869b-fd6f-453b-b271-ac1d0db7cac0" target="_blank" class="btn btn-outline-primary btn-sm mb-2">
                                            <i class="bi bi-link-45deg"></i> Минимальные суммы для вывода
                                        </a></li>
                                        <li><a href="https://documenter.getpostman.com/view/7907941/2s93JusNJt#4461aa48-10ca-4032-803c-ed460b19fec0" target="_blank" class="btn btn-outline-primary btn-sm mb-2">
                                            <i class="bi bi-calculator"></i> Калькулятор цены
                                        </a></li>
                                        <li><a href="https://documenter.getpostman.com/view/7907941/2s93JusNJt" target="_blank" class="btn btn-outline-primary btn-sm mb-2">
                                            <i class="bi bi-book"></i> Полная документация API
                                        </a></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>⚙️ Настройки API:</h6>
                                    <p><strong>API URL:</strong> <?php echo NOWPAYMENTS_API_URL; ?></p>
                                    <p><strong>API Key:</strong> <?php echo substr(NOWPAYMENTS_API_KEY, 0, 8) . '...'; ?></p>
                                    <p><strong>Public Key:</strong> <?php echo substr(NOWPAYMENTS_PUBLIC_KEY, 0, 8) . '...'; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const feeAmountInput = document.getElementById('fee_amount');

    // Разрешаем только цифры, точку и запятую
    amountInput.addEventListener('input', function(e) {
        let value = e.target.value;

        // Заменяем запятую на точку
        value = value.replace(',', '.');

        // Удаляем все символы кроме цифр и точки
        value = value.replace(/[^0-9.]/g, '');

        // Оставляем только одну точку
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }

        e.target.value = value;
    });

    // Обработка вставки из буфера обмена
    amountInput.addEventListener('paste', function(e) {
        e.preventDefault();

        // Получаем данные из буфера обмена
        const paste = (e.clipboardData || window.clipboardData).getData('text');

        // Очищаем и форматируем вставленный текст
        let cleanValue = paste.replace(',', '.').replace(/[^0-9.]/g, '');

        // Оставляем только одну точку
        const parts = cleanValue.split('.');
        if (parts.length > 2) {
            cleanValue = parts[0] + '.' + parts.slice(1).join('');
        }

        // Вставляем очищенное значение
        e.target.value = cleanValue;

        // Запускаем событие input для дополнительной обработки
        e.target.dispatchEvent(new Event('input', { bubbles: true }));
    });

    // Фокус на поле при загрузке страницы
    amountInput.focus();

    // Выделяем весь текст при фокусе
    amountInput.addEventListener('focus', function() {
        this.select();
    });

    // Применяем те же функции к полю расчета комиссий
    if (feeAmountInput) {
        // Разрешаем только цифры, точку и запятую
        feeAmountInput.addEventListener('input', function(e) {
            let value = e.target.value;

            // Заменяем запятую на точку
            value = value.replace(',', '.');

            // Удаляем все символы кроме цифр и точки
            value = value.replace(/[^0-9.]/g, '');

            // Оставляем только одну точку
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }

            e.target.value = value;
        });

        // Обработка вставки из буфера обмена
        feeAmountInput.addEventListener('paste', function(e) {
            e.preventDefault();

            // Получаем данные из буфера обмена
            const paste = (e.clipboardData || window.clipboardData).getData('text');

            // Очищаем и форматируем вставленный текст
            let cleanValue = paste.replace(',', '.').replace(/[^0-9.]/g, '');

            // Оставляем только одну точку
            const parts = cleanValue.split('.');
            if (parts.length > 2) {
                cleanValue = parts[0] + '.' + parts.slice(1).join('');
            }

            // Вставляем очищенное значение
            e.target.value = cleanValue;

            // Запускаем событие input для дополнительной обработки
            e.target.dispatchEvent(new Event('input', { bubbles: true }));
        });

        // Выделяем весь текст при фокусе
        feeAmountInput.addEventListener('focus', function() {
            this.select();
        });
    }
});
</script>

<?php include 'templates/footer.php'; ?>
