<?php
/**
 * Тестирование обработки ошибок в системе выплат
 */

require_once 'config.php';
require_once 'FeeCalculator.php';

echo "❌ ТЕСТИРОВАНИЕ ОБРАБОТКИ ОШИБОК ВЫПЛАТ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые сценарии ошибок
$errorScenarios = [
    [
        'name' => 'Недостаточный баланс',
        'user_balance' => 500,
        'withdrawal_amount' => 1000,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_error' => 'insufficient_balance'
    ],
    [
        'name' => 'Сумма меньше минимума',
        'user_balance' => 10000,
        'withdrawal_amount' => 100,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_error' => 'amount_too_low'
    ],
    [
        'name' => 'Неверный адрес ETH',
        'user_balance' => 10000,
        'withdrawal_amount' => 1000,
        'currency' => 'eth',
        'address' => 'invalid_eth_address',
        'expected_error' => 'invalid_address'
    ],
    [
        'name' => 'Неверный адрес BTC',
        'user_balance' => 10000,
        'withdrawal_amount' => 2500,
        'currency' => 'btc',
        'address' => 'invalid_btc_address',
        'expected_error' => 'invalid_address'
    ],
    [
        'name' => 'Пустой адрес',
        'user_balance' => 10000,
        'withdrawal_amount' => 1000,
        'currency' => 'eth',
        'address' => '',
        'expected_error' => 'empty_address'
    ],
    [
        'name' => 'Неподдерживаемая валюта',
        'user_balance' => 10000,
        'withdrawal_amount' => 1000,
        'currency' => 'invalid_currency',
        'address' => '******************************************',
        'expected_error' => 'unsupported_currency'
    ],
    [
        'name' => 'Нулевая сумма',
        'user_balance' => 10000,
        'withdrawal_amount' => 0,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_error' => 'zero_amount'
    ],
    [
        'name' => 'Отрицательная сумма',
        'user_balance' => 10000,
        'withdrawal_amount' => -500,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_error' => 'negative_amount'
    ],
    [
        'name' => 'Комиссия больше суммы',
        'user_balance' => 10000,
        'withdrawal_amount' => 50, // Очень маленькая сумма
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_error' => 'fee_exceeds_amount'
    ]
];

echo "🎯 ТЕСТ 1: ПРОВЕРКА ВАЛИДАЦИИ ВХОДНЫХ ДАННЫХ\n";
echo str_repeat("-", 40) . "\n\n";

$feeCalculator = FeeCalculator::getInstance();
$passedTests = 0;
$totalTests = count($errorScenarios);

foreach ($errorScenarios as $i => $scenario) {
    echo "📊 Тест " . ($i + 1) . ": {$scenario['name']}\n";
    echo "   💰 Баланс: {$scenario['user_balance']} монет\n";
    echo "   💸 Сумма: {$scenario['withdrawal_amount']} монет\n";
    echo "   🪙 Валюта: {$scenario['currency']}\n";
    echo "   📍 Адрес: " . (empty($scenario['address']) ? '[пустой]' : substr($scenario['address'], 0, 20) . '...') . "\n";
    
    // Тестируем валидацию
    $validationResult = testWithdrawalValidation($scenario, $feeCalculator);
    
    echo "   🔍 Результат: " . ($validationResult['success'] ? 'УСПЕХ' : 'ОШИБКА') . "\n";
    
    if (!$validationResult['success']) {
        echo "   ❌ Ошибка: {$validationResult['error']}\n";
        echo "   🏷️ Код ошибки: {$validationResult['error_code']}\n";
        
        // Проверяем соответствие ожидаемой ошибке
        $expectedError = $scenario['expected_error'];
        $actualError = $validationResult['error_code'];
        
        if ($actualError === $expectedError) {
            echo "   ✅ Ошибка соответствует ожиданиям\n";
            $passedTests++;
        } else {
            echo "   ⚠️ Ошибка НЕ соответствует ожиданиям!\n";
            echo "   📊 Ожидалось: {$expectedError}\n";
            echo "   📊 Получено: {$actualError}\n";
        }
    } else {
        echo "   ⚠️ Ожидалась ошибка, но валидация прошла успешно!\n";
    }
    
    echo "\n";
}

echo "📈 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ОШИБОК:\n";
echo "📊 Всего тестов: {$totalTests}\n";
echo "✅ Прошло успешно: {$passedTests}\n";
echo "❌ Провалилось: " . ($totalTests - $passedTests) . "\n";
echo "📈 Процент успеха: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

echo "🎯 ТЕСТ 2: ПРОВЕРКА ОБРАБОТКИ API ОШИБОК\n";
echo str_repeat("-", 40) . "\n\n";

// Тестируем различные API ошибки
$apiErrorScenarios = [
    [
        'name' => 'Недоступность API NOWPayments',
        'error_type' => 'api_unavailable',
        'http_code' => 503,
        'response' => null
    ],
    [
        'name' => 'Неверный API ключ',
        'error_type' => 'invalid_api_key',
        'http_code' => 401,
        'response' => ['error' => 'Unauthorized']
    ],
    [
        'name' => 'Превышен лимит запросов',
        'error_type' => 'rate_limit',
        'http_code' => 429,
        'response' => ['error' => 'Too Many Requests']
    ],
    [
        'name' => 'Недостаточно средств у провайдера',
        'error_type' => 'insufficient_provider_balance',
        'http_code' => 400,
        'response' => ['error' => 'Insufficient balance']
    ],
    [
        'name' => 'Валюта временно недоступна',
        'error_type' => 'currency_unavailable',
        'http_code' => 400,
        'response' => ['error' => 'Currency not available']
    ]
];

foreach ($apiErrorScenarios as $i => $scenario) {
    echo "📊 API Тест " . ($i + 1) . ": {$scenario['name']}\n";
    
    $errorHandling = testAPIErrorHandling($scenario);
    
    echo "   🔍 Обработка ошибки: " . ($errorHandling['handled'] ? 'ДА' : 'НЕТ') . "\n";
    echo "   📝 Сообщение пользователю: {$errorHandling['user_message']}\n";
    echo "   🔧 Действие системы: {$errorHandling['system_action']}\n";
    
    if ($errorHandling['handled']) {
        echo "   ✅ Ошибка обработана корректно\n";
    } else {
        echo "   ❌ Ошибка обработана некорректно\n";
    }
    
    echo "\n";
}

echo "🎯 ТЕСТ 3: ПРОВЕРКА ВОССТАНОВЛЕНИЯ ПОСЛЕ ОШИБОК\n";
echo str_repeat("-", 40) . "\n\n";

// Тестируем сценарии восстановления
$recoveryScenarios = [
    [
        'name' => 'Повторная попытка после временной ошибки',
        'initial_error' => 'api_timeout',
        'retry_success' => true
    ],
    [
        'name' => 'Автоматическая смена валюты при недоступности',
        'initial_error' => 'currency_unavailable',
        'fallback_currency' => 'eth'
    ],
    [
        'name' => 'Возврат средств при критической ошибке',
        'initial_error' => 'critical_failure',
        'refund_required' => true
    ]
];

foreach ($recoveryScenarios as $i => $scenario) {
    echo "📊 Восстановление " . ($i + 1) . ": {$scenario['name']}\n";
    
    $recoveryResult = testErrorRecovery($scenario);
    
    echo "   🔄 Восстановление: " . ($recoveryResult['success'] ? 'УСПЕШНО' : 'НЕУДАЧНО') . "\n";
    echo "   📝 Действие: {$recoveryResult['action']}\n";
    
    if (isset($scenario['refund_required']) && $scenario['refund_required']) {
        echo "   💰 Возврат средств: " . ($recoveryResult['refunded'] ? 'ДА' : 'НЕТ') . "\n";
    }
    
    echo "\n";
}

echo "🎉 ТЕСТИРОВАНИЕ ОШИБОК ЗАВЕРШЕНО!\n";

/**
 * Тестирует валидацию данных для выплаты
 */
function testWithdrawalValidation($scenario, $feeCalculator) {
    try {
        // Проверка суммы
        if ($scenario['withdrawal_amount'] <= 0) {
            return [
                'success' => false,
                'error' => 'Некорректная сумма',
                'error_code' => $scenario['withdrawal_amount'] === 0 ? 'zero_amount' : 'negative_amount'
            ];
        }
        
        // Проверка баланса
        if ($scenario['withdrawal_amount'] > $scenario['user_balance']) {
            return [
                'success' => false,
                'error' => 'Недостаточно средств',
                'error_code' => 'insufficient_balance'
            ];
        }
        
        // Проверка адреса
        if (empty($scenario['address'])) {
            return [
                'success' => false,
                'error' => 'Не указан адрес',
                'error_code' => 'empty_address'
            ];
        }
        
        // Проверка валюты
        $supportedCurrencies = ['eth', 'btc', 'ton', 'usdttrc20'];
        if (!in_array($scenario['currency'], $supportedCurrencies)) {
            return [
                'success' => false,
                'error' => 'Неподдерживаемая валюта',
                'error_code' => 'unsupported_currency'
            ];
        }
        
        // Валидация адреса
        if (!validateCryptoAddress($scenario['address'], $scenario['currency'])) {
            return [
                'success' => false,
                'error' => 'Неверный формат адреса',
                'error_code' => 'invalid_address'
            ];
        }
        
        // Проверка через калькулятор комиссий
        $calculation = $feeCalculator->calculateWithdrawalAmount(
            $scenario['withdrawal_amount'], 
            $scenario['currency']
        );
        
        if (!$calculation['success']) {
            return [
                'success' => false,
                'error' => $calculation['error'],
                'error_code' => $calculation['error_code'] ?? 'calculation_error'
            ];
        }
        
        return ['success' => true, 'calculation' => $calculation];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Системная ошибка: ' . $e->getMessage(),
            'error_code' => 'system_error'
        ];
    }
}

/**
 * Тестирует обработку API ошибок
 */
function testAPIErrorHandling($scenario) {
    $errorType = $scenario['error_type'];
    
    switch ($errorType) {
        case 'api_unavailable':
            return [
                'handled' => true,
                'user_message' => 'Сервис временно недоступен. Попробуйте позже.',
                'system_action' => 'Повторить запрос через 5 минут'
            ];
            
        case 'invalid_api_key':
            return [
                'handled' => true,
                'user_message' => 'Техническая ошибка. Обратитесь в поддержку.',
                'system_action' => 'Уведомить администратора'
            ];
            
        case 'rate_limit':
            return [
                'handled' => true,
                'user_message' => 'Слишком много запросов. Подождите немного.',
                'system_action' => 'Задержка на 60 секунд'
            ];
            
        case 'insufficient_provider_balance':
            return [
                'handled' => true,
                'user_message' => 'Выплаты временно приостановлены.',
                'system_action' => 'Переключиться на резервный провайдер'
            ];
            
        case 'currency_unavailable':
            return [
                'handled' => true,
                'user_message' => 'Валюта временно недоступна. Выберите другую.',
                'system_action' => 'Предложить альтернативную валюту'
            ];
            
        default:
            return [
                'handled' => false,
                'user_message' => 'Неизвестная ошибка',
                'system_action' => 'Логировать ошибку'
            ];
    }
}

/**
 * Тестирует восстановление после ошибок
 */
function testErrorRecovery($scenario) {
    $errorType = $scenario['initial_error'];
    
    switch ($errorType) {
        case 'api_timeout':
            return [
                'success' => $scenario['retry_success'] ?? false,
                'action' => 'Повторная попытка через 30 секунд',
                'refunded' => false
            ];
            
        case 'currency_unavailable':
            return [
                'success' => true,
                'action' => 'Переключение на ' . ($scenario['fallback_currency'] ?? 'ETH'),
                'refunded' => false
            ];
            
        case 'critical_failure':
            return [
                'success' => true,
                'action' => 'Возврат средств на баланс пользователя',
                'refunded' => true
            ];
            
        default:
            return [
                'success' => false,
                'action' => 'Ошибка не может быть восстановлена',
                'refunded' => false
            ];
    }
}

/**
 * Валидация адресов криптовалют
 */
function validateCryptoAddress($address, $currency) {
    switch ($currency) {
        case 'eth':
            return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
        case 'btc':
            return preg_match('/^(1|3|bc1)[a-zA-Z0-9]{25,58}$/', $address);
        case 'ton':
            return preg_match('/^[UEkuek]Q[A-Za-z0-9_-]{42,46}$/', $address) ||
                   preg_match('/^[0-9a-fA-F]{64}$/', $address);
        case 'usdttrc20':
            return preg_match('/^T[a-zA-Z0-9]{33}$/', $address);
        default:
            return false;
    }
}
?>
