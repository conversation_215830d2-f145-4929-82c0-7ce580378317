<?php
/**
 * api/admin/text_settings.php
 * Страница редактирования текстов бота
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Путь к файлу с текстами бота
$textsFile = __DIR__ . '/../../bot/bot_texts.json';

// Загружаем текущие тексты
$currentTexts = [];
if (file_exists($textsFile)) {
    $content = file_get_contents($textsFile);
    $currentTexts = json_decode($content, true) ?: [];
}

// Обработка сохранения текстов
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_texts') {
    if (isset($_POST['texts_content'])) {
        $newContent = $_POST['texts_content'];
        
        // Пробуем декодировать JSON для проверки
        $decoded = json_decode($newContent, true);
        if ($decoded === null) {
            $message = 'Ошибка: Неверный формат JSON';
        } else {
            // Форматируем JSON для читаемости
            $formattedContent = json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
            // Сохраняем файл
            if (file_put_contents($textsFile, $formattedContent) !== false) {
                $message = 'Тексты успешно сохранены';
                $currentTexts = $decoded;
            } else {
                $message = 'Ошибка: Не удалось сохранить файл';
            }
        }
    } else {
        $message = 'Ошибка: Отсутствуют данные для сохранения';
    }
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Редактирование текстов бота</h1>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert <?php echo strpos($message, 'Ошибка:') === 0 ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form method="post">
                        <input type="hidden" name="action" value="save_texts">
                        
                        <div class="mb-3">
                            <label for="texts_content" class="form-label">Содержимое текстов бота (JSON)</label>
                            <textarea class="form-control font-monospace" id="texts_content" name="texts_content" rows="25" style="font-size: 14px;"><?php 
                                echo htmlspecialchars(json_encode($currentTexts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); 
                            ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Сохранить тексты</button>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>