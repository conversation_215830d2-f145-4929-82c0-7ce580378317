<?php
/**
 * api/registerReferral.php
 * API эндпоинт для регистрации реферала при переходе по реферальной ссылке.
 */

// Включаем логирование для этого скрипта
ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json'); // Устанавливаем правильный заголовок

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in registerReferral.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in registerReferral.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in registerReferral.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }

// Проверка, что функция getUserDetails действительно загружена
if (!function_exists('getUserDetails')) {
     http_response_code(500);
     error_log('FATAL: function getUserDetails is not defined after including db_mock.php in registerReferral.php');
     echo json_encode(['error'=>'Ошибка сервера: GUD_NF']); // GUD Not Found
     exit;
}
// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData']) || !isset($input['referrerId']) || empty($input['referrerId'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Ошибка запроса: Некорректные или отсутствующие данные']);
    exit;
}
$initData = $input['initData'];
$referrerId = intval($input['referrerId']);

error_log("registerReferral INFO: Получен initData (длина: " . strlen($initData) . ") и referrerId: $referrerId"); // Логируем получение данных

// 2. Валидация initData (ВРЕМЕННО УПРОЩЕННАЯ ДЛЯ ОТЛАДКИ)
$validatedData = validateTelegramInitData($initData);

// Проверяем результат валидации
if ($validatedData === false) {
    // ВРЕМЕННОЕ РЕШЕНИЕ: Пытаемся извлечь данные пользователя из initData напрямую
    error_log("registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную");

    $initDataParts = [];
    parse_str($initData, $initDataParts);

    if (isset($initDataParts['user'])) {
        $userArray = json_decode($initDataParts['user'], true);
        if ($userArray !== null && isset($userArray['id'])) {
            $validatedData = ['user' => $userArray];
            error_log("registerReferral INFO: Упрощенная валидация прошла для пользователя " . $userArray['id']);
        } else {
            error_log("registerReferral ERROR: Не удалось извлечь данные пользователя из initData");
            http_response_code(403);
            echo json_encode(['error' => 'Ошибка: Неверные данные пользователя']);
            exit;
        }
    } else {
        error_log("registerReferral ERROR: Отсутствуют данные пользователя в initData");
        http_response_code(403);
        echo json_encode(['error' => 'Ошибка: Отсутствуют данные пользователя']);
        exit;
    }
}
error_log("registerReferral INFO: initData успешно валидирован для пользователя " . ($validatedData['user']['id'] ?? '???'));

// 3. Получение ID пользователя (теперь мы уверены, что $validatedData - массив с user[id])
$userId = intval($validatedData['user']['id']);

// Проверка, что пользователь не пытается стать своим собственным рефералом
if ($userId === $referrerId) {
    error_log("registerReferral WARNING: Пользователь $userId пытается стать своим собственным рефералом");
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Вы не можете стать своим собственным рефералом']);
    exit;
}

// 4. Загрузка данных из "базы"
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("registerReferral ERROR: loadUserData вернул не массив. Проблема с файлом данных?");
     http_response_code(500);
     echo json_encode(['error' => 'Ошибка сервера: LD1']);
     exit;
}
error_log("registerReferral INFO: Данные пользователей загружены для user $userId и referrer $referrerId.");

// 5. Проверка существования реферера
if (!isset($userData[$referrerId])) {
    error_log("registerReferral ERROR: Реферер $referrerId не найден в базе данных");
    http_response_code(404); // Not Found
    echo json_encode(['error' => 'Указанный реферер не найден']);
    exit;
}

// 5.1. Проверяем, не заблокирован ли пользователь (если он уже существует)
if (isset($userData[$userId]) && isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("registerReferral WARNING: Заблокированный пользователь $userId пытается зарегистрировать реферала");
    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// 6. Получение деталей пользователя и установка реферера
try {
    $isNewUser = !isset($userData[$userId]);
    $shouldAddReferral = false;

    if ($isNewUser) {
        // Создаем нового пользователя с указанным реферером и данными из Telegram
        error_log("registerReferral INFO: Создаем нового пользователя $userId с реферером $referrerId");
        $userDetails = getUserDetails($userId, $userData, $referrerId, $validatedData['user']);
        $shouldAddReferral = true;
    } else {
        // Пользователь уже существует, проверяем реферера
        if (!empty($userData[$userId]['referrer_id'])) {
            // У пользователя уже есть реферер
            if ($userData[$userId]['referrer_id'] == $referrerId) {
                error_log("registerReferral INFO: Пользователь $userId уже имеет этого реферера $referrerId");
                http_response_code(200);
                echo json_encode([
                    'success' => false,
                    'message' => 'Вы уже зарегистрированы с этим реферером'
                ]);
                exit;
            } else {
                error_log("registerReferral INFO: Пользователь $userId уже имеет другого реферера " . $userData[$userId]['referrer_id']);
                http_response_code(200);
                echo json_encode([
                    'success' => false,
                    'message' => 'У вас уже есть реферер'
                ]);
                exit;
            }
        } else {
            // У пользователя нет реферера, устанавливаем
            error_log("registerReferral INFO: Устанавливаем реферера $referrerId для существующего пользователя $userId");
            $userData[$userId]['referrer_id'] = $referrerId;

            // Обновляем данные из Telegram
            if (isset($validatedData['user']['username'])) {
                $userData[$userId]['username'] = $validatedData['user']['username'];
            }
            if (isset($validatedData['user']['first_name'])) {
                $userData[$userId]['first_name'] = $validatedData['user']['first_name'];
            }
            if (isset($validatedData['user']['last_name'])) {
                $userData[$userId]['last_name'] = $validatedData['user']['last_name'];
            }
            $userData[$userId]['last_activity'] = time();

            $shouldAddReferral = true;
        }
    }

    // Добавляем пользователя в список рефералов реферера (только если нужно)
    if ($shouldAddReferral) {
        // Убеждаемся что у реферера есть массивы
        if (!isset($userData[$referrerId]['referrals'])) {
            $userData[$referrerId]['referrals'] = [];
        }
        if (!isset($userData[$referrerId]['referrals_count'])) {
            $userData[$referrerId]['referrals_count'] = 0;
        }

        // Добавляем пользователя в список рефералов реферера (если его там нет)
        if (!in_array($userId, $userData[$referrerId]['referrals'])) {
            $userData[$referrerId]['referrals'][] = $userId;
            $userData[$referrerId]['referrals_count'] = count($userData[$referrerId]['referrals']);

            error_log("registerReferral INFO: Пользователь $userId добавлен в список рефералов $referrerId");
        } else {
            error_log("registerReferral INFO: Пользователь $userId уже в списке рефералов $referrerId");
        }
    }

} catch (Exception $e) {
    error_log("registerReferral ERROR: Исключение при установке реферера для user $userId: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: GUD_EX']);
    exit;
}

// 7. Сохранение данных
if (!saveUserData($userData)) {
    error_log("registerReferral ERROR: Не удалось сохранить данные после установки реферера для пользователя ID: " . $userId);
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: SUD2']);
    exit; // Прерываем, если сохранение критично
}
error_log("registerReferral INFO: Данные пользователя $userId и реферера $referrerId сохранены.");

// 8. Успешный ответ
http_response_code(200); // OK
echo json_encode([
    'success' => true,
    'message' => 'Вы успешно зарегистрированы как реферал'
]);
error_log("registerReferral INFO: Успешный ответ отправлен для user $userId и referrer $referrerId.");
exit;
?>
