<?php
/**
 * api/admin/ad_limits_control.php
 * API для управления лимитами рекламы
 */

// Включаем отображение ошибок для отладки
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Проверка авторизации администратора
session_start();

// Отладочная информация
error_log("ad_limits_control.php: Проверка сессии. Session ID: " . session_id());
error_log("ad_limits_control.php: admin_logged_in = " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set'));
error_log("ad_limits_control.php: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    error_log("ad_limits_control.php: Авторизация не пройдена");
    http_response_code(401);
    echo json_encode([
        'error' => 'Требуется авторизация администратора',
        'success' => false,
        'debug' => [
            'session_id' => session_id(),
            'session_exists' => isset($_SESSION['admin_logged_in']),
            'session_value' => $_SESSION['admin_logged_in'] ?? null,
            'all_session_data' => $_SESSION
        ]
    ]);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$settingsFile = __DIR__ . '/ad_limits_settings.json';

// Функция для загрузки настроек
function loadAdLimitsSettings($file) {
    if (!file_exists($file)) {
        $defaultSettings = [
            'disable_all_ad_limits' => false,
            'last_updated' => null,
            'updated_by' => null
        ];
        file_put_contents($file, json_encode($defaultSettings, JSON_PRETTY_PRINT));
        return $defaultSettings;
    }
    
    $content = file_get_contents($file);
    $settings = json_decode($content, true);
    
    if (!$settings) {
        throw new Exception('Ошибка чтения файла настроек');
    }
    
    return $settings;
}

// Функция для сохранения настроек
function saveAdLimitsSettings($file, $settings) {
    $settings['last_updated'] = date('Y-m-d H:i:s');
    $settings['updated_by'] = $_SESSION['admin_username'] ?? 'admin';
    
    $result = file_put_contents($file, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    if ($result === false) {
        throw new Exception('Ошибка сохранения настроек');
    }
    
    return $settings;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Получение текущих настроек
        $settings = loadAdLimitsSettings($settingsFile);
        
        echo json_encode([
            'success' => true,
            'settings' => $settings,
            'timestamp' => time()
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Обновление настроек
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['disable_all_ad_limits'])) {
            throw new Exception('Отсутствует параметр disable_all_ad_limits');
        }
        
        $settings = loadAdLimitsSettings($settingsFile);
        $oldValue = $settings['disable_all_ad_limits'];
        $newValue = (bool)$input['disable_all_ad_limits'];
        
        $settings['disable_all_ad_limits'] = $newValue;
        $settings = saveAdLimitsSettings($settingsFile, $settings);
        
        // Логируем изменение
        $action = $newValue ? 'ОТКЛЮЧЕНЫ' : 'ВКЛЮЧЕНЫ';
        error_log("AD_LIMITS_CONTROL: Лимиты рекламы {$action} администратором. Было: " . ($oldValue ? 'отключены' : 'включены') . ", стало: " . ($newValue ? 'отключены' : 'включены'));
        
        echo json_encode([
            'success' => true,
            'settings' => $settings,
            'message' => $newValue ? 'Все лимиты рекламы отключены' : 'Лимиты рекламы включены',
            'changed' => $oldValue !== $newValue
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Метод не поддерживается', 'success' => false]);
    }
    
} catch (Exception $e) {
    error_log("ad_limits_control.php ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'success' => false
    ]);
}
?>
