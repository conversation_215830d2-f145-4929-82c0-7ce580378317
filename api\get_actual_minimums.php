<?php
/**
 * Получение актуальных минимумов для обновления кода
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔍 ПОЛУЧЕНИЕ АКТУАЛЬНЫХ МИНИМУМОВ...\n";
echo str_repeat('=', 50) . "\n";

$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

$currencies = ['ton', 'eth', 'btc', 'usdttrc20', 'ltc', 'bch', 'xrp', 'ada', 'dot'];
$results = [];

foreach ($currencies as $currency) {
    $minAmount = $api->getMinWithdrawalAmount($currency);
    if ($minAmount !== null) {
        $results[$currency] = $minAmount;
        echo "✅ {$currency}: {$minAmount}\n";
    } else {
        echo "❌ {$currency}: не удалось получить\n";
    }
    usleep(200000); // 200ms пауза между запросами
}

echo "\n💱 ПОЛУЧЕНИЕ КУРСОВ...\n";
echo str_repeat('-', 30) . "\n";

$rates = [];
foreach (array_keys($results) as $currency) {
    try {
        if ($currency === 'usdttrc20') {
            $rate = 1.0;
        } else {
            $estimate = $api->getEstimateAmount(1, $currency, 'usd');
            $rate = $estimate['estimated_amount'] ?? null;
        }
        
        if ($rate) {
            $rates[$currency] = $rate;
            echo "✅ {$currency}: 1 = \${$rate}\n";
        }
    } catch (Exception $e) {
        echo "❌ {$currency}: ошибка курса\n";
    }
    usleep(200000);
}

echo "\n🧮 РАСЧЕТ МИНИМУМОВ В МОНЕТАХ...\n";
echo str_repeat('-', 40) . "\n";

$finalData = [];
foreach ($results as $currency => $apiMin) {
    $rate = $rates[$currency] ?? 1;
    
    // Минимум в USD
    $minUsd = $currency === 'usdttrc20' ? $apiMin : $apiMin * $rate;
    
    // Комиссии (примерные)
    $networkFees = [
        'ton' => 0.15,
        'eth' => 0.25,
        'btc' => 0.50,
        'usdttrc20' => 5.58,
        'ltc' => 0.50,
        'bch' => 0.30,
        'xrp' => 0.20,
        'ada' => 0.30,
        'dot' => 0.40
    ];
    
    $networkFee = $networkFees[$currency] ?? 0.30;
    
    // Формула: (минимум_API_USD + комиссия + запас) / 0.001
    $requiredUsd = $minUsd + $networkFee + 0.1; // +0.1 USD запас
    $minCoins = ceil($requiredUsd / 0.001);
    
    $finalData[$currency] = [
        'api_min' => $apiMin,
        'api_min_usd' => $minUsd,
        'network_fee' => $networkFee,
        'min_coins' => $minCoins,
        'rate' => $rate
    ];
    
    echo "💰 {$currency}:\n";
    echo "   API минимум: {$apiMin} (" . number_format($minUsd, 3) . " USD)\n";
    echo "   Комиссия: \${$networkFee}\n";
    echo "   Минимум монет: {$minCoins}\n";
    echo "   Курс: 1 = \$" . number_format($rate, 6) . "\n\n";
}

echo str_repeat('=', 50) . "\n";
echo "📋 ИТОГОВЫЕ ДАННЫЕ ДЛЯ КОДА:\n\n";

$names = [
    'ton' => 'TON (Telegram)',
    'eth' => 'Ethereum (ETH)',
    'btc' => 'Bitcoin (BTC)',
    'usdttrc20' => 'USDT (TRC20)',
    'ltc' => 'Litecoin (LTC)',
    'bch' => 'Bitcoin Cash (BCH)',
    'xrp' => 'Ripple (XRP)',
    'ada' => 'Cardano (ADA)',
    'dot' => 'Polkadot (DOT)'
];

echo "// Актуальные данные от " . date('Y-m-d H:i:s') . "\n";
echo "const currencyData = {\n";

foreach ($finalData as $currency => $data) {
    $name = $names[$currency] ?? strtoupper($currency);
    
    echo "  {$currency}: {\n";
    echo "    name: '{$name}',\n";
    echo "    minCoins: {$data['min_coins']},\n";
    echo "    networkFee: {$data['network_fee']},\n";
    echo "    status: 'good'\n";
    echo "  },\n";
}

echo "};\n\n";

echo "// Для PHP файлов:\n";
echo "\$currencyData = [\n";

foreach ($finalData as $currency => $data) {
    $name = $names[$currency] ?? strtoupper($currency);
    
    echo "    '{$currency}' => [\n";
    echo "        'name' => '{$name}',\n";
    echo "        'minCoins' => {$data['min_coins']},\n";
    echo "        'networkFee' => {$data['network_fee']},\n";
    echo "        'status' => 'good'\n";
    echo "    ],\n";
}

echo "];\n\n";

echo "🎯 РЕКОМЕНДУЕМЫЕ ПОЛЬЗОВАТЕЛЬСКИЕ МИНИМУМЫ:\n";
echo str_repeat('-', 40) . "\n";

// Рекомендуемые минимумы для пользователей (более низкие)
$userFriendlyMins = [
    'ton' => 500,    // $0.50
    'eth' => 300,    // $0.30
    'btc' => 600,    // $0.60
    'usdttrc20' => 9000, // $9.00
    'ltc' => 600,    // $0.60
    'bch' => 400,    // $0.40
    'xrp' => 300,    // $0.30
    'ada' => 400,    // $0.40
    'dot' => 500     // $0.50
];

echo "// Пользовательские минимумы (более низкие для удобства):\n";
foreach ($userFriendlyMins as $currency => $minCoins) {
    $usd = $minCoins * 0.001;
    echo "{$currency}: {$minCoins} монет (\${$usd})\n";
}

?>
