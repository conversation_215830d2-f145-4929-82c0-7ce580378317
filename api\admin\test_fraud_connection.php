<!DOCTYPE html>
<html>
<head>
    <title>Тест подключения к Fraud Detection API</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Тест подключения к Fraud Detection API</h1>
    
    <div id="results"></div>
    
    <script>
    async function testFraudAPI() {
        const results = document.getElementById('results');
        results.innerHTML = '<p>Тестирование...</p>';
        
        try {
            // Тест 1: Проверка доступности тестового API
            console.log('Тест 1: Проверка тестового API...');
            const testResponse = await fetch('../test_fraud_api.php');
            const testData = await testResponse.json();
            
            results.innerHTML += '<h3>✅ Тест 1: Тестовый API работает</h3>';
            results.innerHTML += '<pre>' + JSON.stringify(testData, null, 2) + '</pre>';
            
            // Тест 2: Проверка основного fraud-detection API
            console.log('Тест 2: Проверка основного fraud-detection API...');
            const fraudResponse = await fetch('../fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'get_admin_stats'
                })
            });
            
            if (fraudResponse.ok) {
                const fraudData = await fraudResponse.json();
                results.innerHTML += '<h3>✅ Тест 2: Fraud Detection API работает</h3>';
                results.innerHTML += '<pre>' + JSON.stringify(fraudData, null, 2) + '</pre>';
            } else {
                results.innerHTML += '<h3>❌ Тест 2: Ошибка ' + fraudResponse.status + '</h3>';
                const errorText = await fraudResponse.text();
                results.innerHTML += '<pre>' + errorText + '</pre>';
            }
            
        } catch (error) {
            results.innerHTML += '<h3>❌ Ошибка тестирования</h3>';
            results.innerHTML += '<pre>' + error.message + '</pre>';
        }
    }
    
    // Запускаем тест при загрузке страницы
    testFraudAPI();
    </script>
</body>
</html>
