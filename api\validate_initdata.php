<?php
// api/validate_initdata.php (ИСПРАВЛЕННАЯ ВЕРСИЯ)
require_once __DIR__ . '/config.php';

function validateTelegramInitData(string $initData): array|false {
    if (empty($initData)) { error_log("validateTelegramInitData: initData пустая."); return false; }
    $initDataParts = [];
    parse_str($initData, $initDataParts);

    if (!isset($initDataParts['hash']) || !isset($initDataParts['auth_date']) || !isset($initDataParts['user'])) {
        error_log("validateTelegramInitData: Отсутствует hash, auth_date или user."); return false;
    }

    // *** ИСПРАВЛЕНИЕ НАЧАЛО ***
    // Сохраняем ОРИГИНАЛЬНУЮ строку user перед декодированием
    $originalUserString = $initDataParts['user'];
    $userArray = json_decode($originalUserString, true);
    // *** ИСПРАВЛЕНИЕ КОНЕЦ ***

    if ($userArray === null || !isset($userArray['id'])) {
        error_log("validateTelegramInitData: Невалидный JSON user или отсутствует user ID."); return false;
    }

    $authDate = intval($initDataParts['auth_date']);
    if (time() - $authDate > 3600 * 24) { // 24 часа
        error_log("validateTelegramInitData: auth_date старый (но разрешен).");
    }

    $receivedHash = $initDataParts['hash'];
    unset($initDataParts['hash']);
    ksort($initDataParts);

    $dataCheckString = '';
    foreach ($initDataParts as $key => $value) {
        // *** ИСПРАВЛЕНИЕ НАЧАЛО ***
        // Используем ОРИГИНАЛЬНУЮ строку user для проверки
        if ($key === 'user') {
            $value = $originalUserString;
        }
        // *** ИСПРАВЛЕНИЕ КОНЕЦ ***
        $dataCheckString .= $key . '=' . $value . "\n";
    }
    $dataCheckString = rtrim($dataCheckString, "\n");

    try {
        $secretKey = hash_hmac('sha256', TELEGRAM_BOT_TOKEN, 'WebAppData', true);
        $calculatedHash = hash_hmac('sha256', $dataCheckString, $secretKey);
    } catch (\Exception $e) {
        error_log("validateTelegramInitData: Ошибка HMAC: " . $e->getMessage()); return false;
    }

    if (hash_equals($calculatedHash, $receivedHash)) {
        // Возвращаем данные с декодированным user для удобства
        $initDataParts['user'] = $userArray;
        return $initDataParts;
    } else {
        error_log("validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!");

        // Временный fallback удален - используется только строгая валидация

        // Для отладки:
        // error_log("DataCheckString: " . $dataCheckString);
        // error_log("Received Hash: " . $receivedHash);
        // error_log("Calculated Hash: " . $calculatedHash);
        return false;
    }
}
?>