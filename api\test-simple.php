<?php
/**
 * api/test-simple.php
 * Простейший тест для выявления проблемы
 */

// Включаем отображение ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    echo json_encode([
        'test' => 'success',
        'php_version' => phpversion(),
        'timestamp' => time(),
        'message' => 'Простой тест работает'
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
