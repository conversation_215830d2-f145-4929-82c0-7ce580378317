<?php
/**
 * api/admin/get_current_config.php
 * API для получения текущих значений из config.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    echo json_encode([
        'success' => false,
        'error' => 'Не авторизован'
    ]);
    exit;
}

try {
    // Подключаем config.php заново чтобы получить актуальные значения
    $configFile = __DIR__ . '/../config.php';
    
    // Очищаем кэш опкодов для получения актуальных значений
    if (function_exists('opcache_invalidate')) {
        opcache_invalidate($configFile, true);
    }
    
    // Включаем config.php
    include $configFile;
    
    // Собираем все настройки
    $config = [
        // Награды за рекламу
        'ad_reward_native_banner' => defined('AD_REWARD_NATIVE_BANNER') ? AD_REWARD_NATIVE_BANNER : 10,
        'ad_reward_interstitial' => defined('AD_REWARD_INTERSTITIAL') ? AD_REWARD_INTERSTITIAL : 10,
        'ad_reward_rewarded_video' => defined('AD_REWARD_REWARDED_VIDEO') ? AD_REWARD_REWARDED_VIDEO : 1,
        
        // Настройки приложения
        'referral_bonus_percent' => defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT * 100 : 10,
        'min_withdrawal_amount' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 100,
        'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100,
        'conversion_rate' => defined('CONVERSION_RATE') ? CONVERSION_RATE : 0.001,
        'show_fees_to_user' => defined('SHOW_FEES_TO_USER') ? SHOW_FEES_TO_USER : true,
        
        // NOWPayments настройки
        'nowpayments_email' => defined('NOWPAYMENTS_EMAIL') ? NOWPAYMENTS_EMAIL : '',
        'nowpayments_api_key' => defined('NOWPAYMENTS_API_KEY') ? '***' : '',
    ];
    
    echo json_encode([
        'success' => true,
        'config' => $config,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log('Ошибка в get_current_config.php: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения конфигурации: ' . $e->getMessage()
    ]);
}
?>
