<?php
/**
 * Быстрое исправление webhook
 */

require_once __DIR__ . '/support_config.php';

echo "<h1>🔧 Исправление webhook бота поддержки</h1>";

// 1. Удаляем старый webhook
echo "<h2>1. Удаление старого webhook...</h2>";
$deleteResult = supportTelegramRequest('deleteWebhook', ['drop_pending_updates' => true]);
if ($deleteResult) {
    echo "<p style='color: green;'>✅ Старый webhook удален</p>";
} else {
    echo "<p style='color: red;'>❌ Ошибка удаления старого webhook</p>";
}

sleep(2);

// 2. Устанавливаем новый webhook
echo "<h2>2. Установка нового webhook...</h2>";
$setupResult = supportTelegramRequest('setWebhook', [
    'url' => SUPPORT_WEBHOOK_URL,
    'drop_pending_updates' => true
]);

if ($setupResult) {
    echo "<p style='color: green;'>✅ Новый webhook установлен!</p>";
    echo "<p><strong>URL:</strong> " . SUPPORT_WEBHOOK_URL . "</p>";
} else {
    echo "<p style='color: red;'>❌ Ошибка установки нового webhook</p>";
}

// 3. Проверяем статус
echo "<h2>3. Проверка статуса...</h2>";
$webhookInfo = supportTelegramRequest('getWebhookInfo', []);
if ($webhookInfo) {
    $webhook = $webhookInfo['result'];
    echo "<p><strong>URL:</strong> " . ($webhook['url'] ?: 'НЕ УСТАНОВЛЕН') . "</p>";
    echo "<p><strong>Ожидающих обновлений:</strong> " . $webhook['pending_update_count'] . "</p>";
    
    if (isset($webhook['last_error_date'])) {
        echo "<p style='color: red;'><strong>Последняя ошибка:</strong> " . date('d.m.Y H:i:s', $webhook['last_error_date']) . "</p>";
        echo "<p style='color: red;'><strong>Сообщение ошибки:</strong> " . $webhook['last_error_message'] . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Ошибок нет</p>";
    }
}

// 4. Тестируем доступность webhook
echo "<h2>4. Тест доступности webhook...</h2>";
$webhookUrl = SUPPORT_WEBHOOK_URL;
$testUrl = str_replace('support_webhook.php', 'test_webhook_access.php', $webhookUrl);

echo "<p>Тестовый URL: <a href='{$testUrl}' target='_blank'>{$testUrl}</a></p>";

// 5. Очищаем логи
echo "<h2>5. Очистка логов...</h2>";
file_put_contents(SUPPORT_LOG_FILE, '');
echo "<p style='color: green;'>✅ Логи очищены</p>";

// 6. Тестовая запись в лог
supportBotLog("WEBHOOK FIXED: " . date('Y-m-d H:i:s'));
echo "<p style='color: green;'>✅ Тестовая запись в лог добавлена</p>";

echo "<hr>";
echo "<h2>🎯 Следующие шаги:</h2>";
echo "<ol>";
echo "<li>Напишите боту @uniqpaid_support_bot команду <code>/start</code></li>";
echo "<li>Отправьте любое сообщение</li>";
echo "<li>Проверьте лог: <a href='support_bot.log' target='_blank'>support_bot.log</a></li>";
echo "<li>Проверьте админку: <a href='support.php' target='_blank'>support.php</a></li>";
echo "<li>Если не работает, проверьте тестовый лог: <a href='webhook_test.log' target='_blank'>webhook_test.log</a></li>";
echo "</ol>";

echo "<p><strong>Время исправления:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
