<?php
/**
 * Тест для диагностики проблемы с выводом TON
 */

require_once 'config.php';
require_once 'functions.php';
require_once 'security.php';
require_once 'NOWPaymentsAPI.php';
require_once 'FeeCalculator.php';

echo "=== ДИАГНОСТИКА ПРОБЛЕМЫ С TON ===\n\n";

// Тестовые данные пользователя (как в скриншоте)
$testUserId = 5880288830;
$testCoinsAmount = 3010; // Баланс пользователя
$testWithdrawalAmount = 1700; // Попытка вывести (примерно для 0.6759 TON)
$testCurrency = 'ton';
$testAddress = 'UQBTRSl1c49V6x_nl9flCoCJq';

echo "1. ПРОВЕРКА КОНФИГУРАЦИИ:\n";
echo "CONVERSION_RATE: " . CONVERSION_RATE . "\n";
echo "SHOW_FEES_TO_USER: " . (SHOW_FEES_TO_USER ? 'true' : 'false') . "\n";
echo "MIN_WITHDRAWAL_AMOUNT: " . MIN_WITHDRAWAL_AMOUNT . "\n";
echo "MIN_BALANCE_FOR_WITHDRAWAL: " . MIN_BALANCE_FOR_WITHDRAWAL . "\n\n";

echo "2. ТЕСТОВЫЕ ДАННЫЕ:\n";
echo "User ID: {$testUserId}\n";
echo "Баланс пользователя: {$testCoinsAmount} монет\n";
echo "Попытка вывести: {$testWithdrawalAmount} монет\n";
echo "Валюта: {$testCurrency}\n";
echo "Адрес: {$testAddress}\n\n";

// Загружаем данные пользователя
echo "3. ПРОВЕРКА ДАННЫХ ПОЛЬЗОВАТЕЛЯ:\n";
$userData = loadUserData();
if (!$userData) {
    echo "❌ Ошибка загрузки данных пользователей\n";
    exit;
}

if (!isset($userData[$testUserId])) {
    echo "❌ Пользователь {$testUserId} не найден\n";
    exit;
}

$userBalance = $userData[$testUserId]['balance'];
echo "Реальный баланс пользователя: {$userBalance} монет\n";

if (isset($userData[$testUserId]['total_earned'])) {
    echo "Всего заработано: {$userData[$testUserId]['total_earned']} монет\n";
}

$totalWithdrawn = 0;
if (isset($userData[$testUserId]['withdrawals']) && is_array($userData[$testUserId]['withdrawals'])) {
    foreach ($userData[$testUserId]['withdrawals'] as $withdrawal) {
        if (isset($withdrawal['coins_amount'])) {
            $totalWithdrawn += $withdrawal['coins_amount'];
        }
    }
}
echo "Всего выведено: {$totalWithdrawn} монет\n\n";

// Проверяем функцию verifyBalance
echo "4. ПРОВЕРКА ФУНКЦИИ verifyBalance:\n";
$balanceCheck = verifyBalance($testUserId, $testWithdrawalAmount, $userData);
echo "verifyBalance({$testUserId}, {$testWithdrawalAmount}): " . ($balanceCheck ? 'PASS' : 'FAIL') . "\n";

if (!$balanceCheck) {
    echo "❌ Функция verifyBalance вернула false\n";
    
    // Проверим детали
    if ($userBalance < $testWithdrawalAmount) {
        echo "   Причина: Недостаточно средств на балансе ({$userBalance} < {$testWithdrawalAmount})\n";
    }
    
    if (!isset($userData[$testUserId]['total_earned'])) {
        echo "   Поле total_earned отсутствует\n";
    } else {
        $totalEarned = $userData[$testUserId]['total_earned'];
        $availableForWithdrawal = $totalEarned - $totalWithdrawn;
        echo "   Доступно для вывода: {$availableForWithdrawal} монет\n";
        
        if ($testWithdrawalAmount > $availableForWithdrawal) {
            echo "   Причина: Попытка вывести больше чем заработано ({$testWithdrawalAmount} > {$availableForWithdrawal})\n";
        }
    }
}
echo "\n";

// Проверяем расчет суммы
echo "5. ПРОВЕРКА РАСЧЕТА СУММЫ:\n";
try {
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Конвертируем монеты в USD
    $usdAmount = $testWithdrawalAmount * CONVERSION_RATE;
    echo "USD сумма: {$usdAmount}\n";
    
    // Проверяем комиссию
    if (SHOW_FEES_TO_USER) {
        echo "Комиссии включены\n";
        
        // Получаем данные о валюте
        $currencyDataResponse = file_get_contents('http://argun-defolt.loc/api/getCurrencyData.php');
        $currencyApiData = json_decode($currencyDataResponse, true);
        
        if ($currencyApiData && isset($currencyApiData['currencies'][$testCurrency])) {
            $currencyInfo = $currencyApiData['currencies'][$testCurrency];
            echo "Комиссия TON: {$currencyInfo['networkFee']} USD\n";
            
            $usdAmountAfterFee = $usdAmount - $currencyInfo['networkFee'];
            echo "USD после комиссии: {$usdAmountAfterFee}\n";
            
            if ($usdAmountAfterFee <= 0) {
                echo "❌ Комиссия больше суммы!\n";
            }
        } else {
            echo "❌ Не удалось получить данные о валюте\n";
        }
    } else {
        echo "Комиссии отключены\n";
        $usdAmountAfterFee = $usdAmount;
    }
    
    // Получаем оценку
    echo "Получение оценки от NOWPayments...\n";
    $estimate = $api->getEstimateAmount($usdAmountAfterFee, 'usd', $testCurrency);
    
    if (isset($estimate['estimated_amount'])) {
        $cryptoAmount = $estimate['estimated_amount'];
        echo "Расчетная сумма TON: {$cryptoAmount}\n";
    } else {
        echo "❌ Не удалось получить оценку: " . json_encode($estimate) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при расчете: " . $e->getMessage() . "\n";
}
echo "\n";

// Проверяем минимумы
echo "6. ПРОВЕРКА МИНИМУМОВ:\n";
try {
    $minAmount = $api->getMinWithdrawalAmount($testCurrency);
    echo "Минимум NOWPayments для TON: " . ($minAmount ?: 'не получен') . "\n";
    
    // Проверяем через FeeCalculator
    $feeCalculator = FeeCalculator::getInstance();
    $minCoinsData = $feeCalculator->getMinimumCoinsForCurrency($testCurrency);
    
    if ($minCoinsData) {
        echo "Минимум монет с учетом комиссии: {$minCoinsData['min_coins_with_fees']}\n";
        echo "Минимум монет (старый расчет): {$minCoinsData['min_coins_naive']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка при проверке минимумов: " . $e->getMessage() . "\n";
}
echo "\n";

// Проверяем единый калькулятор
echo "7. ПРОВЕРКА ЕДИНОГО КАЛЬКУЛЯТОРА:\n";
try {
    $feeCalculator = FeeCalculator::getInstance();
    $result = $feeCalculator->calculateWithdrawalAmount($testWithdrawalAmount, $testCurrency);
    
    echo "Результат единого калькулятора:\n";
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
} catch (Exception $e) {
    echo "❌ Ошибка единого калькулятора: " . $e->getMessage() . "\n";
}
echo "\n";

echo "=== КОНЕЦ ДИАГНОСТИКИ ===\n";
?>
