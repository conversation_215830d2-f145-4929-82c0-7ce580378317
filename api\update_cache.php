<?php
/**
 * Скрипт для принудительного обновления кэша курсов, комиссий и минимумов
 * Вызывается по cron или принудительно при ошибках выплат
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

function force_update_currency_cache() {
    error_log("CACHE_UPDATE: 🚀 Запущено принудительное обновление кэша...");

    $api = new NOWPaymentsAPI(
        NOWPAYMENTS_API_KEY,
        NOWPAYMENTS_PUBLIC_KEY,
        NOWPAYMENTS_IPN_SECRET,

        NOWPAYMENTS_API_URL
    );

    // Валюты для обновления
    $currencies = ['eth', 'btc', 'ton', 'usdttrc20'];
    $updatedData = [];

    foreach ($currencies as $currency) {
        try {
            // 1. Получаем курс USD
            $estimate = $api->getEstimateAmount(1, 'usd', $currency);
            $rate_usd = isset($estimate['estimated_amount']) ? (1 / $estimate['estimated_amount']) : 0;

            // 2. Получаем минимальную сумму для выплаты
            $min_amount = $api->getMinWithdrawalAmount($currency);

            // 3. Получаем комиссию сети (в USD)
            // Для этого сначала получаем комиссию в крипте, потом конвертируем в USD
            $fee_estimate = $api->getWithdrawalFeeEstimate($currency, $min_amount * 2); // Берем с запасом
            $fee_crypto = $fee_estimate['fee'] ?? 0;
            $network_fee_usd = $fee_crypto * $rate_usd;

            if ($rate_usd > 0 && $min_amount > 0) {
                $updatedData[$currency] = [
                    'rate_usd' => $rate_usd,
                    'min_amount' => $min_amount,
                    'network_fee' => $network_fee_usd, // Комиссия в USD
                    'updated_at' => time()
                ];
                error_log("CACHE_UPDATE: ✅ {$currency}: rate={$rate_usd}, min={$min_amount}, fee_usd={$network_fee_usd}");
            } else {
                error_log("CACHE_UPDATE: ⚠️ Не удалось получить полные данные для {$currency}");
            }
        } catch (Exception $e) {
            error_log("CACHE_UPDATE: ❌ Ошибка при обновлении {$currency}: " . $e->getMessage());
        }
        sleep(1); // Задержка между запросами к API
    }

    if (!empty($updatedData)) {
        // Используем функции из currency_cache.php для атомарного обновления
        require_once __DIR__ . '/currency_cache.php';
        $cache = loadCurrencyCache();

        foreach ($updatedData as $currency => $data) {
            if (isset($cache['currencies'][$currency])) {
                $cache['currencies'][$currency]['rate_usd'] = $data['rate_usd'];
                $cache['currencies'][$currency]['min_amount'] = $data['min_amount'];
                $cache['currencies'][$currency]['network_fee'] = $data['network_fee'];
                $cache['currencies'][$currency]['last_updated'] = $data['updated_at'];
                $cache['currencies'][$currency]['status'] = 'available'; // Считаем доступной если обновилась
            }
        }

        $cache['last_updated'] = time();

        if (saveCurrencyCache($cache)) {
            error_log("CACHE_UPDATE: ✅ Кэш успешно обновлен в файле: {$cacheFile}");
            return ['success' => true, 'message' => 'Кэш обновлен', 'updated_currencies' => array_keys($updatedData)];
        } else {
            error_log("CACHE_UPDATE: ❌ Не удалось записать кэш в файл: {$cacheFile}");
            return ['success' => false, 'error' => 'Не удалось записать кэш'];
        }
    }

    return ['success' => false, 'error' => 'Нет данных для обновления'];
}

// Если скрипт вызван напрямую, выполняем обновление
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    header('Content-Type: application/json');
    $result = force_update_currency_cache();
    echo json_encode($result);
}

?>