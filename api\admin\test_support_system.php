<?php
/**
 * api/admin/test_support_system.php
 * Тестирование системы поддержки
 */

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы поддержки</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h1 class="h3 mb-0">
                            <i class="bi bi-clipboard-check"></i>
                            Тест системы поддержки
                        </h1>
                    </div>
                    <div class="card-body">
                        
                        <!-- Проверка файлов -->
                        <h5><i class="bi bi-file-earmark-check"></i> Проверка файлов</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <?php
                                $files = [
                                    'support_config.php' => 'Конфигурация бота',
                                    'support_data.php' => 'Функции работы с данными',
                                    'support_webhook.php' => 'Webhook обработчик',
                                    'support.php' => 'Главная страница поддержки',
                                    'support_chat.php' => 'Страница чата',
                                    'send_support_message.php' => 'API отправки сообщений',
                                    'webhook_manager.php' => 'Управление webhook',
                                    'support_chats.json' => 'Данные чатов',
                                    'support_messages.json' => 'Данные сообщений'
                                ];
                                
                                foreach ($files as $file => $description) {
                                    $exists = file_exists(__DIR__ . '/' . $file);
                                    $icon = $exists ? '<i class="bi bi-check-circle text-success"></i>' : '<i class="bi bi-x-circle text-danger"></i>';
                                    $status = $exists ? 'Существует' : 'Отсутствует';
                                    echo "<tr><td>{$icon}</td><td>{$file}</td><td>{$description}</td><td>{$status}</td></tr>";
                                }
                                ?>
                            </table>
                        </div>
                        
                        <!-- Проверка конфигурации -->
                        <h5><i class="bi bi-gear"></i> Конфигурация</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <tr><td><strong>Токен бота:</strong></td><td><?php echo substr(SUPPORT_BOT_TOKEN, 0, 10) . '...'; ?></td></tr>
                                <tr><td><strong>Username бота:</strong></td><td>@<?php echo SUPPORT_BOT_USERNAME; ?></td></tr>
                                <tr><td><strong>Webhook URL:</strong></td><td><?php echo SUPPORT_WEBHOOK_URL; ?></td></tr>
                                <tr><td><strong>Файл чатов:</strong></td><td><?php echo SUPPORT_CHATS_FILE; ?></td></tr>
                                <tr><td><strong>Файл сообщений:</strong></td><td><?php echo SUPPORT_MESSAGES_FILE; ?></td></tr>
                                <tr><td><strong>Лог файл:</strong></td><td><?php echo SUPPORT_LOG_FILE; ?></td></tr>
                            </table>
                        </div>
                        
                        <!-- Проверка данных -->
                        <h5><i class="bi bi-database"></i> Данные</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">Чаты поддержки</div>
                                    <div class="card-body">
                                        <?php
                                        $chats = getSupportChats();
                                        echo "<p><strong>Всего чатов:</strong> " . count($chats) . "</p>";
                                        
                                        if (!empty($chats)) {
                                            echo "<small>Последние чаты:</small><ul class='list-unstyled'>";
                                            $recentChats = array_slice($chats, -3, 3, true);
                                            foreach ($recentChats as $chatId => $chat) {
                                                $name = $chat['first_name'] . ($chat['last_name'] ? ' ' . $chat['last_name'] : '');
                                                $lang = $chat['language'] ?? 'en';
                                                $flag = $lang === 'ru' ? '🇷🇺' : '🇺🇸';
                                                echo "<li>{$flag} {$name} (ID: {$chat['user_id']})</li>";
                                            }
                                            echo "</ul>";
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">Сообщения</div>
                                    <div class="card-body">
                                        <?php
                                        $messages = getSupportMessages();
                                        echo "<p><strong>Всего сообщений:</strong> " . count($messages) . "</p>";
                                        
                                        if (!empty($messages)) {
                                            $userMessages = array_filter($messages, function($msg) { return $msg['from_user']; });
                                            $supportMessages = array_filter($messages, function($msg) { return !$msg['from_user']; });
                                            echo "<p><small>От пользователей: " . count($userMessages) . "</small></p>";
                                            echo "<p><small>От поддержки: " . count($supportMessages) . "</small></p>";
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Проверка API -->
                        <h5><i class="bi bi-cloud"></i> Проверка Telegram API</h5>
                        <div class="mb-4">
                            <button class="btn btn-primary" onclick="testTelegramAPI()">
                                <i class="bi bi-play"></i> Тестировать API
                            </button>
                            <div id="api-result" class="mt-3"></div>
                        </div>
                        
                        <!-- Тестовые функции -->
                        <h5><i class="bi bi-tools"></i> Тестовые функции</h5>
                        <div class="mb-4">
                            <button class="btn btn-success" onclick="createTestChat()">
                                <i class="bi bi-plus"></i> Создать тестовый чат
                            </button>
                            <button class="btn btn-info" onclick="testLanguageDetection()">
                                <i class="bi bi-translate"></i> Тест определения языка
                            </button>
                            <div id="test-result" class="mt-3"></div>
                        </div>
                        
                        <!-- Ссылки -->
                        <h5><i class="bi bi-link"></i> Полезные ссылки</h5>
                        <div class="list-group">
                            <a href="support.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-headset"></i> Главная страница поддержки
                            </a>
                            <a href="setup_support_webhook.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-gear"></i> Настройка webhook
                            </a>
                            <a href="https://t.me/<?php echo SUPPORT_BOT_USERNAME; ?>" target="_blank" class="list-group-item list-group-item-action">
                                <i class="bi bi-telegram"></i> Открыть бота в Telegram
                            </a>
                            <a href="SUPPORT_README.md" target="_blank" class="list-group-item list-group-item-action">
                                <i class="bi bi-file-text"></i> Документация
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testTelegramAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Тестирование...';
            
            fetch('webhook_manager.php?action=check')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> API работает корректно!<br>
                                <small>Бот: ${data.bot_info.first_name} (@${data.bot_info.username})</small>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-x-circle"></i> Ошибка API: ${data.error}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-x-circle"></i> Ошибка сети: ${error.message}
                        </div>
                    `;
                });
        }
        
        function createTestChat() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="alert alert-info">Создание тестового чата...</div>';
            
            // Имитируем создание тестового чата
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> Тестовый чат создан!<br>
                        <small>Для полного тестирования отправьте сообщение боту @<?php echo SUPPORT_BOT_USERNAME; ?></small>
                    </div>
                `;
            }, 1000);
        }
        
        function testLanguageDetection() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>Тест определения языка:</strong><br>
                    🇷🇺 Русский: ru, be, kk, ky, uz, uk<br>
                    🇺🇸 Английский: все остальные языки<br>
                    <small>Язык определяется автоматически по настройкам Telegram пользователя</small>
                </div>
            `;
        }
    </script>
</body>
</html>
