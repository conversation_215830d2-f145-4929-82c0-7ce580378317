<?php
// Ультра-надежный логгер с отладкой

$debug_log_file = __DIR__ . '/../database/richadds_DEBUG.log';

function write_debug($message) {
    global $debug_log_file;
    file_put_contents($debug_log_file, date('Y-m-d H:i:s') . ' - ' . $message . "\n", FILE_APPEND);
}

write_debug("--- Request Started ---");

header('Content-Type: application/json');

function send_response($success, $message = '') {
    write_debug("Sending response: success='{$success}', message='{$message}'");
    echo json_encode(['success' => $success, 'message' => $message]);
    exit;
}

$log_file_path = __DIR__ . '/../database/richadds_success_log.csv';
write_debug("Log file path is: {$log_file_path}");

$database_dir = __DIR__ . '/../database';
if (!is_writable($database_dir)) {
    write_debug("FATAL: Database directory is not writable.");
    send_response(false, 'Server error: Cannot write to database directory.');
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    write_debug("ERROR: Invalid or empty JSON received.");
    send_response(false, 'Invalid JSON');
}

write_debug("Received data: " . json_encode($input));

$user_id = $input['telegramUserId'] ?? 'N/A';
$ip_address = $_SERVER['REMOTE_ADDR'] ?? 'N/A';
$device_type = $input['deviceType'] ?? 'N/A';
$platform = $input['platform'] ?? 'N/A';
$screen_resolution = $input['screenResolution'] ?? 'N/A';
$device_manufacturer = $input['deviceManufacturer'] ?? 'N/A';
$time = gmdate('Y-m-d H:i:s'); // Полный формат даты и времени

$data_to_write = [
    $user_id, $ip_address, $device_type, $platform, 
    $screen_resolution, $device_manufacturer, $time
];

write_debug("Prepared data for CSV: " . implode(",", $data_to_write));

clearstatcache(); // Очищаем кэш состояния файлов

$file_handle = fopen($log_file_path, 'a');
if (!$file_handle) {
    write_debug("FATAL: fopen() failed for {$log_file_path}");
    send_response(false, 'Server error: Could not open log file.');
}

write_debug("fopen() successful.");

if (filesize($log_file_path) == 0) {
    write_debug("File is empty. Writing headers.");
    fputcsv($file_handle, ["Telegram User ID", "IP Address", "Device Type", "Platform", "Screen Resolution", "Device Manufacturer", "Time (UTC)"]);
}

$bytes_written = fputcsv($file_handle, $data_to_write);

if ($bytes_written === false) {
    write_debug("FATAL: fputcsv() failed.");
    fclose($file_handle);
    send_response(false, 'Server error: Could not write to log file.');
}

write_debug("fputcsv() successful. Bytes written: {$bytes_written}");

fclose($file_handle);
write_debug("fclose() successful.");

// Сортируем CSV файл по времени после добавления новой записи
sortCsvByTime($log_file_path);

send_response(true, 'Log successful');

/**
 * Сортирует CSV файл по времени (последняя колонка)
 */
function sortCsvByTime($filePath) {
    if (!file_exists($filePath)) {
        write_debug("sortCsvByTime: File does not exist: $filePath");
        return false;
    }

    try {
        write_debug("sortCsvByTime: Starting sort for $filePath");

        // Читаем все данные
        $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        if (count($lines) <= 1) {
            write_debug("sortCsvByTime: No data to sort (only header or empty)");
            return true; // Нет данных для сортировки или только заголовок
        }

        // Отделяем заголовок от данных
        $header = array_shift($lines);
        $data = array_map('str_getcsv', $lines);

        // Сортируем данные по времени (последняя колонка - индекс 6)
        usort($data, function($a, $b) {
            if (count($a) < 7 || count($b) < 7) {
                return 0; // Пропускаем некорректные строки
            }

            $timeA = strtotime($a[6]); // Время в последней колонке (полный формат YYYY-MM-DD HH:MM:SS)
            $timeB = strtotime($b[6]);

            // Сортируем по возрастанию (старые записи сверху, новые снизу)
            return $timeA - $timeB;
        });

        // Записываем отсортированные данные обратно в файл
        $handle = fopen($filePath, 'w');
        if (!$handle) {
            write_debug("sortCsvByTime: Cannot open file for writing");
            return false;
        }

        // Записываем заголовок
        fputcsv($handle, str_getcsv($header));

        // Записываем отсортированные данные
        foreach ($data as $row) {
            fputcsv($handle, $row);
        }

        fclose($handle);

        write_debug("sortCsvByTime: CSV file sorted successfully, " . count($data) . " records");
        return true;

    } catch (Exception $e) {
        write_debug("sortCsvByTime: Error - " . $e->getMessage());
        return false;
    }
}